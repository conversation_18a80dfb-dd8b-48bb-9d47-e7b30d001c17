# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.8] - 2025-08-11

### Fixed
- **WooCommerce Subscriptions Compatibility** - Resolved critical issue where payment gateways disappeared when WooCommerce Subscriptions plugin was activated
- **Admin Settings JavaScript Loading** - Fixed JavaScript loading issues for admin payment settings interface when WooCommerce Subscriptions was active
- **Class Initialization Timing** - Fixed initialization order conflicts between Monoova plugin and WooCommerce Subscriptions

### Improved
- **Subscription Integration Reliability** - Enhanced initialization timing to prevent conflicts with subscription plugin
- **Error Handling** - Added safety checks for gateway registration and initialization
- **Debug Capabilities** - Added debug logging for subscription integration troubleshooting
- **Plugin Compatibility** - Properly declared compatibility with WooCommerce Subscriptions

### Technical Changes
- Delayed subscription class initialization until after gateways are properly registered
- Moved admin settings handler and admin class initialization to proper hooks
- Added class existence checks before gateway registration
- Enhanced webhook handler initialization timing
- Added WooCommerce Subscriptions compatibility declaration

## [1.0.7] - 2025-08-05
- Added PayTo payment gateway with mandate management
- Implemented WooCommerce Subscriptions integration
- Enhanced admin dashboard with PayTo statistics
- Added express checkout option for Card or PayTo payment methods in the admin settings
- Improved error handling and user experience
- Added comprehensive webhook handling for all gateways

## [1.0.6] - 2025-07-22
### Fixed
- Fixed webhook handler for NPPReceivePayment to detect this event by the only field `ReconciliationRuleReference` (remove the checking field `SourceBankAccount` as we don't always have source account info available) 

## [1.0.5] - 2025-07-21

### Added
- **Webhook Configuration Management** - New comprehensive webhook management system in General Settings
  - **Separate Sandbox and Live Mode Configuration** - Independent webhook management for testing and production environments
  - **Auto-Status Checking** - Automatic verification of webhook subscription status for all required events
  - **One-Click Webhook Registration** - Simple "Connect" button to register all necessary webhook subscriptions
  - **Real-time Status Indicators** - Visual status chips showing "Active" or "Inactive" webhook states
  - **API Credentials Validation** - Smart validation that prevents webhook operations when API credentials are missing
  - **Comprehensive Event Coverage** - Supports all required webhook events:
    - **Card API Events**: CreditCardPaymentNotification, CreditCardRefundNotification
    - **PayTo API Events**: PaymentAgreementNotification, PaymentInstructionNotification
    - **Payments API Events**: NppReturn, NppPaymentStatus, PayToReceivePayment, NPPReceivePayment, InboundDirectCredit, NPPCreditRejections, InboundDirectCreditRejections

### Improved
- **Enhanced Admin Experience** - Streamlined webhook setup process with clear visual feedback
- **Automatic Configuration Detection** - System automatically detects and validates API configuration before allowing webhook operations
- **Error Prevention** - Prevents invalid webhook subscription attempts when credentials are incomplete
- **Real-time Updates** - Webhook status updates automatically when API credentials are configured
- **User Guidance** - Clear error messages guide administrators to complete required configuration steps

### Fixed
- **Webhook Subscription Error Handling** - Improved error handling for existing webhook subscriptions (HTTP 400 responses)
- **API Response Processing** - Fixed array operator error in API response handling for webhook subscription conflicts
- **Mode-Specific API Calls** - Webhook operations now use correct API credentials and URLs based on selected mode (Sandbox/Live)

---

## [1.0.4] - 2025-07-16

### Fixed
- Fixed empty billing data when creating card token with a guest user
- Updated PayID instructions text from "Scan to pay" to "Pay" for better user experience

---

## [1.0.3] - 2025-07-14

### Added
- Integrated Card SDK and PayID instruction section directly into WooCommerce checkout flow
- Improved PayID flow with enhanced reusability - generate and reuse 1 PayID for up to 10 orders instead of creating new PayID for each order
- Enhanced webhook processing for PayID overpayment/underpayment scenarios via NPPRejection handling
- **Auto-navigation to Order Received page** - After successful PayID payment confirmation, users are automatically redirected to the Order Received page after a 5-second countdown

### Fixed
- Fixed scroll broken UI in the Express checkout dialog
- Fixed webhook handler for PayID payments in case of overpayment/underpayment - now properly handled by NPPRejection instead of processing as successful payment
- Improved payment flow stability and user experience during checkout

### Changed
- Removed redirect page dependency for Card SDK integration
- Moved PayID instructions from Order Received page to checkout page for better user experience
- Updated payment processing logic to handle edge cases more reliably
- **Enhanced payment success flow** - Payment confirmation now includes visual countdown timer and automatic navigation to order details

### Improved
- Enhanced checkout experience with inline payment processing
- Better error handling and user feedback during payment flows
- Optimized PayID generation and management for better performance
- **Streamlined post-payment experience** - Users no longer need to manually navigate to order details after payment confirmation

---

## [1.0.2] - Previous Release
*Previous changelog entries would go here*

## [1.0.1] - Previous Release
*Previous changelog entries would go here*

