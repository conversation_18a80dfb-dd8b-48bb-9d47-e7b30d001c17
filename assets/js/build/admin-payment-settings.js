(()=>{"use strict";const e=window.wp.element;function t(){return t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var o in a)({}).hasOwnProperty.call(a,o)&&(e[o]=a[o])}return e},t.apply(null,arguments)}const a=window.wp.components,o=window.wp.i18n,n=(0,e.memo)((({type:t})=>{const[o,n]=(0,e.useState)(!1);return React.createElement("div",{style:{position:"relative",display:"inline-block"}},React.createElement("div",{onMouseEnter:()=>n(!0),onMouseLeave:()=>n(!1),style:{width:"13px",height:"13px",borderRadius:"50%",backgroundColor:"#D4D4D4",color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"8px",fontWeight:"bold",cursor:"help"}},"i"),o&&React.createElement(a.Popover,{position:"top right",noArrow:!1,onClose:()=>n(!1)},(()=>{switch(t){case"label":return React.createElement("div",{style:{padding:"24px 16px",width:"300px"}},React.createElement("div",{style:{fontSize:"14px",fontWeight:"500",marginBottom:"16px"}},"Customize the Field of the card details"),React.createElement("div",{style:{backgroundColor:"#FAFAFA",padding:"16px 12px"}},React.createElement("div",{style:{fontSize:"14px",fontWeight:"500",color:"#000000",marginBottom:"4px",margin:"-6px",border:"2px solid #FF4E4E",padding:"6px",borderRadius:"4px",display:"inline-block"}},"Card number"),React.createElement("div",{style:{border:"1px solid #d1d5db",borderRadius:"8px",padding:"10px",fontSize:"14px",color:"#999"}},"1234 5678 9012 3456")));case"input":return React.createElement("div",{style:{padding:"24px 16px",width:"300px"}},React.createElement("div",{style:{fontSize:"14px",fontWeight:"500",marginBottom:"16px"}},"Customize the Field of the card details"),React.createElement("div",{style:{backgroundColor:"#FAFAFA",padding:"16px 12px"}},React.createElement("div",{style:{fontSize:"14px",fontWeight:"500",color:"#000000",marginBottom:"4px"}},"Card number"),React.createElement("div",{style:{border:"2px solid #FF4E4E",borderRadius:"4px",padding:"6px",margin:"-6px"}},React.createElement("div",{style:{border:"1px solid #d1d5db",borderRadius:"8px",padding:"10px",fontSize:"14px",color:"#999"}},"1234 5678 9012 3456"))));case"button":return React.createElement("div",{style:{padding:"24px 16px",width:"300px"}},React.createElement("div",{style:{backgroundColor:"#2ab5c4",borderRadius:"10px",padding:"12px 24px",textAlign:"center",fontSize:"17px",fontWeight:"bold",color:"#000000",cursor:"pointer"}},"Pay"));default:return null}})()))})),c=(0,e.memo)((({value:e,onChange:o,error:n,...c})=>{const r=n?"has-error":"";return React.createElement("div",null,React.createElement(a.TextControl,t({},c,{value:e||"",onChange:o,className:r,style:n?{borderColor:"#d63638"}:{}})),n&&React.createElement(a.__experimentalText,{color:"#d63638",size:"12",style:{marginTop:"4px",display:"block"}},n))})),r=(0,e.memo)((({value:e,onChange:o,...n})=>React.createElement(a.TextareaControl,t({},n,{value:e||"",onChange:o})))),l=(0,e.memo)((({label:e,description:t,required:o=!1,children:n})=>React.createElement(a.BaseControl,{className:"monoova-form-field"},React.createElement(a.__experimentalVStack,{spacing:2},React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:1},React.createElement(a.__experimentalText,{weight:"500",size:"14",color:"#1e1e1e"},e),o&&React.createElement(a.__experimentalText,{color:"#d63638",size:"14"},"*")),n,t&&React.createElement(a.__experimentalText,{variant:"muted",size:"13",lineHeight:"1.4"},t))))),m=(0,e.memo)((({label:t,description:o,value:n,onChange:c,disabled:r=!1})=>{const[m,s]=(0,e.useState)(!1);return React.createElement(l,{label:t,description:o},React.createElement("div",{style:{position:"relative"}},React.createElement(a.Button,{disabled:r,onClick:()=>s(!m),style:{display:"flex",alignItems:"center",justifyContent:"space-between",gap:"8px",padding:"10px",border:"1px solid #d1d5db",borderRadius:"8px",background:"#fff",cursor:r?"not-allowed":"pointer",width:"100%",height:"45px"}},React.createElement("span",null,n||"#000000"),React.createElement("div",{style:{width:"25px",height:"25px",borderRadius:"8px",backgroundColor:n||"#000000",border:"1px solid #ddd"}})),m&&React.createElement(a.Popover,{position:"bottom left",onClose:()=>s(!1),noArrow:!1},React.createElement("div",{style:{padding:"16px"}},React.createElement(a.ColorPicker,{color:n||"#000000",onChange:c,enableAlpha:!1})))))})),s=(0,e.memo)((({mode:e,modeLabel:t,status:n,onCheckStatus:c,onSubscribe:r})=>React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.__experimentalText,{size:"14",color:"#374151"},(0,o.__)(`Configure ${t} webhook notifications to receive real-time payment and transaction status updates from Monoova.`,"monoova-payments-for-woocommerce")),n?.validationError&&React.createElement(a.Notice,{status:"warning",isDismissible:!1},React.createElement(a.__experimentalText,{size:"14",color:"#B45309"},n.validationError)),React.createElement(a.Flex,{align:"center",justify:"space-between",gap:3,style:{width:"100%"}},React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.__experimentalText,{weight:"600",size:"14"},(0,o.__)(`${t} webhook notifications`,"monoova-payments-for-woocommerce")),React.createElement("div",{className:"monoova-webhook-status-chip "+(n?.all_active?"active":"inactive")},n?.isChecking?React.createElement(a.Flex,{align:"center",gap:2},React.createElement(a.Spinner,{style:{width:"14px",height:"14px",margin:0}}),React.createElement("span",null,(0,o.__)("Checking","monoova-payments-for-woocommerce"))):n?.all_active?(0,o.__)("Active","monoova-payments-for-woocommerce"):(0,o.__)("Inactive","monoova-payments-for-woocommerce"))),React.createElement("div",null,React.createElement(a.Button,{variant:"primary",style:{justifyContent:"center"},onClick:r,isBusy:n?.isConnecting,disabled:n?.isConnecting||n?.isChecking||n?.all_active||n?.validationError},n?.isConnecting?React.createElement(a.Flex,{align:"center",gap:2},React.createElement(a.Spinner,{style:{width:"14px",height:"14px",margin:0}}),React.createElement("span",null,(0,o.__)("Connecting","monoova-payments-for-woocommerce"))):n?.all_active?(0,o.__)("Connected","monoova-payments-for-woocommerce"):(0,o.__)("Connect","monoova-payments-for-woocommerce"))))))))),i=(0,e.memo)((({settings:e,saveNotice:t,onChangeHandlers:n,setSaveNotice:r,validationErrors:m={},webhookStatus:i,onCheckWebhookStatus:_,onSubscribeToWebhooks:p})=>React.createElement(a.__experimentalVStack,{spacing:6,className:"monoova-general-settings-tab"},t&&React.createElement(a.Notice,{className:"monoova-save-notice",status:t.type,onRemove:()=>r(null),isDismissible:!0},t.message),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Basic Settings","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure basic payment gateway settings.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.enabled,onChange:e=>{n.enabled(e),e||(n.enable_card_payments(!1),n.enable_payid_payments(!1),n.enable_payto_payments(!1))}}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Enable Monoova Payments","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Enable this payment gateway to accept payments.","monoova-payments-for-woocommerce")))))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Account Settings","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure your Monoova account details.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(l,{label:(0,o.__)("Monoova mAccount Number","monoova-payments-for-woocommerce"),description:(0,o.__)("Your general Monoova mAccount number for transactions.","monoova-payments-for-woocommerce"),required:!0},React.createElement(c,{value:e.maccount_number||"",onChange:n.maccount_number,placeholder:(0,o.__)("Enter M-Account number","monoova-payments-for-woocommerce")})))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("API Credentials","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Secure API keys for connecting to Monoova services.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.__experimentalGrid,{columns:2,gap:4},React.createElement(l,{label:(0,o.__)("Test API Key","monoova-payments-for-woocommerce"),description:(0,o.__)("Get your Test API key from your Monoova dashboard.","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.test_api_key||"",onChange:n.test_api_key,type:"password",placeholder:(0,o.__)("Enter test API key","monoova-payments-for-woocommerce")})),React.createElement(l,{label:(0,o.__)("Live API Key","monoova-payments-for-woocommerce"),description:(0,o.__)("Get your Live API key from your Monoova dashboard.","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.live_api_key||"",onChange:n.live_api_key,type:"password",placeholder:(0,o.__)("Enter live API key","monoova-payments-for-woocommerce"),error:m.live_api_key})))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("API URLs (Advanced)","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Override default API URLs if needed. Leave blank to use defaults.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.__experimentalGrid,{columns:2,gap:4},React.createElement(l,{label:(0,o.__)("PayID API URL (Sandbox)","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.monoova_payments_api_url_sandbox||"https://api.m-pay.com.au",onChange:n.monoova_payments_api_url_sandbox,placeholder:"https://api.m-pay.com.au"})),React.createElement(l,{label:(0,o.__)("PayID API URL (Live)","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.monoova_payments_api_url_live||"https://api.mpay.com.au",onChange:n.monoova_payments_api_url_live,placeholder:"https://api.mpay.com.au",error:m.monoova_payments_api_url_live}))),React.createElement(a.__experimentalGrid,{columns:2,gap:4},React.createElement(l,{label:(0,o.__)("Card API URL (Sandbox)","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.monoova_card_api_url_sandbox||"https://sand-api.monoova.com",onChange:n.monoova_card_api_url_sandbox,placeholder:"https://sand-api.monoova.com"})),React.createElement(l,{label:(0,o.__)("Card API URL (Live)","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.monoova_card_api_url_live||"https://api.monoova.com",onChange:n.monoova_card_api_url_live,placeholder:"https://api.monoova.com",error:m.monoova_card_api_url_live})))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Webhook Configuration - Sandbox mode","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure SANDBOX webhook notifications for testing. You MUST subscribe to all webhook events to receive payment and transaction status notifications from Monoova.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(s,{mode:"sandbox",modeLabel:"Sandbox",status:i?.sandbox,onCheckStatus:()=>_(!0),onSubscribe:()=>p(!0)}))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Webhook Configuration - Live mode","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure LIVE webhook notifications for production. You MUST subscribe to all webhook events to receive payment and transaction status notifications from Monoova.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(s,{mode:"live",modeLabel:"Live",status:i?.live,onCheckStatus:()=>_(!1),onSubscribe:()=>p(!1)})))))),_=(0,e.memo)((({label:e,description:t,icons:o,checked:n,onChange:c,disabled:r=!1})=>React.createElement(a.PanelRow,null,React.createElement(a.Flex,{justify:"flex-start",align:"center",gap:3},React.createElement(a.CheckboxControl,{checked:n,onChange:c,disabled:r}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{size:"14",weight:"500",color:r?"#757575":"#1e1e1e"},React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:1},React.createElement("span",{style:{marginRight:"8px"}},e),o&&o.map(((e,t)=>React.createElement("img",{key:t,src:e.src,alt:e.alt,width:"24",height:"16"}))))),t&&React.createElement(a.__experimentalText,{size:"12",color:"#757575",lineHeight:"1.4"},t)))))),p=(0,e.memo)((({settings:e,saveNotice:t,onChangeHandlers:n,setSaveNotice:c})=>React.createElement(a.__experimentalVStack,{spacing:6,className:"monoova-payment-methods-tab"},t&&React.createElement(a.Notice,{status:t.type,onRemove:()=>c(null)},t.message),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Payments accepted on checkout","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Select payments available to customers at checkout. Based on their device type, location, and purchase history, your customers will only see the most relevant payment methods.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(_,{label:(0,o.__)("Credit / debit card","monoova-payments-for-woocommerce"),description:(0,o.__)("Let your customers pay with major credit and debit cards without leaving your store.","monoova-payments-for-woocommerce"),icons:[{src:`${window.monoovaPluginUrl||""}assets/images/visa.png`,alt:"Visa"},{src:`${window.monoovaPluginUrl||""}assets/images/mastercard.png`,alt:"Mastercard"}],checked:e.enable_card_payments,onChange:n.enable_card_payments}),React.createElement(_,{label:(0,o.__)("PayID / Bank Transfer","monoova-payments-for-woocommerce"),description:(0,o.__)("Allow customers to pay using PayID or direct bank transfer with real-time payment confirmation.","monoova-payments-for-woocommerce"),icons:[{src:`${window.monoovaPluginUrl||""}assets/images/payid-logo.png`,alt:"PayID"},{src:`${window.monoovaPluginUrl||""}assets/images/bank-transfer.png`,alt:"Bank Transfer"}],checked:e.enable_payid_payments,onChange:n.enable_payid_payments}),React.createElement(_,{label:(0,o.__)("PayTo","monoova-payments-for-woocommerce"),description:(0,o.__)("Customer approves a payment agreement.","monoova-payments-for-woocommerce"),icons:[{src:`${window.monoovaPluginUrl||""}assets/images/payto-logo.svg`,alt:"PayTo"}],checked:e.enable_payto_payments,onChange:n.enable_payto_payments,disabled:!e.enabled})))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Express checkouts","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Let your customers use their favorite express checkout methods.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(_,{label:(0,o.__)("Express checkout by credit / debit card","monoova-payments-for-woocommerce"),description:(0,o.__)("Allow customers to skip the checkout form with saved card payment details.","monoova-payments-for-woocommerce"),icons:[{src:`${window.monoovaPluginUrl||""}assets/images/cards.png`,alt:"Express Checkout"}],checked:e.enable_express_checkout,onChange:n.enable_express_checkout,disabled:!e.enable_card_payments}),React.createElement(_,{label:(0,o.__)("Express checkout by PayTo agreement","monoova-payments-for-woocommerce"),description:(0,o.__)("Allow customers to skip the checkout form with saved PayTo agreements.","monoova-payments-for-woocommerce"),icons:[{src:`${window.monoovaPluginUrl||""}assets/images/bank-transfer.png`,alt:"PayTo Express Checkout"}],checked:e.enable_payto_express_checkout,onChange:n.enable_payto_express_checkout,disabled:!e.enable_payto_payments}),e.enable_express_checkout&&e.enable_payto_express_checkout&&React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:3},React.createElement(a.__experimentalHeading,{level:5},(0,o.__)("Express Checkout Method Priority","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Choose which express checkout method to show first when both Card and PayTo are available.","monoova-payments-for-woocommerce")),React.createElement(a.SelectControl,{value:e.express_checkout_method_priority||"card",onChange:n.express_checkout_method_priority,options:[{label:(0,o.__)("Card Express Checkout First","monoova-payments-for-woocommerce"),value:"card"},{label:(0,o.__)("PayTo Express Checkout First","monoova-payments-for-woocommerce"),value:"payto"}]})))))))))))),d=(0,e.memo)((({settings:e,saveNotice:t,onChangeHandlers:s,setSaveNotice:i,handleSettingChange:_})=>React.createElement(a.__experimentalVStack,{spacing:6,className:"monoova-card-settings-tab"},t&&React.createElement(a.Notice,{className:"monoova-save-notice",status:t.type,onRemove:()=>i(null),isDismissible:!0},t.message),!e.enable_card_payments&&React.createElement(a.Notice,{status:"warning",isDismissible:!1},(0,o.__)("Card payments are disabled. Enable them in the Payment Methods tab to configure these settings.","monoova-payments-for-woocommerce")),e.card_testmode&&React.createElement(a.Card,{className:"monoova-account-status"},React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:2},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Test Mode","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{size:"13"},(0,o.__)("When enabled, card payment methods powered by Monoova will appear on checkout in test mode. No live transactions are processed.","monoova-payments-for-woocommerce"))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Basic Information","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure how this payment method appears to customers.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(l,{label:(0,o.__)("Title","monoova-payments-for-woocommerce"),description:(0,o.__)("This controls the title which the user sees during checkout.","monoova-payments-for-woocommerce"),required:!0},React.createElement(c,{value:e.card_title||"",onChange:s.card_title,disabled:!e.enable_card_payments,placeholder:(0,o.__)("Enter card payment method title","monoova-payments-for-woocommerce")})),React.createElement(l,{label:(0,o.__)("Description","monoova-payments-for-woocommerce"),description:(0,o.__)("This controls the description which the user sees during checkout.","monoova-payments-for-woocommerce")},React.createElement(r,{value:e.card_description||"",onChange:s.card_description,disabled:!e.enable_card_payments,rows:3,placeholder:(0,o.__)("Enter card payment method description","monoova-payments-for-woocommerce")}))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Card Settings","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure test mode and logging for card payments.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.__experimentalGrid,{columns:2,gap:4},React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.card_testmode,disabled:!e.enable_card_payments,onChange:s.card_testmode}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Test Mode","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Process card payments using test API keys","monoova-payments-for-woocommerce"))))),React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.card_debug,disabled:!e.enable_card_payments,onChange:s.card_debug}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Enable logging","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Log card payment events for debugging purposes","monoova-payments-for-woocommerce"))))))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Payment Processing","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure how card payments are processed and handled.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.capture,disabled:!e.enable_card_payments,onChange:s.capture}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Capture payments immediately","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Capture the payment immediately when the order is placed","monoova-payments-for-woocommerce"))))),React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.saved_cards,disabled:!e.enable_card_payments,onChange:s.saved_cards}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Enable saved cards","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Allow customers to save payment methods for future purchases","monoova-payments-for-woocommerce"))))),React.createElement(a.__experimentalDivider,null),React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.apply_surcharge,disabled:!e.enable_card_payments,onChange:s.apply_surcharge}),React.createElement(a.__experimentalVStack,{spacing:1,style:{flexGrow:1}},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Apply surcharge","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Add a surcharge to card payments to cover processing fees","monoova-payments-for-woocommerce"))),e.apply_surcharge&&React.createElement("div",{style:{width:"120px"}},React.createElement(a.TextControl,{value:e.surcharge_amount,disabled:!e.enable_card_payments,onChange:e=>_("surcharge_amount",parseFloat(e)||0),type:"number",min:0,max:10,step:.01}))))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Security & Wallet Settings","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure security features and wallet payment options.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.__experimentalGrid,{columns:2,gap:4},React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.enable_apple_pay,disabled:!e.enable_card_payments,onChange:s.enable_apple_pay}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Enable Apple Pay","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Allow customers to pay using Apple Pay on supported devices","monoova-payments-for-woocommerce"))))),React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.enable_google_pay,disabled:!e.enable_card_payments,onChange:s.enable_google_pay}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Enable Google Pay","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Allow customers to pay using Google Pay on supported devices","monoova-payments-for-woocommerce")))))),React.createElement(a.__experimentalDivider,null),React.createElement(l,{label:(0,o.__)("Order button text","monoova-payments-for-woocommerce"),description:(0,o.__)("Customize the text displayed on the payment button.","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.order_button_text||"",onChange:s.order_button_text,disabled:!e.enable_card_payments,placeholder:(0,o.__)("Pay with Card","monoova-payments-for-woocommerce")}))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Checkout UI Style Settings","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Customize the appearance of the checkout form fields and buttons.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.Flex,{align:"center",justify:"flex-start"},React.createElement(a.__experimentalText,{weight:"500",size:"15"},(0,o.__)("Label Input Fields Of Card Details","monoova-payments-for-woocommerce")),React.createElement(n,{type:"label"})),React.createElement(a.__experimentalGrid,{columns:12,gap:4,style:{gap:"16px"}},React.createElement("div",{style:{gridColumn:"span 7"}},React.createElement(l,{label:(0,o.__)("Font family","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.checkout_ui_styles?.input_label?.font_family||"Helvetica, Arial, sans-serif",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input_label:{...e.checkout_ui_styles?.input_label,font_family:t}}),disabled:!e.enable_card_payments,options:[{label:"Inter",value:"Inter"},{label:"Helvetica",value:"Helvetica, Arial, sans-serif"},{label:"Arial",value:"Arial, sans-serif"},{label:"Times New Roman",value:"Times New Roman, serif"},{label:"Courier New",value:"Courier New, monospace"}]}))),React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(l,{label:(0,o.__)("Font weight","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.checkout_ui_styles?.input_label?.font_weight||"normal",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input_label:{...e.checkout_ui_styles?.input_label,font_weight:t}}),disabled:!e.enable_card_payments,options:[{label:"Regular",value:"normal"},{label:"Bold",value:"bold"},{label:"Light",value:"300"},{label:"Medium",value:"500"},{label:"Semi Bold",value:"600"}]}))),React.createElement("div",{style:{gridColumn:"span 2"}},React.createElement(l,{label:(0,o.__)("Font size","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.checkout_ui_styles?.input_label?.font_size||"14px",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input_label:{...e.checkout_ui_styles?.input_label,font_size:t}}),disabled:!e.enable_card_payments,options:[{label:"12px",value:"12px"},{label:"14px",value:"14px"},{label:"16px",value:"16px"},{label:"18px",value:"18px"},{label:"20px",value:"20px"}]})))),React.createElement(a.__experimentalGrid,{columns:12,gap:4,style:{gap:"16px"}},React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(m,{label:(0,o.__)("Text color","monoova-payments-for-woocommerce"),value:e.checkout_ui_styles?.input_label?.color||"#000000",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input_label:{...e.checkout_ui_styles?.input_label,color:t}}),disabled:!e.enable_card_payments})))))),React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.Flex,{align:"center",justify:"flex-start"},React.createElement(a.__experimentalText,{weight:"500",size:"15"},(0,o.__)("Input Fields Of Card Details","monoova-payments-for-woocommerce")),React.createElement(n,{type:"input"})),React.createElement(a.__experimentalGrid,{columns:12,gap:4,style:{gap:"16px"}},React.createElement("div",{style:{gridColumn:"span 7"}},React.createElement(l,{label:(0,o.__)("Font family","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.checkout_ui_styles?.input?.font_family||"Helvetica, Arial, sans-serif",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input:{...e.checkout_ui_styles?.input,font_family:t}}),disabled:!e.enable_card_payments,options:[{label:"Inter",value:"Inter"},{label:"Helvetica",value:"Helvetica, Arial, sans-serif"},{label:"Arial",value:"Arial, sans-serif"},{label:"Times New Roman",value:"Times New Roman, serif"},{label:"Courier New",value:"Courier New, monospace"}]}))),React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(l,{label:(0,o.__)("Font weight","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.checkout_ui_styles?.input?.font_weight||"normal",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input:{...e.checkout_ui_styles?.input,font_weight:t}}),disabled:!e.enable_card_payments,options:[{label:"Regular",value:"normal"},{label:"Bold",value:"bold"},{label:"Light",value:"300"},{label:"Medium",value:"500"},{label:"Semi Bold",value:"600"}]}))),React.createElement("div",{style:{gridColumn:"span 2"}},React.createElement(l,{label:(0,o.__)("Font size","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.checkout_ui_styles?.input?.font_size||"14px",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input:{...e.checkout_ui_styles?.input,font_size:t}}),disabled:!e.enable_card_payments,options:[{label:"12px",value:"12px"},{label:"14px",value:"14px"},{label:"16px",value:"16px"},{label:"18px",value:"18px"},{label:"20px",value:"20px"}]})))),React.createElement(a.__experimentalGrid,{columns:12,gap:4,style:{gap:"16px"}},React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(m,{label:(0,o.__)("Background color","monoova-payments-for-woocommerce"),value:e.checkout_ui_styles?.input?.background_color||"#FAFAFA",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input:{...e.checkout_ui_styles?.input,background_color:t}}),disabled:!e.enable_card_payments})),React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(m,{label:(0,o.__)("Border color","monoova-payments-for-woocommerce"),value:e.checkout_ui_styles?.input?.border_color||"#E8E8E8",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input:{...e.checkout_ui_styles?.input,border_color:t}}),disabled:!e.enable_card_payments})),React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(m,{label:(0,o.__)("Text color","monoova-payments-for-woocommerce"),value:e.checkout_ui_styles?.input?.text_color||"#000000",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input:{...e.checkout_ui_styles?.input,text_color:t}}),disabled:!e.enable_card_payments})),React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(l,{label:(0,o.__)("Border radius","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.checkout_ui_styles?.input?.border_radius||"8px",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,input:{...e.checkout_ui_styles?.input,border_radius:t}}),disabled:!e.enable_card_payments,style:{borderRadius:"8px",padding:"10px",borderColor:"#D0D5DD"},placeholder:"8px"}))))))),React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.Flex,{align:"center",justify:"flex-start"},React.createElement(a.__experimentalText,{weight:"500",size:"15"},(0,o.__)("Pay Button","monoova-payments-for-woocommerce")),React.createElement(n,{type:"button"})),React.createElement(a.__experimentalGrid,{columns:12,gap:4,style:{gap:"16px"}},React.createElement("div",{style:{gridColumn:"span 7"}},React.createElement(l,{label:(0,o.__)("Font family","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.checkout_ui_styles?.submit_button?.font_family||"Helvetica, Arial, sans-serif",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,submit_button:{...e.checkout_ui_styles?.submit_button,font_family:t}}),disabled:!e.enable_card_payments,options:[{label:"Inter",value:"Inter"},{label:"Helvetica",value:"Helvetica, Arial, sans-serif"},{label:"Arial",value:"Arial, sans-serif"},{label:"Times New Roman",value:"Times New Roman, serif"},{label:"Courier New",value:"Courier New, monospace"}]}))),React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(l,{label:(0,o.__)("Font weight","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.checkout_ui_styles?.submit_button?.font_weight||"bold",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,submit_button:{...e.checkout_ui_styles?.submit_button,font_weight:t}}),disabled:!e.enable_card_payments,options:[{label:"Regular",value:"normal"},{label:"Bold",value:"bold"},{label:"Light",value:"300"},{label:"Medium",value:"500"},{label:"Semi Bold",value:"600"}]}))),React.createElement("div",{style:{gridColumn:"span 2"}},React.createElement(l,{label:(0,o.__)("Font size","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.checkout_ui_styles?.submit_button?.font_size||"17px",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,submit_button:{...e.checkout_ui_styles?.submit_button,font_size:t}}),disabled:!e.enable_card_payments,options:[{label:"14px",value:"14px"},{label:"16px",value:"16px"},{label:"17px",value:"17px"},{label:"18px",value:"18px"},{label:"20px",value:"20px"}]})))),React.createElement(a.__experimentalGrid,{columns:12,gap:4,style:{gap:"16px"}},React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(m,{label:(0,o.__)("Background color","monoova-payments-for-woocommerce"),value:e.checkout_ui_styles?.submit_button?.background||"#2ab5c4",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,submit_button:{...e.checkout_ui_styles?.submit_button,background:t}}),disabled:!e.enable_card_payments})),React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(m,{label:(0,o.__)("Border color","monoova-payments-for-woocommerce"),value:e.checkout_ui_styles?.submit_button?.border_color||"#2ab5c4",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,submit_button:{...e.checkout_ui_styles?.submit_button,border_color:t}}),disabled:!e.enable_card_payments})),React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(m,{label:(0,o.__)("Text color","monoova-payments-for-woocommerce"),value:e.checkout_ui_styles?.submit_button?.text_color||"#000000",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,submit_button:{...e.checkout_ui_styles?.submit_button,text_color:t}}),disabled:!e.enable_card_payments})),React.createElement("div",{style:{gridColumn:"span 3"}},React.createElement(l,{label:(0,o.__)("Border radius","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.checkout_ui_styles?.submit_button?.border_radius||"10px",onChange:t=>_("checkout_ui_styles",{...e.checkout_ui_styles,submit_button:{...e.checkout_ui_styles?.submit_button,border_radius:t}}),disabled:!e.enable_card_payments,style:{borderRadius:"8px",padding:"10px",borderColor:"#D0D5DD"},placeholder:"10px"})))))))))))),u=(0,e.memo)((({settings:e,saveNotice:t,onChangeHandlers:n,setSaveNotice:m,onGenerateAutomatcher:s,isGenerating:i})=>React.createElement(a.__experimentalVStack,{spacing:6,className:"monoova-payid-settings-tab"},t&&React.createElement(a.Notice,{className:"monoova-save-notice",status:t.type,onRemove:()=>m(null),isDismissible:!0},t.message),!e.enable_payid_payments&&React.createElement(a.Notice,{status:"warning",isDismissible:!1},(0,o.__)("PayID payments are disabled. Enable them in the Payment Methods tab to configure these settings.","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Basic Information","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure how this payment method appears to customers.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(l,{label:(0,o.__)("Title","monoova-payments-for-woocommerce"),description:(0,o.__)("This controls the title which the user sees during checkout.","monoova-payments-for-woocommerce"),required:!0},React.createElement(c,{value:e.payid_title||"",onChange:n.payid_title,disabled:!e.enable_payid_payments,placeholder:(0,o.__)("Enter PayID payment method title","monoova-payments-for-woocommerce")})),React.createElement(l,{label:(0,o.__)("Description","monoova-payments-for-woocommerce"),description:(0,o.__)("This controls the description which the user sees during checkout.","monoova-payments-for-woocommerce")},React.createElement(r,{value:e.payid_description||"",onChange:n.payid_description,disabled:!e.enable_payid_payments,rows:3,placeholder:(0,o.__)("Enter PayID payment method description","monoova-payments-for-woocommerce")}))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("PayID Settings","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure test mode and logging for PayID payments.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.__experimentalGrid,{columns:2,gap:4},React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.payid_testmode,disabled:!e.enable_payid_payments,onChange:n.payid_testmode}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Test Mode","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Process PayID payments using test API keys","monoova-payments-for-woocommerce"))))),React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.payid_debug,disabled:!e.enable_payid_payments,onChange:n.payid_debug}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Enable logging","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Log PayID payment events for debugging purposes","monoova-payments-for-woocommerce"))))))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Payment Options","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure payment types, expiry settings, and customer instructions.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(l,{label:(0,o.__)("Account Name","monoova-payments-for-woocommerce"),description:(0,o.__)("The account name to use when generating the store-wide Automatcher account.","monoova-payments-for-woocommerce"),required:!0},React.createElement(c,{value:e.account_name||"",onChange:n.account_name,disabled:!e.enable_payid_payments,placeholder:(0,o.__)("e.g. Your Store Name","monoova-payments-for-woocommerce")})),React.createElement(a.__experimentalGrid,{columns:2,gap:4},React.createElement(l,{label:(0,o.__)("Store BSB","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.static_bsb||"",readOnly:!0,disabled:!e.enable_payid_payments,placeholder:(0,o.__)("Generated by Monoova","monoova-payments-for-woocommerce")})),React.createElement(l,{label:(0,o.__)("Store Account Number","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.static_account_number||"",readOnly:!0,disabled:!e.enable_payid_payments,placeholder:(0,o.__)("Generated by Monoova","monoova-payments-for-woocommerce")}))),React.createElement(a.Button,{variant:"secondary",onClick:s,isBusy:i,disabled:!e.enable_payid_payments||i},(0,o.__)("Generate / Replace Store Automatcher Account","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Note: It may take up to 5 minutes for a newly generated account to become fully active for receiving payments.","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalDivider,null),React.createElement(l,{label:(0,o.__)("Payment types","monoova-payments-for-woocommerce"),description:(0,o.__)("Select which payment types to accept.","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{multiple:!0,value:e.payment_types,onChange:n.payment_types,disabled:!e.enable_payid_payments,options:[{label:(0,o.__)("PayID","monoova-payments-for-woocommerce"),value:"payid"},{label:(0,o.__)("Bank Transfer","monoova-payments-for-woocommerce"),value:"bank_transfer"}]})),React.createElement(l,{label:(0,o.__)("Payment expiry (hours)","monoova-payments-for-woocommerce"),description:(0,o.__)("Number of hours before payment instructions expire.","monoova-payments-for-woocommerce")},React.createElement(a.TextControl,{value:e.expire_hours,onChange:n.expire_hours,type:"number",min:1,max:168,disabled:!e.enable_payid_payments})),React.createElement(l,{label:(0,o.__)("Payment Instructions","monoova-payments-for-woocommerce"),description:(0,o.__)("Additional instructions to show customers about PayID/Bank Transfer payments.","monoova-payments-for-woocommerce")},React.createElement(r,{value:e.instructions||"",onChange:n.instructions,rows:4,disabled:!e.enable_payid_payments})),React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.payid_show_reference_field,onChange:n.payid_show_reference_field,disabled:!e.enable_payid_payments}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Display Payment Reference Field","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("If enabled, a separate 'Payment Reference' field will be shown below the QR code and in the bank transfer details.","monoova-payments-for-woocommerce"))))))))))))),y=(0,e.memo)((({settings:e,saveNotice:t,onChangeHandlers:n,setSaveNotice:m,onGenerateAutomatcher:s,isGenerating:i})=>React.createElement(a.__experimentalVStack,{spacing:6,className:"monoova-payto-settings-tab"},t&&React.createElement(a.Notice,{className:"monoova-save-notice",status:t.type,onRemove:()=>m(null),isDismissible:!0},t.message),!e.enable_payto_payments&&React.createElement(a.Notice,{status:"warning",isDismissible:!1},(0,o.__)("PayTo payments are disabled. Enable them in the Payment Methods tab to configure these settings.","monoova-payments-for-woocommerce")),e.enable_payto_payments&&e.enable_payto_express_checkout&&React.createElement(a.Notice,{status:"info",isDismissible:!1},(0,o.__)("PayTo Express Checkout is enabled. Customers with saved PayTo agreements can complete purchases with a single click.","monoova-payments-for-woocommerce")),e.enable_payto_payments&&!e.enable_payto_express_checkout&&React.createElement(a.Notice,{status:"info",isDismissible:!1},(0,o.__)("Enable PayTo Express Checkout in the Payment Methods tab to allow customers with saved agreements to complete purchases faster.","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Basic Information","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure how this payment method appears to customers.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(l,{label:(0,o.__)("Title","monoova-payments-for-woocommerce"),description:(0,o.__)("This controls the title which the user sees during checkout.","monoova-payments-for-woocommerce"),required:!0},React.createElement(c,{value:e.payto_title||"",onChange:n.payto_title,disabled:!e.enable_payto_payments,placeholder:(0,o.__)("Enter PayTo payment method title","monoova-payments-for-woocommerce")})),React.createElement(l,{label:(0,o.__)("Description","monoova-payments-for-woocommerce"),description:(0,o.__)("This controls the description which the user sees during checkout.","monoova-payments-for-woocommerce")},React.createElement(r,{value:e.payto_description||"",onChange:n.payto_description,disabled:!e.enable_payto_payments,rows:3,placeholder:(0,o.__)("Enter PayTo payment method description","monoova-payments-for-woocommerce")}))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("PayTo Settings","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure PayTo payment gateway behavior and debugging options.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.payto_testmode,onChange:n.payto_testmode,disabled:!e.enable_payto_payments}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Enable Test Mode","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Enable this to use sandbox/test environment for PayTo payments.","monoova-payments-for-woocommerce"))))),React.createElement(a.PanelRow,null,React.createElement(a.Flex,{align:"center",justify:"flex-start",gap:3,style:{width:"100%"}},React.createElement(a.CheckboxControl,{checked:e.payto_debug,onChange:n.payto_debug,disabled:!e.enable_payto_payments}),React.createElement(a.__experimentalVStack,{spacing:1},React.createElement(a.__experimentalText,{weight:"500",size:"14"},(0,o.__)("Enable Debug Mode","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Enable debug logging for PayTo transactions. Check logs under WooCommerce > Status > Logs.","monoova-payments-for-woocommerce")))))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Payment Agreement Settings","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("Configure PayTo payment agreement settings including purpose codes and expiry dates.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(l,{label:(0,o.__)("Purpose Code","monoova-payments-for-woocommerce"),description:(0,o.__)("Purpose code for PayTo agreements as per ISO 20022 standards. This indicates the purpose of the payment.","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.payto_purpose||"OTHR",onChange:n.payto_purpose,disabled:!e.enable_payto_payments,options:[{label:"MORT - Mortgage",value:"MORT"},{label:"UTIL - Utilities",value:"UTIL"},{label:"LOAN - Loan",value:"LOAN"},{label:"DEPD - Deposit",value:"DEPD"},{label:"GAMP - Gaming/Gambling",value:"GAMP"},{label:"RETL - Retail",value:"RETL"},{label:"SALA - Salary Payment",value:"SALA"},{label:"PERS - Personal",value:"PERS"},{label:"GOVT - Government",value:"GOVT"},{label:"PENS - Pension",value:"PENS"},{label:"TAXS - Tax Payment",value:"TAXS"},{label:"OTHR - Other",value:"OTHR"}]})),React.createElement(l,{label:(0,o.__)("Agreement Expiry (Days)","monoova-payments-for-woocommerce"),description:(0,o.__)("Number of days from creation after which the PayTo agreement will expire. Leave empty for no expiry.","monoova-payments-for-woocommerce")},React.createElement(a.TextControl,{value:e.payto_agreement_expiry_days||"",onChange:n.payto_agreement_expiry_days,disabled:!e.enable_payto_payments,type:"number",min:1,max:365,placeholder:(0,o.__)("e.g. 30","monoova-payments-for-woocommerce")})),React.createElement(l,{label:(0,o.__)("Payee Type","monoova-payments-for-woocommerce"),description:(0,o.__)("Type of payee for PayTo agreements. ORGN for organizations/businesses, PERS for individuals.","monoova-payments-for-woocommerce")},React.createElement(a.SelectControl,{value:e.payto_payee_type||"ORGN",onChange:n.payto_payee_type,disabled:!e.enable_payto_payments,options:[{label:"ORGN - Organization",value:"ORGN"},{label:"PERS - Person",value:"PERS"}]})),React.createElement(l,{label:(0,o.__)("Default Maximum Amount (AUD)","monoova-payments-for-woocommerce"),description:(0,o.__)("Default maximum amount for PayTo agreements. This can be modified by customers during checkout. Higher amounts provide more flexibility for variable pricing.","monoova-payments-for-woocommerce")},React.createElement(a.TextControl,{value:e.payto_maximum_amount||"1000",onChange:n.payto_maximum_amount,disabled:!e.enable_payto_payments,type:"number",min:.01,step:.01,placeholder:(0,o.__)("e.g. 1000","monoova-payments-for-woocommerce")}))))))),React.createElement(a.__experimentalGrid,{columns:12,gap:6,className:"monoova-settings-section"},React.createElement(a.__experimentalVStack,{spacing:3,style:{gridColumn:"span 4"}},React.createElement(a.__experimentalHeading,{level:3},(0,o.__)("Unified Automatcher Account","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"14"},(0,o.__)("PayTo requires the same unified automatcher account as PayID for payment processing. This account is shared between PayID and PayTo payment methods.","monoova-payments-for-woocommerce"))),React.createElement(a.__experimentalVStack,{spacing:4,style:{gridColumn:"span 8"}},React.createElement(a.Card,null,React.createElement(a.CardBody,null,React.createElement(a.__experimentalVStack,{spacing:4},React.createElement(l,{label:(0,o.__)("Account Name","monoova-payments-for-woocommerce"),description:(0,o.__)("The account name for the unified Automatcher account used by both PayID and PayTo.","monoova-payments-for-woocommerce"),required:!0},React.createElement(c,{value:e.account_name||"",onChange:n.account_name,disabled:!e.enable_payto_payments,placeholder:(0,o.__)("e.g. Your Store Name","monoova-payments-for-woocommerce")})),React.createElement(a.__experimentalGrid,{columns:2,gap:4},React.createElement(l,{label:(0,o.__)("Store BSB","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.static_bsb||"",readOnly:!0,disabled:!e.enable_payto_payments,placeholder:(0,o.__)("Generated by Monoova","monoova-payments-for-woocommerce")})),React.createElement(l,{label:(0,o.__)("Store Account Number","monoova-payments-for-woocommerce")},React.createElement(c,{value:e.static_account_number||"",readOnly:!0,disabled:!e.enable_payto_payments,placeholder:(0,o.__)("Generated by Monoova","monoova-payments-for-woocommerce")}))),React.createElement(a.Button,{variant:"secondary",onClick:s,isBusy:i,disabled:!e.enable_payto_payments||i},(0,o.__)("Generate / Replace Store Automatcher Account","monoova-payments-for-woocommerce")),React.createElement(a.__experimentalText,{variant:"muted",size:"13"},(0,o.__)("Note: This is the same automatcher account used by PayID. Changes here will affect both PayID and PayTo payment methods. It may take up to 5 minutes for a newly generated account to become fully active.","monoova-payments-for-woocommerce")))))))))),v=()=>{const[t,n]=(0,e.useState)({enable_card_payments:!1,enable_payid_payments:!1,enable_payto_payments:!1,enable_express_checkout:!1,enable_payto_express_checkout:!1,express_checkout_method_priority:"card",card_title:"Credit / Debit Card",card_description:"Pay with your credit or debit card via Monoova.",card_testmode:!0,card_debug:!0,capture:!0,saved_cards:!0,apply_surcharge:!1,surcharge_amount:0,enable_apple_pay:!0,enable_google_pay:!0,order_button_text:"Pay with Card",checkout_ui_styles:{input_label:{font_family:"Helvetica, Arial, sans-serif",font_weight:"normal",font_size:"14px",color:"#000000"},input:{font_family:"Helvetica, Arial, sans-serif",font_weight:"normal",font_size:"14px",background_color:"#FAFAFA",border_color:"#E8E8E8",border_radius:"8px",text_color:"#000000"},submit_button:{font_family:"Helvetica, Arial, sans-serif",font_size:"17px",background:"#2ab5c4",border_radius:"10px",border_color:"#2ab5c4",font_weight:"bold",text_color:"#000000"}},payid_title:"PayID / Bank Transfer",payid_description:"Pay using PayID or bank transfer.",payid_testmode:!0,payid_debug:!0,payid_show_reference_field:!0,static_bank_account_name:"",static_bsb:"",static_account_number:"",payment_types:["payid","bank_transfer"],expire_hours:24,account_name:"",instructions:"",payto_title:"PayTo",payto_description:"Approve a payment agreement to complete the purchase.",payto_testmode:!0,payto_debug:!0,payto_purpose:"OTHR",payto_agreement_expiry_days:"",payto_payee_type:"ORGN",payto_maximum_amount:1e3,enabled:!0,maccount_number:"",test_api_key:"",live_api_key:"",monoova_payments_api_url_sandbox:"https://api.m-pay.com.au",monoova_payments_api_url_live:"https://api.mpay.com.au",monoova_card_api_url_sandbox:"https://sand-api.monoova.com",monoova_card_api_url_live:"https://api.monoova.com"}),[c,r]=(0,e.useState)(!1),[l,m]=(0,e.useState)(null),[s,_]=(0,e.useState)({}),[v,g]=(0,e.useState)(!1),[b,h]=(0,e.useState)({sandbox:{all_active:!1,card_active:!1,payto_active:!1,payments_active:!1,isChecking:!1,isConnecting:!1,lastChecked:null},live:{all_active:!1,card_active:!1,payto_active:!1,payments_active:!1,isChecking:!1,isConnecting:!1,lastChecked:null}}),E=(0,e.useCallback)((()=>{setTimeout((()=>{const e=document.querySelector(".monoova-save-notice");e&&e.scrollIntoView({behavior:"smooth",block:"center"})}),100)}),[]),x=e=>{const a=e?"sandbox":"live",o=e?t.test_api_key:t.live_api_key,n=e?t.monoova_payments_api_url_sandbox:t.monoova_payments_api_url_live,c=e?t.monoova_card_api_url_sandbox:t.monoova_card_api_url_live,r=t.maccount_number,l=[];return o&&""!==o.trim()||l.push("API Key"),n&&""!==n.trim()||l.push("PayID API URL"),c&&""!==c.trim()||l.push("Card API URL"),r&&""!==r.trim()||l.push("mAccount Number"),{isValid:0===l.length,missingFields:l,mode:a.charAt(0).toUpperCase()+a.slice(1)}};(0,e.useEffect)((()=>{(async()=>{if(window.monoovaAdminSettings){const e={...window.monoovaAdminSettings};["enabled","enable_card_payments","enable_payid_payments","enable_payto_payments","enable_express_checkout","enable_payto_express_checkout","capture","saved_cards","apply_surcharge","enable_apple_pay","enable_google_pay","card_testmode","card_debug","payid_testmode","payid_debug","payid_show_reference_field","payto_testmode","payto_debug"].forEach((t=>{const a=e[t];e[t]="string"==typeof a?"yes"===a||"1"===a||"true"===a:"number"==typeof a?Boolean(a):"boolean"==typeof a&&a})),n((t=>({...t,...e})))}})()}),[]);const R=async(e=!0)=>{const t=e?"sandbox":"live",a=x(e);if(a.isValid){h((e=>({...e,[t]:{...e[t],isChecking:!0,validationError:null}})));try{if(!window.monoovaCheckWebhookSubscriptionsNonce)throw new Error("Security nonce for checking webhook subscriptions not available. Please refresh the page.");const a=new FormData;a.append("action","monoova_check_webhook_subscriptions_status"),a.append("nonce",window.monoovaCheckWebhookSubscriptionsNonce),a.append("is_testmode",e.toString());const o=await fetch(window.ajaxUrl,{method:"POST",body:a}),n=await o.json();if(!n.success)throw new Error(n.data?.message||"Failed to check webhook status.");h((e=>({...e,[t]:{...e[t],all_active:n.data.all_active,card_active:n.data.card_active,payto_active:n.data.payto_active,payments_active:n.data.payments_active,lastChecked:new Date,isChecking:!1}})))}catch(e){console.error("Error checking webhook status:",e),h((e=>({...e,[t]:{...e[t],isChecking:!1}})))}}else h((e=>({...e,[t]:{...e[t],isChecking:!1,all_active:!1,card_active:!1,payto_active:!1,payments_active:!1,validationError:`Monoova API client (${a.mode}) is not available. Please check your configuration on API Credentials and API URLs sections.`}})))};(0,e.useEffect)((()=>{(t.maccount_number||t.test_api_key||t.live_api_key)&&(R(!0),R(!1))}),[t.test_api_key,t.live_api_key,t.maccount_number,t.monoova_payments_api_url_sandbox,t.monoova_payments_api_url_live,t.monoova_card_api_url_sandbox,t.monoova_card_api_url_live]);const f=(0,e.useCallback)((async(e="all")=>{r(!0),m(null),_({});try{const a={},c=t.enable_card_payments&&!t.card_testmode,l=t.enable_payid_payments&&!t.payid_testmode;if((c||l)&&(t.live_api_key&&""!==t.live_api_key.trim()||(a.live_api_key=(0,o.__)("Live API Key is required when any payment method has test mode disabled.","monoova-payments-for-woocommerce"))),l&&(t.monoova_payments_api_url_live&&""!==t.monoova_payments_api_url_live.trim()||(a.monoova_payments_api_url_live=(0,o.__)("Live Payments API URL is required when PayID test mode is disabled.","monoova-payments-for-woocommerce"))),c&&(t.monoova_card_api_url_live&&""!==t.monoova_card_api_url_live.trim()||(a.monoova_card_api_url_live=(0,o.__)("Live Card API URL is required when Card test mode is disabled.","monoova-payments-for-woocommerce"))),Object.keys(a).length>0){_(a);const e={live_api_key:(0,o.__)("Live API Key","monoova-payments-for-woocommerce"),monoova_payments_api_url_live:(0,o.__)("Live Payments API URL","monoova-payments-for-woocommerce"),monoova_card_api_url_live:(0,o.__)("Live Card API URL","monoova-payments-for-woocommerce")},t=Object.keys(a).map((t=>e[t]||t)).join(", "),n=(0,o.__)("Please fix the following validation errors before saving: ","monoova-payments-for-woocommerce")+t;return m({type:"error",message:n}),r(!1),void E()}const s={...t};["enabled","enable_card_payments","enable_payid_payments","enable_payto_payments","enable_express_checkout","enable_payto_express_checkout","capture","saved_cards","apply_surcharge","enable_apple_pay","enable_google_pay","card_testmode","card_debug","payid_testmode","payid_debug","payid_show_reference_field","payto_testmode","payto_debug"].forEach((e=>{s[e]=Boolean(s[e])}));const i=new FormData;i.append("action","monoova_save_payment_settings"),i.append("settings",JSON.stringify(s)),i.append("tab",e);const p=await fetch(window.ajaxUrl,{method:"POST",body:i}),d=await p.json();if(!d.success)throw new Error(d.data?.message||"Unknown error occurred");m({type:"success",message:d.data.message||(0,o.__)("Settings saved successfully!","monoova-payments-for-woocommerce")}),d.data.settings&&n((e=>({...e,...d.data.settings}))),E()}catch(e){console.error("Error saving settings:",e),m({type:"error",message:e.message||(0,o.__)("Failed to save settings. Please try again.","monoova-payments-for-woocommerce")}),E()}finally{r(!1)}}),[t,E]),w=(0,e.useCallback)((async()=>{g(!0),m(null);try{if(!window.monoovaGenerateAutomatcherNonce)throw new Error("Security nonce for generating automatcher not available. Please refresh the page.");const e=new FormData;e.append("action","monoova_generate_automatcher"),e.append("nonce",window.monoovaGenerateAutomatcherNonce);const t=await fetch(window.ajaxUrl,{method:"POST",body:e}),a=await t.json();if(!a.success)throw new Error(a.data?.message||"Unknown error occurred while generating account.");m({type:"success",message:a.data.message||(0,o.__)("Automatcher account generated successfully!","monoova-payments-for-woocommerce")}),n((e=>({...e,static_bsb:a.data.bsb,static_account_number:a.data.accountNumber,static_bank_account_name:a.data.accountName}))),E()}catch(e){console.error("Error generating Automatcher account:",e),m({type:"error",message:e.message||(0,o.__)("Failed to generate Automatcher account. Please check logs.","monoova-payments-for-woocommerce")}),E()}finally{g(!1)}}),[]),k=(0,e.useCallback)((async(e=!0)=>{const t=e?"sandbox":"live",a=x(e);if(!a.isValid)return m({type:"error",message:`Monoova API client (${a.mode}) is not available. Please check your configuration on API Credentials and API URLs sections.`}),void E();h((e=>({...e,[t]:{...e[t],isConnecting:!0,validationError:null}}))),m(null);try{if(!window.monoovaSubscribeWebhookEventsNonce)throw new Error("Security nonce for subscribing to webhook events not available. Please refresh the page.");const t=new FormData;t.append("action","monoova_subscribe_to_webhook_events"),t.append("nonce",window.monoovaSubscribeWebhookEventsNonce),t.append("is_testmode",e.toString());const a=await fetch(window.ajaxUrl,{method:"POST",body:t}),n=await a.json();if(!n.success)throw new Error(n.data?.message||"Failed to subscribe to webhooks.");m({type:"success",message:n.data.message||(0,o.__)("Webhook subscriptions updated successfully!","monoova-payments-for-woocommerce")}),await R(e),E()}catch(e){console.error("Error subscribing to webhooks:",e),m({type:"error",message:e.message||(0,o.__)("Failed to subscribe to webhooks. Please check logs.","monoova-payments-for-woocommerce")}),E()}finally{h((e=>({...e,[t]:{...e[t],isConnecting:!1}})))}}),[R,E]),C=(0,e.useCallback)(((e,t)=>{n((a=>({...a,[e]:t})))}),[]),S=(0,e.useMemo)((()=>({enabled:e=>C("enabled",e),maccount_number:e=>C("maccount_number",e),test_api_key:e=>C("test_api_key",e),live_api_key:e=>C("live_api_key",e),monoova_payments_api_url_sandbox:e=>C("monoova_payments_api_url_sandbox",e),monoova_payments_api_url_live:e=>C("monoova_payments_api_url_live",e),monoova_card_api_url_sandbox:e=>C("monoova_card_api_url_sandbox",e),monoova_card_api_url_live:e=>C("monoova_card_api_url_live",e),enable_card_payments:e=>C("enable_card_payments",e),enable_payid_payments:e=>C("enable_payid_payments",e),enable_payto_payments:e=>C("enable_payto_payments",e),enable_express_checkout:e=>C("enable_express_checkout",e),enable_payto_express_checkout:e=>C("enable_payto_express_checkout",e),express_checkout_method_priority:e=>C("express_checkout_method_priority",e),card_title:e=>C("card_title",e),card_description:e=>C("card_description",e),card_testmode:e=>C("card_testmode",e),card_debug:e=>C("card_debug",e),capture:e=>C("capture",e),saved_cards:e=>C("saved_cards",e),apply_surcharge:e=>C("apply_surcharge",e),surcharge_amount:e=>C("surcharge_amount",e),enable_apple_pay:e=>C("enable_apple_pay",e),enable_google_pay:e=>C("enable_google_pay",e),order_button_text:e=>C("order_button_text",e),payid_title:e=>C("payid_title",e),payid_description:e=>C("payid_description",e),payid_testmode:e=>C("payid_testmode",e),payid_debug:e=>C("payid_debug",e),payid_show_reference_field:e=>C("payid_show_reference_field",e),static_bank_account_name:e=>C("static_bank_account_name",e),static_bsb:e=>C("static_bsb",e),static_account_number:e=>C("static_account_number",e),payment_types:e=>C("payment_types",e),expire_hours:e=>C("expire_hours",e),account_name:e=>C("account_name",e),instructions:e=>C("instructions",e),payto_title:e=>C("payto_title",e),payto_description:e=>C("payto_description",e),payto_testmode:e=>C("payto_testmode",e),payto_debug:e=>C("payto_debug",e),payto_purpose:e=>C("payto_purpose",e),payto_agreement_expiry_days:e=>C("payto_agreement_expiry_days",e),payto_payee_type:e=>C("payto_payee_type",e),payto_maximum_amount:e=>C("payto_maximum_amount",parseFloat(e)||1e3)})),[C]);(0,e.useEffect)((()=>{const e=async e=>{const t=e.target;if(t&&(t.querySelector("#monoova-payment-settings-container")||t.querySelector('input[name="woocommerce_monoova_unified_enabled"]')||t.querySelector('input[name*="monoova_unified"]')||window.location.href.includes("section=monoova_unified"))){e.preventDefault(),e.stopPropagation();const a=t.querySelector('input[type="submit"], button[type="submit"], .button-primary');a&&a.value&&!a.getAttribute("data-original-value")&&a.setAttribute("data-original-value",a.value);try{await f(),a&&(a.classList.remove("is-busy"),a.disabled=!1,a.value&&(a.value=a.getAttribute("data-original-value")||"Save changes"))}catch(e){throw a&&(a.classList.remove("is-busy"),a.disabled=!1,a.value&&(a.value=a.getAttribute("data-original-value")||"Save changes")),e}return!1}};document.addEventListener("submit",e,!0);const t=document.querySelector("form#mainform");return t&&t.addEventListener("submit",e),()=>{document.removeEventListener("submit",e,!0),t&&t.removeEventListener("submit",e)}}),[f]);const P=[{name:"general_settings",title:(0,o.__)("General Settings","monoova-payments-for-woocommerce"),content:React.createElement(i,{settings:t,saveNotice:l,onChangeHandlers:S,setSaveNotice:m,validationErrors:s,webhookStatus:b,onCheckWebhookStatus:R,onSubscribeToWebhooks:k})},{name:"payment_methods",title:(0,o.__)("Payment methods","monoova-payments-for-woocommerce"),content:React.createElement(p,{settings:t,saveNotice:l,onChangeHandlers:S,setSaveNotice:m})},{name:"card_settings",title:(0,o.__)("Card settings","monoova-payments-for-woocommerce"),content:React.createElement(d,{settings:t,saveNotice:l,onChangeHandlers:S,setSaveNotice:m,handleSettingChange:C})},{name:"payid_settings",title:(0,o.__)("PayID settings","monoova-payments-for-woocommerce"),content:React.createElement(u,{settings:t,saveNotice:l,onChangeHandlers:S,setSaveNotice:m,onGenerateAutomatcher:w,isGenerating:v})},{name:"payto_settings",title:(0,o.__)("PayTo settings","monoova-payments-for-woocommerce"),content:React.createElement(y,{settings:t,saveNotice:l,onChangeHandlers:S,setSaveNotice:m,onGenerateAutomatcher:w,isGenerating:v})}];return React.createElement("div",{className:"monoova-payment-settings"},React.createElement(a.TabPanel,{className:"monoova-settings-tabs",activeClass:"is-active",tabs:P},(e=>React.createElement(a.__experimentalVStack,{spacing:6},e.content))))};document.addEventListener("DOMContentLoaded",(function(){const t=document.getElementById("monoova-payment-settings-container");t&&(0,e.createRoot)(t).render(React.createElement(v,null))}))})();