!function(o){"use strict";var t={init:function(){this.setupCopyButtons(),this.setupAPITestButtons()},setupCopyButtons:function(){o(".monoova-copy-webhook").on("click",(function(){var e=o(this).data("clipboard-text");t.copyToClipboard(e,o(this))}))},copyToClipboard:function(t,e){var n=o("<input>");o("body").append(n),n.val(t).select();var a=document.execCommand("copy");if(n.remove(),a){var s=e.text();e.text(monoova_admin_params.i18n.success),setTimeout((function(){e.text(s)}),2e3)}},setupAPITestButtons:function(){o(".monoova-api-test-button").on("click",(function(t){t.preventDefault();var e=o(this),n=e.closest("tr").find(".monoova-api-test-result");e.prop("disabled",!0),e.text(monoova_admin_params.i18n.loading),n.html("");var a=o("#woocommerce_monoova_card_testmode").is(":checked")?"test":"live",s="test"===a?o("#woocommerce_monoova_card_test_api_key").val():o("#woocommerce_monoova_card_live_api_key").val();if(!s)return n.html('<div class="monoova-test-error">Please enter an API key.</div>'),e.prop("disabled",!1),void e.text("Test API Connection");o.ajax({url:monoova_admin_params.ajax_url,method:"POST",data:{action:"monoova_test_api_connection",api_key:s,mode:a,nonce:monoova_admin_params.nonce},success:function(o){o.success?n.html('<div class="monoova-test-success">API connection successful!</div>'):n.html('<div class="monoova-test-error">API connection failed: '+o.data.message+"</div>")},error:function(){n.html('<div class="monoova-test-error">Request failed. Please try again.</div>')},complete:function(){e.prop("disabled",!1),e.text("Test API Connection")}})}))}};o(document).ready((function(){t.init()}))}(jQuery);