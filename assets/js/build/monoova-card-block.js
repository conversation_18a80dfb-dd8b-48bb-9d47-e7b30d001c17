(()=>{"use strict";const e=wc.wcBlocksRegistry,t=window.wp.htmlEntities,n=wc.wcSettings,o=window.wp.i18n,i=window.wp.element,r=window.wp.data,a=wc.wcBlocksData,s=({containerId:e,isLoading:t=!1,isInitialized:n=!1,containerRef:o=null})=>React.createElement("div",{className:"embedded-card-form",style:{marginTop:"20px"}},t&&!n&&React.createElement("div",{className:"primer-loading-indicator",style:{padding:"20px",textAlign:"center",color:"#666"}},"Loading payment form..."),React.createElement("div",{ref:o,className:"primer-container-target"},React.createElement("div",{id:e}))),c=new class{constructor(){this.containers=new Map,this.activeContainers=new Set,this.isInitialized=!1}getOrCreateContainer(e,t){const n=`${e}_${t}`;if(!this.containers.has(n)){const e=document.createElement("div");e.id=t,e.className="primer-checkout-persistent-container",e.style.cssText="\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            ",document.body.appendChild(e),this.containers.set(n,{element:e,isInitialized:!1,isVisible:!1,orderId:null,clientToken:null})}return this.containers.get(n)}showContainer(e,t,n){const o=`${e}_${t}`,i=this.containers.get(o);i&&i.element&&(this.hideAllContainers(),n&&(n.appendChild(i.element),i.element.style.cssText="\n                    position: relative;\n                    top: auto;\n                    left: auto;\n                    width: 100%;\n                    visibility: visible;\n                    opacity: 1;\n                    pointer-events: auto;\n                    transition: all 0.3s ease;\n                ",i.isVisible=!0,this.activeContainers.add(o)))}hideContainer(e,t){const n=`${e}_${t}`,o=this.containers.get(n);o&&o.element&&(document.body.appendChild(o.element),o.element.style.cssText="\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            ",o.isVisible=!1,this.activeContainers.delete(n))}hideAllContainers(){this.containers.forEach(((e,t)=>{e.isVisible&&this.hideContainer(t.split("_")[0],t.split("_")[1])}))}clearOrderData(e,t){const n=`${e}_${t}`,o=this.containers.get(n);o&&(o.orderId=null,o.clientToken=null,o.instructions=null,o.isInitialized=!1)}setContainerInitialized(e,t){const n=`${e}_${t}`,o=this.containers.get(n);o&&(o.isInitialized=!0)}isContainerInitialized(e,t){const n=`${e}_${t}`,o=this.containers.get(n);return!!o&&o.isInitialized}setOrderData(e,t,n,o,i=null){const r=`${e}_${t}`,a=this.containers.get(r);a?(a.orderId=n,a.clientToken=o,a.instructions=i):console.warn(`[PrimerCheckoutManager] Container ${r} not found for setting order data`)}getOrderData(e,t){const n=`${e}_${t}`,o=this.containers.get(n);return o?{orderId:o.orderId,clientToken:o.clientToken,instructions:o.instructions}:{orderId:null,clientToken:null,instructions:null}}getContainerElement(e,t){const n=`${e}_${t}`,o=this.containers.get(n);return o?o.element:null}cleanup(){this.containers.forEach((e=>{e.element&&e.element.parentNode&&e.element.parentNode.removeChild(e.element)})),this.containers.clear(),this.activeContainers.clear()}};"undefined"!=typeof window&&window.addEventListener("beforeunload",(()=>{c.cleanup()}));if(void 0===e.registerPaymentMethod)console.warn("WooCommerce Blocks registerPaymentMethod is not available. This may be due to edit mode or missing dependencies.");else{let l={};try{if(void 0===n.getSetting)throw new Error("getSetting function not available");l=(0,n.getSetting)("monoova_card_data",{})}catch(e){console.error("getSetting not available, trying fallback:",e),l=window.monoova_card_blocks_params||{}}l&&"object"==typeof l&&0!==Object.keys(l).length||(console.warn("Monoova Card settings not found or empty, using defaults"),l={title:"Credit / Debit Card",description:"Accept payments via Mastercard, Visa, Apple pay and Google pay",supports:[]});const d=(0,o.__)("Credit / Debit Card","monoova-payments-for-woocommerce"),u=(0,t.decodeEntities)(l.title)||d,m=({billing:e,shippingData:n,checkoutStatus:o,paymentStatus:d,eventRegistration:u,emitResponse:m})=>{const h=(0,t.decodeEntities)(l.description||""),{orderId:p}=(0,r.useSelect)((e=>({orderId:e(a.CHECKOUT_STORE_KEY).getOrderId()}))),_=()=>{const t=e?.billingAddress;return!!t&&!!(t.email&&t.first_name&&t.last_name&&t.address_1&&t.city&&t.postcode&&t.country&&t.state&&t.city)},{isInitialized:y,isLoading:g,clientToken:b,orderId:f,resetStates:k,containerRef:w}=(({settings:e,billing:t,shippingData:n,orderId:o,containerId:r="in-checkout-primer-sdk-container",paymentMethodId:a="monoova_card",hasRequiredInfo:s=!0})=>{const[l,d]=(0,i.useState)(!1),[u,m]=(0,i.useState)(null),[h,p]=(0,i.useState)(o||null),[_,y]=(0,i.useState)(!1),[g,b]=(0,i.useState)(null),{targetRef:f,containerElement:k,containerIdActive:w,isContainerInitialized:C,setContainerInitialized:v,setOrderData:E,getOrderData:I,showContainer:R,hideContainer:S}=((e,t)=>{const n=(0,i.useRef)(null),o=(0,i.useRef)(!1),r=`persistent-${e}-${t}`;return(0,i.useEffect)((()=>{const i=c.getOrCreateContainer(e,t);return i.element&&(i.element.id=r),n.current&&!o.current&&(c.showContainer(e,t,n.current),o.current=!0),()=>{o.current&&(c.hideContainer(e,t),o.current=!1)}}),[e,t]),{targetRef:n,containerElement:c.getContainerElement(e,t),containerIdActive:r,isContainerInitialized:c.isContainerInitialized(e,t),setContainerInitialized:()=>c.setContainerInitialized(e,t),setOrderData:(n,o,i=null)=>c.setOrderData(e,t,n,o,i),getOrderData:()=>c.getOrderData(e,t),clearOrderData:()=>{c.clearOrderData(e,t)},showContainer:()=>{n.current&&!o.current&&(c.showContainer(e,t,n.current),o.current=!0)},hideContainer:()=>{o.current&&(c.hideContainer(e,t),o.current=!1)}}})(a,r),x=(0,i.useRef)(!1),z=(0,i.useRef)(!1),P=C&&k,$=I(),D=$?.orderId,T=$?.clientToken,A=(0,i.useCallback)((()=>{d(!1),m(null),p(null),y(!1),b(null),x.current=!1,z.current=!1}),[]),O=(0,i.useCallback)((()=>new Promise(((e,t)=>{if(window.Primer&&document.querySelector('link[href="https://sdk.primer.io/web/v2.54.5/Checkout.css"]')&&document.querySelector('script[src="https://sdk.primer.io/web/v2.54.5/Primer.min.js"]'))return void e();const n=document.createElement("link");n.rel="stylesheet",n.href="https://sdk.primer.io/web/v2.54.5/Checkout.css",document.head.appendChild(n);const o=document.createElement("script");o.src="https://sdk.primer.io/web/v2.54.5/Primer.min.js",o.crossOrigin="anonymous",o.onload=e,o.onerror=t,document.head.appendChild(o)}))),[]),L=(0,i.useCallback)((async()=>{if(x.current)return null;if(u||T){const e=u||T,t=o||h||D;return!u&&T&&m(T),!h&&t&&p(t),{token:e,orderId:t,checkoutData:null}}const n=o||h||D;if(!n)throw new Error("No order ID available for token generation");try{x.current=!0;const o=e=>({first_name:e?.first_name||"",last_name:e?.last_name||"",company:e?.company||"",address_1:e?.address_1||"",address_2:e?.address_2||"",city:e?.city||"",state:e?.state||"",postcode:e?.postcode||"",country:e?.country||"",email:e?.email||"",phone:e?.phone||""}),i=t?.billingAddress||{},r=await fetch(e.ajax_url,{method:"POST",body:new URLSearchParams({action:"monoova_get_client_token",_wpnonce:e.generate_token_nonce,order_id:n,is_in_checkout:!!document.body.classList.contains("woocommerce-checkout"),billingAddress:JSON.stringify(o(i)),card_type:"visa"})}),a=await r.json();if(!a.success)throw new Error(a.data?.message||"Failed to generate client token");const{clientToken:s}=a.data;return m(s),p(n),E(n,s),{token:s,orderId:n,checkoutData:a.data}}catch(e){throw console.error("Error generating client token:",e),e}finally{x.current=!1}}),[e,t,n,u,h,o,T,D]),M=(0,i.useCallback)((async(t,n)=>{try{const o=t?.payment?.id;if(!o)throw new Error("Payment ID not received from Primer");const i=I(),r=h||i?.orderId;if(!r)throw new Error("Order ID not available for payment completion");const a=await fetch(e.ajax_url,{method:"POST",body:new URLSearchParams({action:e.ajax_complete_express_action,nonce:e.express_checkout_nonce,orderId:r,primerPaymentId:o,clientRef:n?.clientTransactionUniqueReference||"",is_in_checkout:!!document.body.classList.contains("woocommerce-checkout")})}),s=await a.json();if(!s.success)throw new Error(s.data?.message||"Payment completion failed");window.location.href=s.data.redirect_url}catch(t){console.error("Error completing payment:",t),alert(t.message||e.i18n?.generic_error||"Payment completion failed"),A()}}),[e,h,u,I,A]),N=(0,i.useCallback)((async(t,n)=>{if(!z.current&&!C&&!l)try{z.current=!0,await O();const{Primer:o}=window;if(!o)throw new Error("Primer SDK not loaded");const i={container:`#${w||r}`,clientSessionCachingEnabled:!0,style:{inputLabel:{fontFamily:e.checkout_ui_styles?.input_label?.font_family||"Helvetica, Arial, sans-serif",fontSize:e.checkout_ui_styles?.input_label?.font_size||"14px",fontWeight:e.checkout_ui_styles?.input_label?.font_weight||"normal",color:e.checkout_ui_styles?.input_label?.color||"#000000"},input:{base:{fontFamily:e.checkout_ui_styles?.input?.font_family||"Helvetica, Arial, sans-serif",fontSize:e.checkout_ui_styles?.input?.font_size||"14px",fontWeight:e.checkout_ui_styles?.input?.font_weight||"normal",background:e.checkout_ui_styles?.input?.background_color||"#FAFAFA",borderColor:e.checkout_ui_styles?.input?.border_color||"#E8E8E8",borderRadius:e.checkout_ui_styles?.input?.border_radius||"8px",color:e.checkout_ui_styles?.input?.text_color||"#000000"},focus:{borderColor:"#2ab5c4",boxShadow:"0 0 0 2px rgba(42, 181, 196, 0.2)"},error:{borderColor:"#d63638",color:"#d63638"}},submitButton:{base:{color:e.checkout_ui_styles?.submit_button?.text_color||"#000000",background:e.checkout_ui_styles?.submit_button?.background||"#2ab5c4",borderRadius:e.checkout_ui_styles?.submit_button?.border_radius||"10px",borderColor:e.checkout_ui_styles?.submit_button?.border_color||"#2ab5c4",fontFamily:e.checkout_ui_styles?.submit_button?.font_family||"Helvetica, Arial, sans-serif",fontSize:e.checkout_ui_styles?.submit_button?.font_size||"17px",fontWeight:e.checkout_ui_styles?.submit_button?.font_weight||"bold",boxShadow:"none"},disabled:{color:"#9b9b9b",background:"#e1deda"}},loadingScreen:{color:e.checkout_ui_styles?.submit_button?.background||"#2ab5c4"}},errorMessage:{disabled:!0},successScreen:!1,onCheckoutComplete:e=>{console.log("Primer checkout complete:",e),M(e,n)},onCheckoutFail:(t,{payment:n})=>{console.error("Primer checkout failed:",t,n);let o=e.i18n?.generic_error||"Payment failed";t&&t.message?o=t.message:n&&n.processor&&n.processor.message&&(o=n.processor.message),alert(o),A()},onCheckoutCancel:()=>{console.log("Primer checkout cancelled"),A()}};await o.showUniversalCheckout(t,i),d(!0),v(),y(!1)}catch(t){console.error("Error initializing Primer checkout:",t),y(!1),alert(t.message||e.i18n?.generic_error||"Failed to initialize payment form")}finally{z.current=!1}}),[e,r,O,M,A,l,w,v]);return(0,i.useEffect)((()=>{const e=setTimeout((async()=>{if(P)return d(!0),y(!1),T&&!u&&m(T),D&&!h&&p(D),void R();if(l||_||x.current||z.current)return;if(!o)return;if(!s)return;const e=t?.billingAddress?`${t.billingAddress.email}_${t.billingAddress.first_name}_${t.billingAddress.last_name}`:null;if((t?.billingAddress||document.body.classList.contains("woocommerce-checkout"))&&e!==g)try{y(!0),b(e);const t=await L();t&&t.token?await N(t.token,t.checkoutData):(console.warn("No token received from order creation"),y(!1))}catch(e){console.error("Error during payment initialization:",e),y(!1),alert(e.message||"Failed to initialize payment form.")}}),100);return()=>clearTimeout(e)}),[o,s,P,R,D,T]),{isInitialized:l||P,isLoading:_,clientToken:u||T,orderId:o||h||D,resetStates:A,containerRef:f}})({settings:l,billing:e,shippingData:n,orderId:p,containerId:"in-checkout-primer-sdk-container",paymentMethodId:"monoova_card",hasRequiredInfo:_()});return(0,i.useEffect)((()=>{const e=document.querySelector(".wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button");return e&&(e.style.display="none"),()=>{e&&(e.style.display="")}}),[]),React.createElement("div",null,React.createElement("div",{className:"monoova-card-content",dangerouslySetInnerHTML:{__html:h}}),!_()&&!y&&!g&&React.createElement("div",{className:"monoova-card-info"},React.createElement("p",null,"Please complete your billing information to initialize the payment form."),React.createElement("small",null,"Required: Email, Name, Address, City, Postcode, and Country")),React.createElement(s,{containerId:"in-checkout-primer-sdk-container",isInitialized:y,isLoading:g,containerRef:w}))},h=e=>{const{PaymentMethodLabel:t}=e.components||{};return React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"}},React.createElement("span",{style:{fontWeight:600}},u),React.createElement("img",{src:`${l.plugin_url||"/wp-content/plugins/monoova-payments-for-woocommerce/"}assets/images/card-payment-method-types.svg`,alt:"Card payment methods",style:{height:"32px",maxHeight:"32px",width:"auto"}}))},p=()=>{const e=(0,t.decodeEntities)(l.description||"");return React.createElement("div",null,React.createElement("div",{className:"monoova-card-content",dangerouslySetInnerHTML:{__html:e}}),React.createElement("div",{className:"embedded-card-form"},React.createElement("div",{id:"in-checkout-primer-sdk-container"})))},_={name:"monoova_card",label:React.createElement(h,null),content:React.createElement(m,null),edit:React.createElement(p,null),canMakePayment:()=>!0,ariaLabel:u,supports:{features:l?.supports??[]},icons:l?.icons??null};try{(0,e.registerPaymentMethod)(_)}catch(e){console.error("Failed to register Monoova Card payment method:",e)}}})();