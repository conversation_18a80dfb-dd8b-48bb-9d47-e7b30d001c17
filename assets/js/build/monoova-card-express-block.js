(()=>{"use strict";const e=wc.wcBlocksRegistry,o=window.wp.htmlEntities,t=wc.wcSettings,n=window.wp.i18n,r=window.wp.element,s=window.wp.data,i=wc.wcBlocksData;if(void 0===e.registerExpressPaymentMethod)console.warn("WooCommerce Blocks registerExpressPaymentMethod is not available. This may be due to edit mode or missing dependencies.");else if(window.wp&&window.wp.element)if(void 0===r.createElement||void 0===r.useState||void 0===r.useCallback){console.error("Required React hooks are not available");const e=window.wp.element;e&&e.createElement&&e.useState&&e.useCallback?(e.createElement,e.useState,e.useCallback,a()):console.error("Fallback wp.element also not available")}else a();else console.error("WordPress element library is not available");function a(){let a={};try{if(void 0===t.getSetting)throw new Error("getSetting function not available");a=(0,t.getSetting)("monoova_card_express_data",{})}catch(e){console.error("getSetting not available, trying fallback:",e),a=window.monoova_card_express_blocks_params||{}}a&&"object"==typeof a&&0!==Object.keys(a).length||(console.warn("Monoova Card Express settings not found or empty, using defaults"),a={title:"Monoova Express Checkout",description:"Pay quickly and securely with Monoova Express Checkout.",supports:["products"],style:["height","borderRadius"],is_available:!1,user_logged_in:!1,button_style:{height:48,borderRadius:4,color:"primary"},i18n:{express_pay_button:"Pay with Monoova",processing:"Processing payment...",generic_error:"An error occurred while processing your payment. Please try again.",login_required:"Please log in to use express checkout."}});const c=(0,n.__)("Monoova Express Checkout","monoova-payments-for-woocommerce"),l={name:"monoova_card_express",title:(0,o.decodeEntities)(a.title)||c,description:(0,o.decodeEntities)(a.description||""),gatewayId:a.gatewayId||"monoova_card",content:(0,r.createElement)((({onClick:e,onClose:o,buttonAttributes:t,billing:n,shippingData:c,cartData:l,emitResponse:d,eventRegistration:u,activePaymentMethod:m,checkoutStatus:p,paymentStatus:h,...y})=>{const[b,_]=(0,r.useState)(!1),[g,w]=(0,r.useState)(null),{orderId:k}=(0,s.useSelect)((e=>({orderId:e(i.CHECKOUT_STORE_KEY).getOrderId()}))),f=e=>({first_name:e.first_name||"",last_name:e.last_name||"",company:e.company||"",address_1:e.address_1||"",address_2:e.address_2||"",city:e.city||"",state:e.state||"",postcode:e.postcode||"",country:e.country||"",email:e.email||"",phone:e.phone||""}),x={height:t?.height||a.button_style?.height||48,borderRadius:t?.borderRadius||a.button_style?.borderRadius||4,backgroundColor:"primary"===a.button_style?.color?"#2271b1":"#000",color:"#fff",border:"none",fontSize:"16px",fontWeight:"600",cursor:"pointer",width:"100%",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",padding:"0 16px",boxSizing:"border-box",transition:"all 0.2s ease"},v=(0,r.useCallback)((async()=>{if(!b)try{_(!0),e();const t=document.body.classList.contains("woocommerce-checkout")||window.location.pathname.includes("/checkout/")||null!==document.querySelector(".woocommerce-checkout"),r=n?.billingAddress||{},s=c?.shippingAddress||r,i=await fetch(a.ajax_url,{method:"POST",body:new URLSearchParams({action:a.ajax_express_checkout_action||"monoova_express_checkout",nonce:a.express_checkout_nonce,orderId:k,billingAddress:JSON.stringify(f(r)),shippingAddress:JSON.stringify(f(s)),shippingOption:"",cardType:"visa",isCheckoutPage:t})});if(!i.ok)throw _(!1),o(),new Error("Network response was not ok");const l=await i.json();if(!l.success)throw _(!1),o(),new Error(l.data?.message||a.i18n?.generic_error||"Payment failed");w(l.data),await P(l.data)}catch(e){console.error("Express payment error:",e),_(!1),o(),alert(e.message||a.i18n?.generic_error||"Payment failed")}}),[b,e,o,l,n,c]),P=async e=>{try{if(window.Primer||await C(),!e.clientToken||!e.orderId)throw new Error("Missing required payment data from server");const t=document.createElement("dialog");t.id="primer-express-checkout-dialog";const n=document.createElement("style");n.textContent="\n                    #primer-express-checkout-dialog {\n                        border: none;\n                        border-radius: 8px;\n                        padding: 20px;\n                        width: 100%;\n                        max-width: 500px;\n                        box-shadow: 0 5px 15px rgba(0,0,0,0.3);\n                        min-height: 300px;\n                        max-height: 90vh;\n                        overflow-x: hidden;\n                    }\n                    #primer-express-checkout-dialog::backdrop {\n                        background: rgba(0, 0, 0, 0.8);\n                    }\n                    /* only hide #primer-checkout-other-payment-methods (inside #primer-express-checkout) if it doesn't include class PrimerCheckout--enter and PrimerCheckout--entered */\n                    /* Hide other payment methods if not in enter state */\n                    #primer-express-checkout #primer-checkout-other-payment-methods:not(.PrimerCheckout--enter) {\n                        display: none;\n                    }\n                ",document.head.appendChild(n);const r=()=>{document.body.contains(t)&&(t.close(),document.head.removeChild(n),document.body.removeChild(t)),_(!1),o()},s=document.createElement("button");s.innerHTML="×",s.style.cssText="\n                    position: absolute;\n                    top: 10px;\n                    right: 15px;\n                    background: none;\n                    border: none;\n                    font-size: 24px;\n                    cursor: pointer;\n                    color: #666;\n                    z-index: 1;\n                ",s.onclick=()=>{r()};const i=document.createElement("div");i.id="primer-express-checkout",i.style.paddingTop="20px",t.appendChild(s),t.appendChild(i),document.body.appendChild(t),t.showModal();const c={container:"#primer-express-checkout",clientSessionCachingEnabled:!1,style:{inputLabel:{fontFamily:a.checkout_ui_styles?.input_label?.font_family||"Helvetica, Arial, sans-serif",fontSize:a.checkout_ui_styles?.input_label?.font_size||"14px",fontWeight:a.checkout_ui_styles?.input_label?.font_weight||"normal",color:a.checkout_ui_styles?.input_label?.color||"#000000"},input:{base:{fontFamily:a.checkout_ui_styles?.input?.font_family||"Helvetica, Arial, sans-serif",fontSize:a.checkout_ui_styles?.input?.font_size||"14px",fontWeight:a.checkout_ui_styles?.input?.font_weight||"normal",background:a.checkout_ui_styles?.input?.background_color||"#FAFAFA",borderColor:a.checkout_ui_styles?.input?.border_color||"#E8E8E8",borderRadius:a.checkout_ui_styles?.input?.border_radius||"8px",color:a.checkout_ui_styles?.input?.text_color||"#000000"}},submitButton:{base:{color:a.checkout_ui_styles?.submit_button?.text_color||"#000000",background:a.checkout_ui_styles?.submit_button?.background||"#2ab5c4",borderRadius:a.checkout_ui_styles?.submit_button?.border_radius||"10px",borderColor:a.checkout_ui_styles?.submit_button?.border_color||"#2ab5c4",fontFamily:a.checkout_ui_styles?.submit_button?.font_family||"Helvetica, Arial, sans-serif",fontSize:a.checkout_ui_styles?.submit_button?.font_size||"17px",fontWeight:a.checkout_ui_styles?.submit_button?.font_weight||"bold",boxShadow:"none"},disabled:{color:"#9b9b9b",background:"#e1deda"}},loadingScreen:{color:a.checkout_ui_styles?.submit_button?.background||"#2ab5c4"}},errorMessage:{disabled:!0},successScreen:!1,onCheckoutComplete:async o=>{r();const t=o?.payment?.id;if(!t)throw new Error("Payment ID not received from Primer");await E(o,e)},onCheckoutFail:(e,{payment:o})=>{console.error("Primer express checkout failed:",e,o),r();let t=a.i18n?.generic_error||"Payment failed";e&&e.message?t=e.message:o&&o.processor&&o.processor.message&&(t=o.processor.message),alert(t)},onCheckoutCancel:()=>{console.log("Primer express checkout cancelled"),r()},onAuthorizationFailed:e=>{console.log("Primer express checkout authorization failed:",e),r(),alert(a.i18n?.auth_failed||"Payment authorization failed")},onPaymentFailed:e=>{console.log("Primer express checkout payment failed:",e),r(),alert(a.i18n?.payment_failed||"Payment failed")}};await window.Primer.showUniversalCheckout(e.clientToken,c)}catch(e){console.error("Primer express checkout initialization error:",e),_(!1),o(),alert(a.i18n?.generic_error||"Payment failed")}},E=async(e,t)=>{try{const o=e?.payment?.id,n=t?.clientTransactionUniqueReference||"",r=t?.orderId;if(!r)throw new Error("Order ID not available");const s=await fetch(a.ajax_url,{method:"POST",body:new URLSearchParams({action:a.ajax_complete_express_action||"monoova_complete_express_checkout",nonce:a.express_checkout_nonce,orderId:r,primerPaymentId:o,clientRef:n})}),i=await s.json();if(!i.success)throw new Error(i.data?.message||"Payment completion failed");window.location.href=i.data.redirect_url}catch(e){console.error("Express payment completion error:",e),_(!1),o(),alert(e.message||a.i18n?.generic_error||"Payment completion failed")}},C=()=>new Promise(((e,o)=>{if(window.Primer)return void e();const t=document.createElement("link");t.rel="stylesheet",t.href="https://sdk.primer.io/web/v2.54.5/Checkout.css",document.head.appendChild(t);const n=document.createElement("script");n.src="https://sdk.primer.io/web/v2.54.5/Primer.min.js",n.crossOrigin="anonymous",n.onload=e,n.onerror=o,document.head.appendChild(n)}));return(0,r.createElement)("button",{style:x,onClick:v,disabled:b,className:"monoova-express-pay-button"},[(0,r.createElement)("span",{key:"text"},b?a.i18n?.processing||"Processing...":a.i18n?.express_pay_button||"Pay with Monoova")])})),edit:(0,r.createElement)((({buttonAttributes:e})=>{const o={height:e?.height||a.button_style?.height||48,borderRadius:e?.borderRadius||a.button_style?.borderRadius||4,backgroundColor:"#ccc",color:"#666",border:"none",fontSize:"16px",fontWeight:"600",cursor:"not-allowed",width:"100%",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",padding:"0 16px",boxSizing:"border-box"};return(0,r.createElement)("button",{style:o,disabled:!0,className:"monoova-express-pay-button monoova-express-pay-button--preview"},[a.icons?.[0]&&(0,r.createElement)("img",{key:"logo",src:a.icons[0],alt:"Monoova",style:{height:"20px",width:"auto"}}),(0,r.createElement)("span",{key:"text"},a.i18n?.express_pay_button||"Pay with Monoova")])})),canMakePayment:({cart:e,cartTotals:o,cartNeedsShipping:t,shippingAddress:n,billingAddress:r,selectedShippingMethods:s,paymentRequirements:i})=>{const a=document.body.classList.contains("woocommerce-checkout")||window.location.pathname.includes("/checkout/")||null!==document.querySelector(".woocommerce-checkout")||null!==document.querySelector(".wp-block-woocommerce-checkout");return(document.body.classList.contains("woocommerce-cart")||window.location.pathname.includes("/cart/")||null!==document.querySelector(".woocommerce-cart")||null!==document.querySelector(".wp-block-woocommerce-cart"))&&!a},paymentMethodId:"monoova_card",supports:{features:a?.supports??["products"],style:a?.style??["height","borderRadius"]}};try{(0,e.registerExpressPaymentMethod)(l)}catch(e){console.error("Failed to register Monoova Card Express payment method:",e)}}})();