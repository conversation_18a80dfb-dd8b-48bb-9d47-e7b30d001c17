(()=>{"use strict";const e=wc.wcBlocksRegistry,t=wc.wcSettings,n=window.wp.htmlEntities,a=window.wp.i18n,r=window.wp.element,i=window.wp.data,s=wc.wcBlocksData,o=new class{constructor(){this.containers=new Map,this.activeContainers=new Set,this.isInitialized=!1}getOrCreateContainer(e,t){const n=`${e}_${t}`;if(!this.containers.has(n)){const e=document.createElement("div");e.id=t,e.className="primer-checkout-persistent-container",e.style.cssText="\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            ",document.body.appendChild(e),this.containers.set(n,{element:e,isInitialized:!1,isVisible:!1,orderId:null,clientToken:null})}return this.containers.get(n)}showContainer(e,t,n){const a=`${e}_${t}`,r=this.containers.get(a);r&&r.element&&(this.hideAllContainers(),n&&(n.appendChild(r.element),r.element.style.cssText="\n                    position: relative;\n                    top: auto;\n                    left: auto;\n                    width: 100%;\n                    visibility: visible;\n                    opacity: 1;\n                    pointer-events: auto;\n                    transition: all 0.3s ease;\n                ",r.isVisible=!0,this.activeContainers.add(a)))}hideContainer(e,t){const n=`${e}_${t}`,a=this.containers.get(n);a&&a.element&&(document.body.appendChild(a.element),a.element.style.cssText="\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            ",a.isVisible=!1,this.activeContainers.delete(n))}hideAllContainers(){this.containers.forEach(((e,t)=>{e.isVisible&&this.hideContainer(t.split("_")[0],t.split("_")[1])}))}clearOrderData(e,t){const n=`${e}_${t}`,a=this.containers.get(n);a&&(a.orderId=null,a.clientToken=null,a.instructions=null,a.isInitialized=!1)}setContainerInitialized(e,t){const n=`${e}_${t}`,a=this.containers.get(n);a&&(a.isInitialized=!0)}isContainerInitialized(e,t){const n=`${e}_${t}`,a=this.containers.get(n);return!!a&&a.isInitialized}setOrderData(e,t,n,a,r=null){const i=`${e}_${t}`,s=this.containers.get(i);s?(s.orderId=n,s.clientToken=a,s.instructions=r):console.warn(`[PrimerCheckoutManager] Container ${i} not found for setting order data`)}getOrderData(e,t){const n=`${e}_${t}`,a=this.containers.get(n);return a?{orderId:a.orderId,clientToken:a.clientToken,instructions:a.instructions}:{orderId:null,clientToken:null,instructions:null}}getContainerElement(e,t){const n=`${e}_${t}`,a=this.containers.get(n);return a?a.element:null}cleanup(){this.containers.forEach((e=>{e.element&&e.element.parentNode&&e.element.parentNode.removeChild(e.element)})),this.containers.clear(),this.activeContainers.clear()}};"undefined"!=typeof window&&window.addEventListener("beforeunload",(()=>{o.cleanup()}));const c=window.wp.components,l=({initialSeconds:e=5,redirectUrl:t,message:n="Redirecting in {countdown} seconds..."})=>{const[a,i]=(0,r.useState)(e);(0,r.useEffect)((()=>{if(a<=0)return void(t&&(window.location.href=t));const e=setTimeout((()=>{i((e=>e-1))}),1e3);return()=>clearTimeout(e)}),[a,t]);const s=n.replace("{countdown}",a.toString());return React.createElement("div",{className:"monoova-navigation-countdown",style:{marginTop:"15px",padding:"10px",backgroundColor:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"4px",textAlign:"center",fontSize:"14px",color:"#6c757d"}},s)},d=({text:e,onCopy:t})=>{const[n,a]=(0,r.useState)(!1);return React.createElement("button",{type:"button",className:"monoova-copy-button",onClick:async()=>{try{await navigator.clipboard.writeText(e),a(!0),t&&t(),setTimeout((()=>a(!1)),2e3)}catch(e){console.error("Failed to copy:",e)}},title:n?"Copied!":"Copy"},React.createElement("svg",{width:"19",height:"18",viewBox:"0 0 19 18",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement("path",{d:"M12.4868 9.675V12.825C12.4868 15.45 11.4368 16.5 8.81182 16.5H5.66182C3.03682 16.5 1.98682 15.45 1.98682 12.825V9.675C1.98682 7.05 3.03682 6 5.66182 6H8.81182C11.4368 6 12.4868 7.05 12.4868 9.675Z",stroke:"#2CB5C5","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),React.createElement("path",{d:"M16.9868 5.175V8.325C16.9868 10.95 15.9368 12 13.3118 12H12.4868V9.675C12.4868 7.05 11.4368 6 8.81182 6H6.48682V5.175C6.48682 2.55 7.53682 1.5 10.1618 1.5H13.3118C15.9368 1.5 16.9868 2.55 16.9868 5.175Z",stroke:"#2CB5C5","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})))},m=({expiryTimestamp:e,onExpired:t,strings:n})=>{const[a,i]=(0,r.useState)("");return(0,r.useEffect)((()=>{if(!e||e<=0)return void i("");const n=()=>{const n=1e3*e-(new Date).getTime();if(n<0)return i(""),t&&t(),null;const a=Math.floor(n/864e5),r=Math.floor(n%864e5/36e5),s=Math.floor(n%36e5/6e4),o=Math.floor(n%6e4/1e3);let c=[];return a>0&&c.push(`${a} ${1===a?"day":"days"}`),r>0&&c.push(`${r} ${1===r?"hour":"hours"}`),s>0&&c.push(`${s} ${1===s?"minute":"minutes"}`),o>0&&c.push(`${o} ${1===o?"second":"seconds"}`),`(${c.join(" and ")} remaining)`},a=n();a&&i(a);const r=setInterval((()=>{const e=n();e?i(e):clearInterval(r)}),1e3);return()=>clearInterval(r)}),[e,t]),!a||e<Date.now()/1e3?null:React.createElement("div",{id:"monoova-expiry-info",className:"monoova-expiry-info"},React.createElement("span",{className:"monoova-expiry-label"},n.expires_in),React.createElement("span",{className:"monoova-expiry-time"},React.createElement("strong",null,new Date(1e3*e).toLocaleString()," ",a)))},u=({instructions:e,settings:t,paymentStatus:n="pending",expiryTime:a,paymentFailedReason:i=null,onPaymentStatusChange:s,onRegenerateInstructions:o,orderId:u=null})=>{const[p,y]=(0,r.useState)("payid");if(!e)return null;const{details:g,view_order_url:v}=e,f=t.payment_types.includes("payid")&&g.payid_value,h=t.payment_types.includes("bank_transfer")&&g.bank_bsb&&g.bank_account_number,E=f&&h;(0,r.useEffect)((()=>{f&&"payid"!==p&&"bank_transfer"!==p?y("payid"):!f&&h&&y("bank_transfer")}),[f,h,p]);const R=()=>{if(v)return v;const e=new URLSearchParams(window.location.search).get("key");let n=t.order_received_url;return n.includes("?")?n+=`&order-received=${u}`:n+=`?order-received=${u}`,e&&(n+=`&key=${e}`),n};return"pending"!==n&&"initial"!==n?React.createElement("div",{className:"monoova-payid-bank-transfer-instructions-wrapper"},React.createElement("div",{className:"monoova-instructions-container"},(()=>{switch(n){case"paid":return React.createElement("div",{id:"monoova-payment-confirmed",className:"monoova-payment-status"},React.createElement("div",null,React.createElement("svg",{width:"45",height:"46",viewBox:"0 0 45 46",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M31.8167 18.8513L21.0413 29.6267C20.6983 29.9697 20.2527 30.14 19.8047 30.14C19.3543 30.14 18.9087 29.9697 18.5657 29.6267L13.178 24.239C12.4943 23.5553 12.4943 22.447 13.178 21.7633C13.8617 21.0797 14.9677 21.0797 15.6513 21.7633L19.8047 25.9143L29.341 16.3757C30.0247 15.692 31.133 15.692 31.8167 16.3757C32.5003 17.0593 32.5003 18.1677 31.8167 18.8513ZM22.4997 0.833344C10.2777 0.833344 0.333008 10.778 0.333008 23C0.333008 35.2243 10.2777 45.1667 22.4997 45.1667C34.7217 45.1667 44.6663 35.2243 44.6663 23C44.6663 10.778 34.7217 0.833344 22.4997 0.833344Z",fill:"#2CB5C5"}))),React.createElement("h3",{className:"title"},t.strings.payment_confirmed),React.createElement("p",null,t.strings.payment_confirmed_message),React.createElement("div",{className:"monoova-payment-status-actions"},React.createElement(c.Button,{variant:"primary",href:R()},t.strings.view_order_details)),React.createElement(l,{initialSeconds:5,redirectUrl:R(),message:t.strings.redirecting_to_order_page}));case"expired":return React.createElement("div",{id:"monoova-payment-expired",className:"monoova-payment-status"},React.createElement("div",null,React.createElement("svg",{width:"45",height:"46",viewBox:"0 0 45 46",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24.2647 31.617C24.2647 32.583 23.4807 33.367 22.5147 33.367C21.5487 33.367 20.7647 32.583 20.7647 31.617V21.586C20.7647 20.62 21.5487 19.836 22.5147 19.836C23.4807 19.836 24.2647 20.62 24.2647 21.586V31.617ZM20.7507 14.3456C20.7507 13.3796 21.5347 12.5956 22.5007 12.5956C23.4667 12.5956 24.2507 13.3796 24.2507 14.3456C24.2507 15.3116 23.4667 16.1703 22.5007 16.1703C21.5347 16.1703 20.7507 15.4586 20.7507 14.4926V14.3456ZM22.5007 0.833313C10.2787 0.833313 0.333984 10.7756 0.333984 23C0.333984 35.222 10.2787 45.1666 22.5007 45.1666C34.7227 45.1666 44.6673 35.222 44.6673 23C44.6673 10.7756 34.7227 0.833313 22.5007 0.833313Z",fill:"#FF782A"}))),React.createElement("h3",{className:"title"},t.strings.payment_expired),React.createElement("p",null,t.strings.payment_expired_message),React.createElement("div",{className:"monoova-payment-status-actions"},React.createElement(c.Button,{variant:"primary",onClick:()=>(o&&o(),void s("pending"))},t.strings.place_new_order)));case"failed":return React.createElement("div",{id:"monoova-payment-rejected",className:"monoova-payment-status"},React.createElement("div",null,React.createElement("svg",{width:"45",height:"46",viewBox:"0 0 45 46",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24.2647 31.617C24.2647 32.583 23.4807 33.367 22.5147 33.367C21.5487 33.367 20.7647 32.583 20.7647 31.617V21.586C20.7647 20.62 21.5487 19.836 22.5147 19.836C23.4807 19.836 24.2647 20.62 24.2647 21.586V31.617ZM20.7507 14.3456C20.7507 13.3796 21.5347 12.5956 22.5007 12.5956C23.4667 12.5956 24.2507 13.3796 24.2507 14.3456C24.2507 15.3116 23.4667 16.1703 22.5007 16.1703C21.5347 16.1703 20.7507 15.4586 20.7507 14.4926V14.3456ZM22.5007 0.833313C10.2787 0.833313 0.333984 10.7756 0.333984 23C0.333984 35.222 10.2787 45.1666 22.5007 45.1666C34.7227 45.1666 44.6673 35.222 44.6673 23C44.6673 10.7756 34.7227 0.833313 22.5007 0.833313Z",fill:"#FF0000"}))),React.createElement("h3",{className:"title"},t.strings.payment_failed),React.createElement("p",null,t.strings.payment_failed_message),React.createElement("p",{id:"monoova-rejection-reason",style:{fontStyle:"italic",marginTop:15}},"Reason: ",React.createElement("span",{dangerouslySetInnerHTML:{__html:i}})),React.createElement("div",{className:"monoova-payment-status-actions"},React.createElement(c.Button,{variant:"primary",onClick:()=>{s("pending")}},t.strings.try_again)));default:return null}})())):React.createElement("div",{className:"monoova-payid-bank-transfer-instructions-wrapper"},E&&React.createElement("div",{className:"monoova-instruction-method-selection"},React.createElement("div",{style:{fontWeight:600}},t.strings.pay_with),React.createElement(c.RadioControl,{selected:p,options:[{label:t.strings.payid_method,value:"payid"},{label:t.strings.bank_method,value:"bank_transfer"}],onChange:e=>y(e)})),React.createElement("div",null,t.strings.payment_instructions_description),React.createElement("div",{className:"monoova-instructions-container"},React.createElement("div",{id:"monoova-payment-pending"},t.instructions&&React.createElement("p",{dangerouslySetInnerHTML:{__html:t.instructions}}),"payid"===p&&f&&React.createElement("div",{id:"monoova-payid-view"},React.createElement("div",{className:"monoova-scan-pay"},React.createElement("div",{className:"pay-label"},t.strings.scan_pay),React.createElement("div",{className:"amount",dangerouslySetInnerHTML:{__html:g.amount_to_pay_formatted}})),React.createElement("p",null,t.strings.scan_pay_description),React.createElement("div",{className:"monoova-manual-pay"},React.createElement("img",{src:`${t.plugin_url}assets/images/payid-logo.svg`,alt:"PayID",className:"payid-logo"}),React.createElement("span",{className:"copy-target","data-copy-id":"payid"},g.payid_value),React.createElement(d,{text:g.payid_value})),t.show_reference_field&&React.createElement("div",{className:"monoova-payment-reference-section"},React.createElement("h4",null,t.strings.payment_reference),React.createElement("p",null,t.strings.include_reference_with_payment),React.createElement("div",{className:"monoova-reference-field full-width"},React.createElement("div",{className:"field-label"},t.strings.reference),React.createElement("div",{className:"field-value-wrapper"},React.createElement("span",{className:"copy-target","data-copy-id":"reference"},g.reference+"P"," "),React.createElement(d,{text:g.reference+"P"}))))),"bank_transfer"===p&&h&&React.createElement("div",{id:"monoova-bank-view"},React.createElement("h3",null,t.strings.bank_transfer_details),React.createElement("div",{className:"monoova-bank-field-grid"},React.createElement("div",{className:"monoova-bank-field"},React.createElement("div",{className:"field-label"},t.strings.account_name),React.createElement("div",{className:"field-value-wrapper"},React.createElement("span",{className:"copy-target","data-copy-id":"account-name"},g.bank_account_name))),React.createElement("div",{className:"monoova-bank-field"},React.createElement("div",{className:"field-label"},t.strings.bsb),React.createElement("div",{className:"field-value-wrapper"},React.createElement("span",{className:"copy-target","data-copy-id":"bsb"},g.bank_bsb),React.createElement(d,{text:g.bank_bsb})))),React.createElement("div",{className:"monoova-bank-field full-width"},React.createElement("div",{className:"field-label"},t.strings.account_number),React.createElement("div",{className:"field-value-wrapper"},React.createElement("span",{className:"copy-target","data-copy-id":"account-number"},g.bank_account_number),React.createElement(d,{text:g.bank_account_number}))),t.show_reference_field&&React.createElement("div",{className:"monoova-payment-reference-section"},React.createElement("h4",null,t.strings.payment_reference),React.createElement("p",null,t.strings.include_reference_with_payment),React.createElement("div",{className:"monoova-bank-field full-width"},React.createElement("div",{className:"field-label"},t.strings.reference),React.createElement("div",{className:"field-value-wrapper"},React.createElement("span",{className:"copy-target","data-copy-id":"reference"},g.reference+"B"," "),React.createElement(d,{text:g.reference+"B"}))))),React.createElement("div",{className:"monoova-confirmation-text"},React.createElement("p",null,t.strings.payment_confirmed_automatically)),a&&React.createElement(m,{expiryTimestamp:a,onExpired:()=>{s&&s("expired")},strings:t.strings}))))};if("function"!=typeof e.registerPaymentMethod)console.warn("Monoova PayID Block: registerPaymentMethod not available. Available globals:",Object.keys(window.wc||{}));else{let c={};if("function"==typeof t.getSetting)try{c=(0,t.getSetting)("monoova_payid_data",{})}catch(e){console.log("Monoova PayID Block: getSetting failed:",e)}const l=(0,a.__)("PayID / Bank Transfer","monoova-payments-for-woocommerce"),d=(0,n.decodeEntities)(c.title)||l,m=({billing:e,shippingData:t,checkoutStatus:a,paymentStatus:l,eventRegistration:d,emitResponse:m})=>{const[p,y]=(0,r.useState)("pending"),{orderId:g}=(0,i.useSelect)((e=>({orderId:e(s.CHECKOUT_STORE_KEY).getOrderId()}))),v=()=>{const t=e?.billingAddress;return!!t&&!!(t.email&&t.first_name&&t.last_name&&t.address_1&&t.city&&t.postcode&&t.country)},{isLoading:f,instructions:h,error:E,paymentStatus:R,paymentFailedReason:_,expiryTime:w,resetStates:C,regeneratePaymentInstructions:b,resetPaymentStatus:I,containerRef:k,isInitialized:N}=(({settings:e,billing:t,shippingData:n,orderId:a,containerId:i="payid-instructions-container",paymentMethodId:s="monoova_payid",hasRequiredInfo:c=!0})=>{const[l,d]=(0,r.useState)(!1),[m,u]=(0,r.useState)(null),[p,y]=(0,r.useState)(null),[g,v]=(0,r.useState)(a||null),[f,h]=(0,r.useState)("pending"),[E,R]=(0,r.useState)(null),[_,w]=(0,r.useState)(null),C=(0,r.useRef)(!1),b=(0,r.useRef)(null),I=(0,r.useRef)(null),{targetRef:k,containerElement:N,containerIdActive:x,isContainerInitialized:S,setContainerInitialized:D,setOrderData:T,getOrderData:P,clearOrderData:$,showContainer:M,hideContainer:O}=((e,t)=>{const n=(0,r.useRef)(null),a=(0,r.useRef)(!1),i=`persistent-${e}-${t}`;return(0,r.useEffect)((()=>{const r=o.getOrCreateContainer(e,t);return r.element&&(r.element.id=i),n.current&&!a.current&&(o.showContainer(e,t,n.current),a.current=!0),()=>{a.current&&(o.hideContainer(e,t),a.current=!1)}}),[e,t]),{targetRef:n,containerElement:o.getContainerElement(e,t),containerIdActive:i,isContainerInitialized:o.isContainerInitialized(e,t),setContainerInitialized:()=>o.setContainerInitialized(e,t),setOrderData:(n,a,r=null)=>o.setOrderData(e,t,n,a,r),getOrderData:()=>o.getOrderData(e,t),clearOrderData:()=>{o.clearOrderData(e,t)},showContainer:()=>{n.current&&!a.current&&(o.showContainer(e,t,n.current),a.current=!0)},hideContainer:()=>{a.current&&(o.hideContainer(e,t),a.current=!1)}}})(s,i),B=P(),L=B?.instructions,z=B?.orderId,F=S&&L,V=(0,r.useCallback)((()=>{b.current&&(clearInterval(b.current),b.current=null)}),[]),H=(0,r.useCallback)((async(t=!1)=>{d(!0),y(null),C.current=!0;try{console.log(`PayID Instructions: ${t?"Regenerating":"Generating"} payment instructions for order:`,a);const n=new FormData;n.append("action","monoova_generate_payment_instructions_in_blocked_checkout"),n.append("order_id",a),n.append("nonce",e.generate_instructions_nonce),t&&n.append("regenerate","true");const r=await fetch(e.ajax_url,{method:"POST",body:n}),i=await r.json();if(!i.success)throw new Error(i.data?.message||`Failed to ${t?"regenerate":"generate"} payment instructions`);const s=i.data;u(s),v(a),s.expiry_timestamp&&w(s.expiry_timestamp),T(a,null,s),D()}catch(e){console.error(`PayID Instructions: Error ${t?"regenerating":"generating"} instructions:`,e),y(e.message)}finally{d(!1),C.current=!1}}),[a,e,T,D]),j=(0,r.useCallback)((async()=>{C.current||m||L||a&&c&&await H(!1)}),[a,c,m,L,H]),A=(0,r.useCallback)((async()=>{V(),I.current&&(clearInterval(I.current),I.current=null),u(null),h("pending"),R(null),w(null),$&&$(),await H(!0)}),[a,$,V,H]),Z=(0,r.useCallback)((async()=>{const t=g||z||a;if(!t)return;const n=m||L,r=n?.details?.order_key,i=n?.status_check_nonce;if(r&&i)try{const n=new FormData;n.append("action","monoova_check_payment_status"),n.append("order_id",t),n.append("order_key",r),n.append("nonce",i);const a=await fetch(e.ajax_url,{method:"POST",body:n}),s=await a.json();s.success&&"pending"!==s.data.status&&(h(s.data.status),R(s.data.reason||null),V())}catch(e){console.error("PayID Instructions: Error checking payment status:",e)}else console.warn("PayID Instructions: Missing order key or nonce for status check")}),[g,z,a,m,L,e,V]),q=(0,r.useCallback)((()=>{b.current||(Z(),b.current=setInterval(Z,1e4))}),[Z]),U=(0,r.useCallback)((()=>{_&&!I.current&&(I.current=setInterval((()=>{1e3*_-(new Date).getTime()<0&&(h("expired"),clearInterval(I.current),I.current=null,V())}),1e3))}),[_,V]),G=(0,r.useCallback)((async()=>{const t=g||z||a;if(t)try{const n=m||L,a=n?.details?.order_key;if(!a)return void console.warn("PayID Instructions: Missing order key for status reset");const r=new FormData;r.append("action","monoova_reset_payment_status"),r.append("order_id",t),r.append("order_key",a),r.append("nonce",e.reset_payment_status_nonce);const i=await fetch(e.ajax_url,{method:"POST",body:r}),s=await i.json();if(!s.success)throw console.error("PayID Instructions: Failed to reset payment status:",s.data?.message),new Error(s.data?.message||"Failed to reset payment status");h("pending"),R(null),console.log("PayID Instructions: Payment status reset successfully")}catch(e){console.error("PayID Instructions: Error resetting payment status:",e),y(e.message)}else console.warn("PayID Instructions: No order ID available for payment status reset")}),[g,z,a,m,L,e]),K=(0,r.useCallback)((()=>{u(null),v(null),y(null),d(!1),h("pending"),w(null),C.current=!1,V(),I.current&&(clearInterval(I.current),I.current=null)}),[V]);return(0,r.useEffect)((()=>{const e=setTimeout((async()=>{if(F)return u(L),v(z||a),M(),void(L?.expiry_timestamp&&w(L.expiry_timestamp));!a||!c||l||m||C.current||(await j(),M())}),100);return()=>clearTimeout(e)}),[a,c,F,L,z,l,m,j,M]),(0,r.useEffect)((()=>((m||L)&&"pending"===f&&(q(),U()),()=>{V(),I.current&&clearInterval(I.current)})),[m,L,f,q,U,V]),{isLoading:l,instructions:m||L,error:p,orderId:g||z||a,paymentStatus:f,paymentFailedReason:E,expiryTime:_,resetStates:K,regeneratePaymentInstructions:A,resetPaymentStatus:G,containerRef:k,isInitialized:F||!!m}})({settings:c,billing:e,shippingData:t,orderId:g,containerId:"payid-instructions-container",paymentMethodId:"monoova_payid",hasRequiredInfo:v()});(0,r.useEffect)((()=>{const e=document.querySelector(".wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button");return e&&(e.style.display="none"),()=>{e&&(e.style.display="")}}),[]),(0,r.useEffect)((()=>{"pending"!==R&&y(R)}),[R]);const x=(0,n.decodeEntities)(c.description||"");return f&&!h?React.createElement("div",{className:"monoova-payid-content"},React.createElement("div",{className:"monoova-payid-description",dangerouslySetInnerHTML:{__html:x}}),React.createElement("div",{className:"monoova-payid-loading"},React.createElement("div",{className:"spinner"}),React.createElement("p",null,c.strings?.loading||"Generating payment instructions..."))):v()||h?E?React.createElement("div",{className:"monoova-payid-content"},React.createElement("div",{className:"monoova-payid-description",dangerouslySetInnerHTML:{__html:x}}),React.createElement("div",{className:"monoova-payid-error"},React.createElement("p",null,E),React.createElement("button",{type:"button",onClick:C},"Try Again"))):React.createElement("div",{className:"monoova-payid-content"},React.createElement("div",{className:"monoova-payid-description",dangerouslySetInnerHTML:{__html:x}}),React.createElement("div",{ref:k,className:"payid-container-target"},h&&React.createElement(u,{instructions:h,settings:c,paymentStatus:p,expiryTime:w,paymentFailedReason:_,onPaymentStatusChange:e=>{"pending"===e?(y("pending"),I&&I()):y(e)},onRegenerateInstructions:b,orderId:g}))):React.createElement("div",{className:"monoova-payid-content"},React.createElement("div",{className:"monoova-payid-description",dangerouslySetInnerHTML:{__html:x}}),React.createElement("div",{className:"monoova-payid-info"},React.createElement("p",null,"Please complete your billing information to generate PayID payment instructions."),React.createElement("small",null,"Required: Email, Name, Address, City, Postcode, and Country")))},p=e=>{const{PaymentMethodLabel:t}=e.components||{};return React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"}},React.createElement("span",{style:{fontWeight:600}},d),React.createElement("img",{src:`${c.plugin_url||"/wp-content/plugins/monoova-payments-for-woocommerce/"}assets/images/payid-logo.svg`,alt:"PayID logo",style:{height:"24px",width:"auto"}}))},y=()=>{const e=(0,n.decodeEntities)(c.description||"");return React.createElement("div",{className:"monoova-payid-content"},React.createElement("div",{className:"monoova-payid-description",dangerouslySetInnerHTML:{__html:e}}),React.createElement("div",{className:"payid-instructions-preview"},React.createElement("div",{style:{border:"1px dashed #ccc",padding:"20px",textAlign:"center",borderRadius:"8px",color:"#666",margin:"15px 0"}},React.createElement("p",null,React.createElement("strong",null,"PayID / Bank Transfer Instructions")),React.createElement("p",null,"Payment instructions will appear here during checkout"))))},g={name:"monoova_payid",label:React.createElement(p,null),content:React.createElement(m,null),edit:React.createElement(y,null),canMakePayment:function(){return!0},ariaLabel:d,supports:{features:c.supports||[]},icons:c.icons||null};try{(0,e.registerPaymentMethod)(g)}catch(e){console.error("Monoova PayID Block: Failed to register payment method:",e)}}})();