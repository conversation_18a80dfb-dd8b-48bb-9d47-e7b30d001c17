/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./assets/js/src/blocks/NavigationCountdown.js":
/*!*****************************************************!*\
  !*** ./assets/js/src/blocks/NavigationCountdown.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NavigationCountdown: () => (/* binding */ NavigationCountdown)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);


/**
 * Navigation Countdown Component
 * 
 * Shows a countdown timer and redirects to a URL when it reaches 0
 * 
 * @param {Object} props
 * @param {number} props.initialSeconds - Starting countdown time in seconds
 * @param {string} props.redirectUrl - URL to redirect to when countdown reaches 0
 * @param {string} props.message - Message template with {countdown} placeholder
 */
const NavigationCountdown = ({
  initialSeconds = 5,
  redirectUrl,
  message = 'Redirecting in {countdown} seconds...'
}) => {
  const [countdown, setCountdown] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(initialSeconds);
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    if (countdown <= 0) {
      if (redirectUrl) {
        window.location.href = redirectUrl;
      }
      return;
    }
    const timer = setTimeout(() => {
      setCountdown(prev => prev - 1);
    }, 1000);
    return () => clearTimeout(timer);
  }, [countdown, redirectUrl]);
  const formattedMessage = message.replace('{countdown}', countdown.toString());
  return /*#__PURE__*/React.createElement("div", {
    className: "monoova-navigation-countdown",
    style: {
      marginTop: '15px',
      padding: '10px',
      backgroundColor: '#f8f9fa',
      border: '1px solid #dee2e6',
      borderRadius: '4px',
      textAlign: 'center',
      fontSize: '14px',
      color: '#6c757d'
    }
  }, formattedMessage);
};

/***/ }),

/***/ "./assets/js/src/blocks/PayIDInstructionsContainer.js":
/*!************************************************************!*\
  !*** ./assets/js/src/blocks/PayIDInstructionsContainer.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PayIDInstructionsContainer: () => (/* binding */ PayIDInstructionsContainer)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/components */ "@wordpress/components");
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _NavigationCountdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NavigationCountdown */ "./assets/js/src/blocks/NavigationCountdown.js");




// QR Code generation component (simplified version)
const QRCodeDisplay = ({
  payload,
  size = 180
}) => {
  const qrRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    if (!payload || !qrRef.current || typeof window.QRCode === 'undefined') return;

    // Clear any previous QR code
    qrRef.current.innerHTML = '';
    new window.QRCode(qrRef.current, {
      text: payload,
      width: size,
      height: size,
      colorDark: '#000000',
      colorLight: '#ffffff',
      correctLevel: window.QRCode?.CorrectLevel?.H
    });
  }, [payload, size]);
  return /*#__PURE__*/React.createElement("div", {
    ref: qrRef,
    id: "monoova-qr-code"
  });
};

// Copy button component
const CopyButton = ({
  text,
  onCopy
}) => {
  const [copied, setCopied] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      onCopy && onCopy();
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };
  return /*#__PURE__*/React.createElement("button", {
    type: "button",
    className: "monoova-copy-button",
    onClick: handleCopy,
    title: copied ? 'Copied!' : 'Copy'
  }, /*#__PURE__*/React.createElement("svg", {
    width: "19",
    height: "18",
    viewBox: "0 0 19 18",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, /*#__PURE__*/React.createElement("path", {
    d: "M12.4868 9.675V12.825C12.4868 15.45 11.4368 16.5 8.81182 16.5H5.66182C3.03682 16.5 1.98682 15.45 1.98682 12.825V9.675C1.98682 7.05 3.03682 6 5.66182 6H8.81182C11.4368 6 12.4868 7.05 12.4868 9.675Z",
    stroke: "#2CB5C5",
    "stroke-width": "1.5",
    "stroke-linecap": "round",
    "stroke-linejoin": "round"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M16.9868 5.175V8.325C16.9868 10.95 15.9368 12 13.3118 12H12.4868V9.675C12.4868 7.05 11.4368 6 8.81182 6H6.48682V5.175C6.48682 2.55 7.53682 1.5 10.1618 1.5H13.3118C15.9368 1.5 16.9868 2.55 16.9868 5.175Z",
    stroke: "#2CB5C5",
    "stroke-width": "1.5",
    "stroke-linecap": "round",
    "stroke-linejoin": "round"
  })));
};

// Countdown timer component
const CountdownTimer = ({
  expiryTimestamp,
  onExpired,
  strings
}) => {
  const [timeLeft, setTimeLeft] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)('');
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    if (!expiryTimestamp || expiryTimestamp <= 0) {
      setTimeLeft('');
      return;
    }
    const calculateTimeLeft = () => {
      const distance = expiryTimestamp * 1000 - new Date().getTime();
      if (distance < 0) {
        setTimeLeft('');
        onExpired && onExpired();
        return null;
      }
      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor(distance % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));
      const minutes = Math.floor(distance % (1000 * 60 * 60) / (1000 * 60));
      const seconds = Math.floor(distance % (1000 * 60) / 1000);
      let timeArr = [];
      if (days > 0) timeArr.push(`${days} ${days === 1 ? "day" : "days"}`);
      if (hours > 0) timeArr.push(`${hours} ${hours === 1 ? "hour" : "hours"}`);
      if (minutes > 0) timeArr.push(`${minutes} ${minutes === 1 ? "minute" : "minutes"}`);
      if (seconds > 0) timeArr.push(`${seconds} ${seconds === 1 ? "second" : "seconds"}`);
      return `(${timeArr.join(" and ")} remaining)`;
    };

    // Set initial value
    const initialTime = calculateTimeLeft();
    if (initialTime) {
      setTimeLeft(initialTime);
    }
    const interval = setInterval(() => {
      const newTime = calculateTimeLeft();
      if (newTime) {
        setTimeLeft(newTime);
      } else {
        clearInterval(interval);
      }
    }, 1000);
    return () => clearInterval(interval);
  }, [expiryTimestamp, onExpired]);
  if (!timeLeft || expiryTimestamp < Date.now() / 1000) return null;
  return /*#__PURE__*/React.createElement("div", {
    id: "monoova-expiry-info",
    className: "monoova-expiry-info"
  }, /*#__PURE__*/React.createElement("span", {
    className: "monoova-expiry-label"
  }, strings.expires_in), /*#__PURE__*/React.createElement("span", {
    className: "monoova-expiry-time"
  }, /*#__PURE__*/React.createElement("strong", null, new Date(expiryTimestamp * 1000).toLocaleString(), " ", timeLeft)));
};

// Main PayID instructions component
const PayIDInstructionsContainer = ({
  instructions,
  settings,
  paymentStatus = 'pending',
  expiryTime,
  paymentFailedReason = null,
  onPaymentStatusChange,
  onRegenerateInstructions,
  // New prop for regeneration
  orderId = null // Order ID for generating order received URL
}) => {
  const [selectedMethod, setSelectedMethod] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)('payid');
  if (!instructions) return null;
  const {
    details,
    view_order_url
  } = instructions;
  const showPayID = settings.payment_types.includes('payid') && details.payid_value;
  const showBank = settings.payment_types.includes('bank_transfer') && details.bank_bsb && details.bank_account_number;
  const showMethodSwitcher = showPayID && showBank;

  // Default to first available method
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    if (showPayID && selectedMethod !== 'payid' && selectedMethod !== 'bank_transfer') {
      setSelectedMethod('payid');
    } else if (!showPayID && showBank) {
      setSelectedMethod('bank_transfer');
    }
  }, [showPayID, showBank, selectedMethod]);
  const handleExpired = () => {
    onPaymentStatusChange && onPaymentStatusChange('expired');
  };
  const handlePlaceNewOrder = () => {
    if (onRegenerateInstructions) {
      onRegenerateInstructions();
    }
    onPaymentStatusChange('pending');
  };
  const handlePayAgain = () => {
    // Just reset the UI to show the instructions again without regenerating
    onPaymentStatusChange('pending');
  };

  // Generate order received URL with order ID and key
  const generateOrderReceivedUrl = () => {
    if (view_order_url) {
      return view_order_url;
    }

    // Get order key from the current URL if available
    const urlParams = new URLSearchParams(window.location.search);
    const orderKey = urlParams.get('key');

    // Construct the order received URL with order ID and key
    let orderReceivedUrl = settings.order_received_url;
    if (orderReceivedUrl.includes('?')) {
      orderReceivedUrl += `&order-received=${orderId}`;
    } else {
      orderReceivedUrl += `?order-received=${orderId}`;
    }
    if (orderKey) {
      orderReceivedUrl += `&key=${orderKey}`;
    }
    return orderReceivedUrl;
  };
  const renderPaymentStatus = () => {
    switch (paymentStatus) {
      case 'paid':
        return /*#__PURE__*/React.createElement("div", {
          id: "monoova-payment-confirmed",
          className: "monoova-payment-status"
        }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("svg", {
          width: "45",
          height: "46",
          viewBox: "0 0 45 46",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }, /*#__PURE__*/React.createElement("path", {
          "fill-rule": "evenodd",
          "clip-rule": "evenodd",
          d: "M31.8167 18.8513L21.0413 29.6267C20.6983 29.9697 20.2527 30.14 19.8047 30.14C19.3543 30.14 18.9087 29.9697 18.5657 29.6267L13.178 24.239C12.4943 23.5553 12.4943 22.447 13.178 21.7633C13.8617 21.0797 14.9677 21.0797 15.6513 21.7633L19.8047 25.9143L29.341 16.3757C30.0247 15.692 31.133 15.692 31.8167 16.3757C32.5003 17.0593 32.5003 18.1677 31.8167 18.8513ZM22.4997 0.833344C10.2777 0.833344 0.333008 10.778 0.333008 23C0.333008 35.2243 10.2777 45.1667 22.4997 45.1667C34.7217 45.1667 44.6663 35.2243 44.6663 23C44.6663 10.778 34.7217 0.833344 22.4997 0.833344Z",
          fill: "#2CB5C5"
        }))), /*#__PURE__*/React.createElement("h3", {
          className: "title"
        }, settings.strings.payment_confirmed), /*#__PURE__*/React.createElement("p", null, settings.strings.payment_confirmed_message), /*#__PURE__*/React.createElement("div", {
          className: "monoova-payment-status-actions"
        }, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.Button, {
          variant: "primary",
          href: generateOrderReceivedUrl()
        }, settings.strings.view_order_details)), /*#__PURE__*/React.createElement(_NavigationCountdown__WEBPACK_IMPORTED_MODULE_2__.NavigationCountdown, {
          initialSeconds: 5,
          redirectUrl: generateOrderReceivedUrl(),
          message: settings.strings.redirecting_to_order_page
        }));
      case 'expired':
        return /*#__PURE__*/React.createElement("div", {
          id: "monoova-payment-expired",
          className: "monoova-payment-status"
        }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("svg", {
          width: "45",
          height: "46",
          viewBox: "0 0 45 46",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }, /*#__PURE__*/React.createElement("path", {
          "fill-rule": "evenodd",
          "clip-rule": "evenodd",
          d: "M24.2647 31.617C24.2647 32.583 23.4807 33.367 22.5147 33.367C21.5487 33.367 20.7647 32.583 20.7647 31.617V21.586C20.7647 20.62 21.5487 19.836 22.5147 19.836C23.4807 19.836 24.2647 20.62 24.2647 21.586V31.617ZM20.7507 14.3456C20.7507 13.3796 21.5347 12.5956 22.5007 12.5956C23.4667 12.5956 24.2507 13.3796 24.2507 14.3456C24.2507 15.3116 23.4667 16.1703 22.5007 16.1703C21.5347 16.1703 20.7507 15.4586 20.7507 14.4926V14.3456ZM22.5007 0.833313C10.2787 0.833313 0.333984 10.7756 0.333984 23C0.333984 35.222 10.2787 45.1666 22.5007 45.1666C34.7227 45.1666 44.6673 35.222 44.6673 23C44.6673 10.7756 34.7227 0.833313 22.5007 0.833313Z",
          fill: "#FF782A"
        }))), /*#__PURE__*/React.createElement("h3", {
          className: "title"
        }, settings.strings.payment_expired), /*#__PURE__*/React.createElement("p", null, settings.strings.payment_expired_message), /*#__PURE__*/React.createElement("div", {
          className: "monoova-payment-status-actions"
        }, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.Button, {
          variant: "primary",
          onClick: () => handlePlaceNewOrder()
        }, settings.strings.place_new_order)));
      case 'failed':
        return /*#__PURE__*/React.createElement("div", {
          id: "monoova-payment-rejected",
          className: "monoova-payment-status"
        }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("svg", {
          width: "45",
          height: "46",
          viewBox: "0 0 45 46",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        }, /*#__PURE__*/React.createElement("path", {
          "fill-rule": "evenodd",
          "clip-rule": "evenodd",
          d: "M24.2647 31.617C24.2647 32.583 23.4807 33.367 22.5147 33.367C21.5487 33.367 20.7647 32.583 20.7647 31.617V21.586C20.7647 20.62 21.5487 19.836 22.5147 19.836C23.4807 19.836 24.2647 20.62 24.2647 21.586V31.617ZM20.7507 14.3456C20.7507 13.3796 21.5347 12.5956 22.5007 12.5956C23.4667 12.5956 24.2507 13.3796 24.2507 14.3456C24.2507 15.3116 23.4667 16.1703 22.5007 16.1703C21.5347 16.1703 20.7507 15.4586 20.7507 14.4926V14.3456ZM22.5007 0.833313C10.2787 0.833313 0.333984 10.7756 0.333984 23C0.333984 35.222 10.2787 45.1666 22.5007 45.1666C34.7227 45.1666 44.6673 35.222 44.6673 23C44.6673 10.7756 34.7227 0.833313 22.5007 0.833313Z",
          fill: "#FF0000"
        }))), /*#__PURE__*/React.createElement("h3", {
          className: "title"
        }, settings.strings.payment_failed), /*#__PURE__*/React.createElement("p", null, settings.strings.payment_failed_message), /*#__PURE__*/React.createElement("p", {
          id: "monoova-rejection-reason",
          style: {
            fontStyle: 'italic',
            marginTop: 15
          }
        }, "Reason: ", /*#__PURE__*/React.createElement("span", {
          dangerouslySetInnerHTML: {
            __html: paymentFailedReason
          }
        })), /*#__PURE__*/React.createElement("div", {
          className: "monoova-payment-status-actions"
        }, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.Button, {
          variant: "primary",
          onClick: () => handlePayAgain()
        }, settings.strings.try_again)));
      default:
        return null;
    }
  };
  if (paymentStatus !== 'pending' && paymentStatus !== 'initial') {
    return /*#__PURE__*/React.createElement("div", {
      className: "monoova-payid-bank-transfer-instructions-wrapper"
    }, /*#__PURE__*/React.createElement("div", {
      className: "monoova-instructions-container"
    }, renderPaymentStatus()));
  }
  return /*#__PURE__*/React.createElement("div", {
    className: "monoova-payid-bank-transfer-instructions-wrapper"
  }, showMethodSwitcher && /*#__PURE__*/React.createElement("div", {
    className: "monoova-instruction-method-selection"
  }, /*#__PURE__*/React.createElement("div", {
    style: {
      fontWeight: 600
    }
  }, settings.strings.pay_with), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_1__.RadioControl, {
    selected: selectedMethod,
    options: [{
      label: settings.strings.payid_method,
      value: 'payid'
    }, {
      label: settings.strings.bank_method,
      value: 'bank_transfer'
    }],
    onChange: v => setSelectedMethod(v)
  })), /*#__PURE__*/React.createElement("div", null, settings.strings.payment_instructions_description), /*#__PURE__*/React.createElement("div", {
    className: "monoova-instructions-container"
  }, /*#__PURE__*/React.createElement("div", {
    id: "monoova-payment-pending"
  }, settings.instructions && /*#__PURE__*/React.createElement("p", {
    dangerouslySetInnerHTML: {
      __html: settings.instructions
    }
  }), selectedMethod === 'payid' && showPayID && /*#__PURE__*/React.createElement("div", {
    id: "monoova-payid-view"
  }, /*#__PURE__*/React.createElement("div", {
    className: "monoova-scan-pay"
  }, /*#__PURE__*/React.createElement("div", {
    className: "pay-label"
  }, settings.strings.scan_pay), /*#__PURE__*/React.createElement("div", {
    className: "amount",
    dangerouslySetInnerHTML: {
      __html: details.amount_to_pay_formatted
    }
  })), /*#__PURE__*/React.createElement("p", null, settings.strings.scan_pay_description), /*#__PURE__*/React.createElement("div", {
    className: "monoova-manual-pay"
  }, /*#__PURE__*/React.createElement("img", {
    src: `${settings.plugin_url}assets/images/payid-logo.svg`,
    alt: "PayID",
    className: "payid-logo"
  }), /*#__PURE__*/React.createElement("span", {
    className: "copy-target",
    "data-copy-id": "payid"
  }, details.payid_value), /*#__PURE__*/React.createElement(CopyButton, {
    text: details.payid_value
  })), settings.show_reference_field && /*#__PURE__*/React.createElement("div", {
    className: "monoova-payment-reference-section"
  }, /*#__PURE__*/React.createElement("h4", null, settings.strings.payment_reference), /*#__PURE__*/React.createElement("p", null, settings.strings.include_reference_with_payment), /*#__PURE__*/React.createElement("div", {
    className: "monoova-reference-field full-width"
  }, /*#__PURE__*/React.createElement("div", {
    className: "field-label"
  }, settings.strings.reference), /*#__PURE__*/React.createElement("div", {
    className: "field-value-wrapper"
  }, /*#__PURE__*/React.createElement("span", {
    className: "copy-target",
    "data-copy-id": "reference"
  }, details.reference + 'P', " "), /*#__PURE__*/React.createElement(CopyButton, {
    text: details.reference + 'P'
  }))))), selectedMethod === 'bank_transfer' && showBank && /*#__PURE__*/React.createElement("div", {
    id: "monoova-bank-view"
  }, /*#__PURE__*/React.createElement("h3", null, settings.strings.bank_transfer_details), /*#__PURE__*/React.createElement("div", {
    className: "monoova-bank-field-grid"
  }, /*#__PURE__*/React.createElement("div", {
    className: "monoova-bank-field"
  }, /*#__PURE__*/React.createElement("div", {
    className: "field-label"
  }, settings.strings.account_name), /*#__PURE__*/React.createElement("div", {
    className: "field-value-wrapper"
  }, /*#__PURE__*/React.createElement("span", {
    className: "copy-target",
    "data-copy-id": "account-name"
  }, details.bank_account_name))), /*#__PURE__*/React.createElement("div", {
    className: "monoova-bank-field"
  }, /*#__PURE__*/React.createElement("div", {
    className: "field-label"
  }, settings.strings.bsb), /*#__PURE__*/React.createElement("div", {
    className: "field-value-wrapper"
  }, /*#__PURE__*/React.createElement("span", {
    className: "copy-target",
    "data-copy-id": "bsb"
  }, details.bank_bsb), /*#__PURE__*/React.createElement(CopyButton, {
    text: details.bank_bsb
  })))), /*#__PURE__*/React.createElement("div", {
    className: "monoova-bank-field full-width"
  }, /*#__PURE__*/React.createElement("div", {
    className: "field-label"
  }, settings.strings.account_number), /*#__PURE__*/React.createElement("div", {
    className: "field-value-wrapper"
  }, /*#__PURE__*/React.createElement("span", {
    className: "copy-target",
    "data-copy-id": "account-number"
  }, details.bank_account_number), /*#__PURE__*/React.createElement(CopyButton, {
    text: details.bank_account_number
  }))), settings.show_reference_field && /*#__PURE__*/React.createElement("div", {
    className: "monoova-payment-reference-section"
  }, /*#__PURE__*/React.createElement("h4", null, settings.strings.payment_reference), /*#__PURE__*/React.createElement("p", null, settings.strings.include_reference_with_payment), /*#__PURE__*/React.createElement("div", {
    className: "monoova-bank-field full-width"
  }, /*#__PURE__*/React.createElement("div", {
    className: "field-label"
  }, settings.strings.reference), /*#__PURE__*/React.createElement("div", {
    className: "field-value-wrapper"
  }, /*#__PURE__*/React.createElement("span", {
    className: "copy-target",
    "data-copy-id": "reference"
  }, details.reference + 'B', " "), /*#__PURE__*/React.createElement(CopyButton, {
    text: details.reference + 'B'
  }))))), /*#__PURE__*/React.createElement("div", {
    className: "monoova-confirmation-text"
  }, /*#__PURE__*/React.createElement("p", null, settings.strings.payment_confirmed_automatically)), expiryTime && /*#__PURE__*/React.createElement(CountdownTimer, {
    expiryTimestamp: expiryTime,
    onExpired: handleExpired,
    strings: settings.strings
  }))));
};

/***/ }),

/***/ "./assets/js/src/hooks/usePayIDPaymentInstructions.js":
/*!************************************************************!*\
  !*** ./assets/js/src/hooks/usePayIDPaymentInstructions.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   usePayIDPaymentInstructions: () => (/* binding */ usePayIDPaymentInstructions)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _usePersistentPaymentDetailsContainer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./usePersistentPaymentDetailsContainer */ "./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js");


const usePayIDPaymentInstructions = ({
  settings,
  billing,
  shippingData,
  orderId: existingOrderId,
  // Accept existing order ID from props
  containerId = "payid-instructions-container",
  paymentMethodId = "monoova_payid",
  hasRequiredInfo = true // New parameter to control API calling
}) => {
  const [isLoading, setIsLoading] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [instructions, setInstructions] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [error, setError] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [orderId, setOrderId] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(existingOrderId || null); // Initialize with existing order ID
  const [paymentStatus, setPaymentStatus] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)('pending');
  const [paymentFailedReason, setPaymentFailedReason] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [expiryTime, setExpiryTime] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);

  // Use refs to prevent duplicate API calls
  const isGeneratingInstructionsRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
  const pollingIntervalRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  const countdownIntervalRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);

  // Use persistent container management
  const {
    targetRef,
    containerElement,
    containerIdActive,
    isContainerInitialized,
    setContainerInitialized,
    setOrderData,
    getOrderData,
    clearOrderData,
    // Make sure this is available from the hook
    showContainer,
    hideContainer
  } = (0,_usePersistentPaymentDetailsContainer__WEBPACK_IMPORTED_MODULE_1__.usePersistentPaymentDetailsContainer)(paymentMethodId, containerId);

  // Get persistent instruction data
  const persistentData = getOrderData();
  const persistentInstructions = persistentData?.instructions;
  const persistentOrderId = persistentData?.orderId;

  // Check if we should use existing instructions
  const shouldUseExistingInstructions = isContainerInitialized && persistentInstructions;
  const stopPolling = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  }, []);

  // Internal function to fetch instructions from the backend
  const fetchPaymentInstructions = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (isRegeneration = false) => {
    setIsLoading(true);
    setError(null);
    isGeneratingInstructionsRef.current = true;
    try {
      console.log(`PayID Instructions: ${isRegeneration ? 'Regenerating' : 'Generating'} payment instructions for order:`, existingOrderId);
      const instructionsData = new FormData();
      instructionsData.append('action', 'monoova_generate_payment_instructions_in_blocked_checkout');
      instructionsData.append('order_id', existingOrderId);
      instructionsData.append('nonce', settings.generate_instructions_nonce);
      if (isRegeneration) {
        instructionsData.append('regenerate', 'true');
      }
      const instructionsResponse = await fetch(settings.ajax_url, {
        method: 'POST',
        body: instructionsData
      });
      const instructionsResult = await instructionsResponse.json();
      if (!instructionsResult.success) {
        throw new Error(instructionsResult.data?.message || `Failed to ${isRegeneration ? 'regenerate' : 'generate'} payment instructions`);
      }
      const paymentInstructions = instructionsResult.data;
      setInstructions(paymentInstructions);
      setOrderId(existingOrderId);
      if (paymentInstructions.expiry_timestamp) {
        setExpiryTime(paymentInstructions.expiry_timestamp);
      }
      setOrderData(existingOrderId, null, paymentInstructions);
      setContainerInitialized();
    } catch (error) {
      console.error(`PayID Instructions: Error ${isRegeneration ? 'regenerating' : 'generating'} instructions:`, error);
      setError(error.message);
    } finally {
      setIsLoading(false);
      isGeneratingInstructionsRef.current = false;
    }
  }, [existingOrderId, settings, setOrderData, setContainerInitialized]);

  // Generate payment instructions for existing order
  const generatePaymentInstructions = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async () => {
    if (isGeneratingInstructionsRef.current || instructions || persistentInstructions) {
      return;
    }
    if (!existingOrderId || !hasRequiredInfo) {
      return;
    }
    await fetchPaymentInstructions(false);
  }, [existingOrderId, hasRequiredInfo, instructions, persistentInstructions, fetchPaymentInstructions]);
  const regeneratePaymentInstructions = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async () => {
    // Stop any ongoing polling and timers
    stopPolling();
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }

    // Reset all relevant states
    setInstructions(null);
    setPaymentStatus('pending');
    setPaymentFailedReason(null);
    setExpiryTime(null);

    // Clear persistent data
    if (clearOrderData) {
      clearOrderData();
    }
    await fetchPaymentInstructions(true);
  }, [existingOrderId, clearOrderData, stopPolling, fetchPaymentInstructions]);

  // Payment status polling
  const checkPaymentStatus = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async () => {
    const currentOrderId = orderId || persistentOrderId || existingOrderId;
    if (!currentOrderId) return;

    // Get the order key and status check nonce from the current instructions data
    const currentInstructions = instructions || persistentInstructions;
    const orderKey = currentInstructions?.details?.order_key;
    const statusCheckNonce = currentInstructions?.status_check_nonce;
    if (!orderKey || !statusCheckNonce) {
      console.warn('PayID Instructions: Missing order key or nonce for status check');
      return;
    }
    try {
      const statusData = new FormData();
      statusData.append('action', 'monoova_check_payment_status');
      statusData.append('order_id', currentOrderId);
      statusData.append('order_key', orderKey); // Add order key parameter
      statusData.append('nonce', statusCheckNonce); // Use the nonce from backend

      const response = await fetch(settings.ajax_url, {
        method: 'POST',
        body: statusData
      });
      const result = await response.json();
      // mock data with a delay 10s
      // const result = await new Promise((resolve) => {
      //     setTimeout(() => {
      //         resolve({
      //             success: true,
      //             data: {
      //                 status: 'failed',
      //                 reason: 'Insufficient funds',
      //             }
      //         });
      //     }, 10000);
      // });

      if (result.success && result.data.status !== 'pending') {
        setPaymentStatus(result.data.status);
        setPaymentFailedReason(result.data.reason || null);
        stopPolling();
      }
    } catch (error) {
      console.error('PayID Instructions: Error checking payment status:', error);
    }
  }, [orderId, persistentOrderId, existingOrderId, instructions, persistentInstructions, settings, stopPolling]);

  // Start/stop polling
  const startPolling = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {
    if (pollingIntervalRef.current) return;

    // Initial check
    checkPaymentStatus();
    // Poll every 10 seconds
    pollingIntervalRef.current = setInterval(checkPaymentStatus, 10000);
  }, [checkPaymentStatus]);

  // Countdown timer
  const startCountdown = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {
    if (!expiryTime || countdownIntervalRef.current) return;
    countdownIntervalRef.current = setInterval(() => {
      const distance = expiryTime * 1000 - new Date().getTime();
      if (distance < 0) {
        setPaymentStatus('expired');
        clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
        stopPolling();
      }
    }, 1000);
  }, [expiryTime, stopPolling]);

  // Function to reset just the payment status to restart polling
  const resetPaymentStatus = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async () => {
    const currentOrderId = orderId || persistentOrderId || existingOrderId;
    if (!currentOrderId) {
      console.warn('PayID Instructions: No order ID available for payment status reset');
      return;
    }
    try {
      // Get the order key from current instructions for security
      const currentInstructions = instructions || persistentInstructions;
      const orderKey = currentInstructions?.details?.order_key;
      if (!orderKey) {
        console.warn('PayID Instructions: Missing order key for status reset');
        return;
      }
      const resetData = new FormData();
      resetData.append('action', 'monoova_reset_payment_status');
      resetData.append('order_id', currentOrderId);
      resetData.append('order_key', orderKey);
      resetData.append('nonce', settings.reset_payment_status_nonce);
      const response = await fetch(settings.ajax_url, {
        method: 'POST',
        body: resetData
      });
      const result = await response.json();
      if (result.success) {
        // Reset the payment status to pending to restart polling
        setPaymentStatus('pending');
        setPaymentFailedReason(null);
        console.log('PayID Instructions: Payment status reset successfully');
      } else {
        console.error('PayID Instructions: Failed to reset payment status:', result.data?.message);
        throw new Error(result.data?.message || 'Failed to reset payment status');
      }
    } catch (error) {
      console.error('PayID Instructions: Error resetting payment status:', error);
      setError(error.message);
    }
  }, [orderId, persistentOrderId, existingOrderId, instructions, persistentInstructions, settings]);

  // Reset function
  const resetStates = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {
    setInstructions(null);
    setOrderId(null);
    setError(null);
    setIsLoading(false);
    setPaymentStatus('pending');
    setExpiryTime(null);

    // Reset refs
    isGeneratingInstructionsRef.current = false;

    // Stop timers
    stopPolling();
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }
  }, [stopPolling]);

  // Initialize payment instructions when conditions are met
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    const initializeInstructions = async () => {
      if (shouldUseExistingInstructions) {
        setInstructions(persistentInstructions);
        setOrderId(persistentOrderId || existingOrderId);
        showContainer();

        // Start polling and countdown if we have persistent instructions
        if (persistentInstructions?.expiry_timestamp) {
          setExpiryTime(persistentInstructions.expiry_timestamp);
        }
        return;
      }

      // Check if we have required data to generate instructions
      // Only proceed if guest has provided all required information
      if (existingOrderId && hasRequiredInfo && !isLoading && !instructions && !isGeneratingInstructionsRef.current) {
        await generatePaymentInstructions();
        showContainer();
      }
    };

    // Use a small delay to prevent rapid successive calls
    const timeoutId = setTimeout(initializeInstructions, 100);
    return () => clearTimeout(timeoutId);
  }, [existingOrderId, hasRequiredInfo,
  // Replace individual billing field dependencies with this
  shouldUseExistingInstructions, persistentInstructions, persistentOrderId, isLoading, instructions, generatePaymentInstructions, showContainer]);

  // Start polling when we have instructions
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    if ((instructions || persistentInstructions) && paymentStatus === 'pending') {
      startPolling();
      startCountdown();
    }
    return () => {
      stopPolling();
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, [instructions, persistentInstructions, paymentStatus, startPolling, startCountdown, stopPolling]);
  return {
    isLoading,
    instructions: instructions || persistentInstructions,
    error,
    orderId: orderId || persistentOrderId || existingOrderId,
    // Return existing order ID if available
    paymentStatus,
    paymentFailedReason,
    expiryTime,
    resetStates,
    regeneratePaymentInstructions,
    // Expose the new function
    resetPaymentStatus,
    // Expose the status reset function
    containerRef: targetRef,
    isInitialized: shouldUseExistingInstructions || !!instructions
  };
};

/***/ }),

/***/ "./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js":
/*!*********************************************************************!*\
  !*** ./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   usePersistentPaymentDetailsContainer: () => (/* binding */ usePersistentPaymentDetailsContainer)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);


// Global container management for persistent Primer checkout
class PrimerCheckoutManager {
  constructor() {
    this.containers = new Map();
    this.activeContainers = new Set();
    this.isInitialized = false;
  }

  // Create or get existing container
  getOrCreateContainer(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    if (!this.containers.has(containerKey)) {
      // Create container element
      const container = document.createElement('div');
      container.id = containerId;
      container.className = 'primer-checkout-persistent-container';
      container.style.cssText = `
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 100%;
                visibility: hidden;
                opacity: 0;
                pointer-events: none;
                transition: all 0.3s ease;
            `;

      // Append to body to keep it persistent
      document.body.appendChild(container);
      this.containers.set(containerKey, {
        element: container,
        isInitialized: false,
        isVisible: false,
        orderId: null,
        // Store orderId for payment completion
        clientToken: null // Store clientToken as well
      });
    }
    return this.containers.get(containerKey);
  }

  // Show container in target element
  showContainer(paymentMethodId, containerId, targetElement) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo && containerInfo.element) {
      // Hide all other containers first
      this.hideAllContainers();

      // Move container to target and show it
      if (targetElement) {
        targetElement.appendChild(containerInfo.element);
        containerInfo.element.style.cssText = `
                    position: relative;
                    top: auto;
                    left: auto;
                    width: 100%;
                    visibility: visible;
                    opacity: 1;
                    pointer-events: auto;
                    transition: all 0.3s ease;
                `;
        containerInfo.isVisible = true;
        this.activeContainers.add(containerKey);
      }
    }
  }

  // Hide container
  hideContainer(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo && containerInfo.element) {
      // Move back to body and hide
      document.body.appendChild(containerInfo.element);
      containerInfo.element.style.cssText = `
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 100%;
                visibility: hidden;
                opacity: 0;
                pointer-events: none;
                transition: all 0.3s ease;
            `;
      containerInfo.isVisible = false;
      this.activeContainers.delete(containerKey);
    }
  }

  // Hide all containers
  hideAllContainers() {
    this.containers.forEach((containerInfo, containerKey) => {
      if (containerInfo.isVisible) {
        this.hideContainer(containerKey.split('_')[0], containerKey.split('_')[1]);
      }
    });
  }

  // Clear order data for a specific container
  clearOrderData(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo) {
      containerInfo.orderId = null;
      containerInfo.clientToken = null;
      containerInfo.instructions = null; // Also clear instructions
      containerInfo.isInitialized = false; // Reset initialization state
    }
  }

  // Set initialization status
  setContainerInitialized(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo) {
      containerInfo.isInitialized = true;
    }
  }

  // Check if container is initialized
  isContainerInitialized(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    return containerInfo ? containerInfo.isInitialized : false;
  }

  // Set order and token data
  setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions = null) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo) {
      containerInfo.orderId = orderId;
      containerInfo.clientToken = clientToken;
      containerInfo.instructions = instructions; // Add instructions storage (using for PayID/Bank Transfer)
    } else {
      console.warn(`[PrimerCheckoutManager] Container ${containerKey} not found for setting order data`);
    }
  }

  // Get order data
  getOrderData(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    const result = containerInfo ? {
      orderId: containerInfo.orderId,
      clientToken: containerInfo.clientToken,
      instructions: containerInfo.instructions // Include instructions in returned data
    } : {
      orderId: null,
      clientToken: null,
      instructions: null
    };
    return result;
  }

  // Get container element
  getContainerElement(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    return containerInfo ? containerInfo.element : null;
  }

  // Cleanup
  cleanup() {
    this.containers.forEach(containerInfo => {
      if (containerInfo.element && containerInfo.element.parentNode) {
        containerInfo.element.parentNode.removeChild(containerInfo.element);
      }
    });
    this.containers.clear();
    this.activeContainers.clear();
  }
}

// Global instance
const primerCheckoutManager = new PrimerCheckoutManager();

// Hook for persistent container management
const usePersistentPaymentDetailsContainer = (paymentMethodId, containerId) => {
  const targetRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  const isActiveRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);

  // Generate persistent container ID
  const persistentContainerId = `persistent-${paymentMethodId}-${containerId}`;
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    // Create or get container
    const containerInfo = primerCheckoutManager.getOrCreateContainer(paymentMethodId, containerId);

    // Update the container's ID to match what Primer expects
    if (containerInfo.element) {
      containerInfo.element.id = persistentContainerId;
    }

    // Show container when component mounts
    if (targetRef.current && !isActiveRef.current) {
      primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);
      isActiveRef.current = true;
    }

    // Cleanup on unmount
    return () => {
      if (isActiveRef.current) {
        primerCheckoutManager.hideContainer(paymentMethodId, containerId);
        isActiveRef.current = false;
      }
    };
  }, [paymentMethodId, containerId]);
  return {
    targetRef,
    containerElement: primerCheckoutManager.getContainerElement(paymentMethodId, containerId),
    containerIdActive: persistentContainerId,
    // Return the active container ID for Primer
    isContainerInitialized: primerCheckoutManager.isContainerInitialized(paymentMethodId, containerId),
    setContainerInitialized: () => primerCheckoutManager.setContainerInitialized(paymentMethodId, containerId),
    // Order data persistence methods
    setOrderData: (orderId, clientToken, instructions = null) => primerCheckoutManager.setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions),
    getOrderData: () => primerCheckoutManager.getOrderData(paymentMethodId, containerId),
    clearOrderData: () => {
      primerCheckoutManager.clearOrderData(paymentMethodId, containerId);
    },
    showContainer: () => {
      if (targetRef.current && !isActiveRef.current) {
        primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);
        isActiveRef.current = true;
      }
    },
    hideContainer: () => {
      if (isActiveRef.current) {
        primerCheckoutManager.hideContainer(paymentMethodId, containerId);
        isActiveRef.current = false;
      }
    }
  };
};

// Cleanup function for page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    primerCheckoutManager.cleanup();
  });
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (primerCheckoutManager);

/***/ }),

/***/ "@woocommerce/block-data":
/*!**************************************!*\
  !*** external ["wc","wcBlocksData"] ***!
  \**************************************/
/***/ ((module) => {

module.exports = wc.wcBlocksData;

/***/ }),

/***/ "@woocommerce/blocks-registry":
/*!******************************************!*\
  !*** external ["wc","wcBlocksRegistry"] ***!
  \******************************************/
/***/ ((module) => {

module.exports = wc.wcBlocksRegistry;

/***/ }),

/***/ "@woocommerce/settings":
/*!************************************!*\
  !*** external ["wc","wcSettings"] ***!
  \************************************/
/***/ ((module) => {

module.exports = wc.wcSettings;

/***/ }),

/***/ "@wordpress/components":
/*!************************************!*\
  !*** external ["wp","components"] ***!
  \************************************/
/***/ ((module) => {

module.exports = window["wp"]["components"];

/***/ }),

/***/ "@wordpress/data":
/*!******************************!*\
  !*** external ["wp","data"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["data"];

/***/ }),

/***/ "@wordpress/element":
/*!*********************************!*\
  !*** external ["wp","element"] ***!
  \*********************************/
/***/ ((module) => {

module.exports = window["wp"]["element"];

/***/ }),

/***/ "@wordpress/html-entities":
/*!**************************************!*\
  !*** external ["wp","htmlEntities"] ***!
  \**************************************/
/***/ ((module) => {

module.exports = window["wp"]["htmlEntities"];

/***/ }),

/***/ "@wordpress/i18n":
/*!******************************!*\
  !*** external ["wp","i18n"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["i18n"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!*****************************************************!*\
  !*** ./assets/js/src/blocks/monoova-payid-block.js ***!
  \*****************************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @woocommerce/blocks-registry */ "@woocommerce/blocks-registry");
/* harmony import */ var _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @woocommerce/settings */ "@woocommerce/settings");
/* harmony import */ var _woocommerce_settings__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/html-entities */ "@wordpress/html-entities");
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wordpress/data */ "@wordpress/data");
/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @woocommerce/block-data */ "@woocommerce/block-data");
/* harmony import */ var _woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _hooks_usePayIDPaymentInstructions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/usePayIDPaymentInstructions */ "./assets/js/src/hooks/usePayIDPaymentInstructions.js");
/* harmony import */ var _PayIDInstructionsContainer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PayIDInstructionsContainer */ "./assets/js/src/blocks/PayIDInstructionsContainer.js");
/**
 * Monoova PayID Block for WooCommerce Blocks
 * 
 * Uses ES6 imports with defensive programming to handle cases where WooCommerce dependencies 
 * may not be available (edit mode, missing plugins, etc.)
 */









// Import custom hooks and components



// Load QR Code library
// if (typeof window !== 'undefined' && !window.QRCode) {
//     const script = document.createElement('script');
//     script.src = 'https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js';
//     script.async = true;
//     document.head.appendChild(script);
// }

// If registerPaymentMethod is not available, we can't register the payment method
if (typeof _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__.registerPaymentMethod !== 'function') {
  console.warn('Monoova PayID Block: registerPaymentMethod not available. Available globals:', Object.keys(window.wc || {}));
} else {
  // Try to get settings
  let settings = {};
  if (typeof _woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__.getSetting === 'function') {
    try {
      settings = (0,_woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__.getSetting)('monoova_payid_data', {});
    } catch (error) {
      console.log('Monoova PayID Block: getSetting failed:', error);
    }
  }
  const defaultLabel = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)('PayID / Bank Transfer', 'monoova-payments-for-woocommerce');
  const label = (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__.decodeEntities)(settings.title) || defaultLabel;

  /**
   * Content component for PayID with payment instructions
   */
  const Content = ({
    billing,
    shippingData,
    checkoutStatus,
    paymentStatus,
    eventRegistration,
    emitResponse
  }) => {
    const [currentPaymentStatus, setCurrentPaymentStatus] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)('pending');

    // Get the existing order ID from WooCommerce checkout store
    const {
      orderId
    } = (0,_wordpress_data__WEBPACK_IMPORTED_MODULE_5__.useSelect)(select => {
      const store = select(_woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6__.CHECKOUT_STORE_KEY);
      return {
        orderId: store.getOrderId()
      };
    });

    // Helper function to check if required guest information is available
    const hasRequiredGuestInfo = () => {
      const billingAddress = billing?.billingAddress;
      if (!billingAddress) return false;

      // Required fields for guest customers to generate PayID instructions
      return !!(billingAddress.email && billingAddress.first_name && billingAddress.last_name && billingAddress.address_1 && billingAddress.city && billingAddress.postcode && billingAddress.country);
    };

    // Use the PayID payment instructions hook
    const {
      isLoading,
      instructions,
      error,
      paymentStatus: hookPaymentStatus,
      paymentFailedReason,
      expiryTime,
      resetStates,
      regeneratePaymentInstructions,
      // Get the regeneration function
      resetPaymentStatus,
      // Get the status reset function
      containerRef,
      isInitialized
    } = (0,_hooks_usePayIDPaymentInstructions__WEBPACK_IMPORTED_MODULE_7__.usePayIDPaymentInstructions)({
      settings,
      billing,
      shippingData,
      orderId,
      // Pass the existing order ID from checkout store
      containerId: "payid-instructions-container",
      paymentMethodId: "monoova_payid",
      hasRequiredInfo: hasRequiredGuestInfo() // Pass the validation result
    });
    (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useEffect)(() => {
      // Selector for the block checkout's "Place Order" button
      const placeOrderButton = document.querySelector('.wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button');
      if (placeOrderButton) {
        // Hide the button when Monoova Card is selected
        placeOrderButton.style.display = 'none';
      }

      // Cleanup function: runs when component unmounts (e.g., user selects another payment method)
      return () => {
        if (placeOrderButton) {
          // Restore the button's default display style
          placeOrderButton.style.display = '';
        }
      };
    }, []);

    // Update payment status
    (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useEffect)(() => {
      if (hookPaymentStatus !== 'pending') {
        setCurrentPaymentStatus(hookPaymentStatus);
      }
    }, [hookPaymentStatus]);
    const description = (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__.decodeEntities)(settings.description || '');

    // Handle payment status changes from the component
    const handlePaymentStatusChange = status => {
      if (status === 'pending') {
        // This will just change the UI view
        setCurrentPaymentStatus('pending');
        // This will restart the polling in the hook
        if (resetPaymentStatus) {
          resetPaymentStatus();
        }
      } else {
        setCurrentPaymentStatus(status);
      }
    };

    // Loading state
    if (isLoading && !instructions) {
      return /*#__PURE__*/React.createElement("div", {
        className: "monoova-payid-content"
      }, /*#__PURE__*/React.createElement("div", {
        className: "monoova-payid-description",
        dangerouslySetInnerHTML: {
          __html: description
        }
      }), /*#__PURE__*/React.createElement("div", {
        className: "monoova-payid-loading"
      }, /*#__PURE__*/React.createElement("div", {
        className: "spinner"
      }), /*#__PURE__*/React.createElement("p", null, settings.strings?.loading || 'Generating payment instructions...')));
    }

    // Show message when required information is missing
    if (!hasRequiredGuestInfo() && !instructions) {
      return /*#__PURE__*/React.createElement("div", {
        className: "monoova-payid-content"
      }, /*#__PURE__*/React.createElement("div", {
        className: "monoova-payid-description",
        dangerouslySetInnerHTML: {
          __html: description
        }
      }), /*#__PURE__*/React.createElement("div", {
        className: "monoova-payid-info"
      }, /*#__PURE__*/React.createElement("p", null, "Please complete your billing information to generate PayID payment instructions."), /*#__PURE__*/React.createElement("small", null, "Required: Email, Name, Address, City, Postcode, and Country")));
    }

    // Error state
    if (error) {
      return /*#__PURE__*/React.createElement("div", {
        className: "monoova-payid-content"
      }, /*#__PURE__*/React.createElement("div", {
        className: "monoova-payid-description",
        dangerouslySetInnerHTML: {
          __html: description
        }
      }), /*#__PURE__*/React.createElement("div", {
        className: "monoova-payid-error"
      }, /*#__PURE__*/React.createElement("p", null, error), /*#__PURE__*/React.createElement("button", {
        type: "button",
        onClick: resetStates
      }, "Try Again")));
    }
    return /*#__PURE__*/React.createElement("div", {
      className: "monoova-payid-content"
    }, /*#__PURE__*/React.createElement("div", {
      className: "monoova-payid-description",
      dangerouslySetInnerHTML: {
        __html: description
      }
    }), /*#__PURE__*/React.createElement("div", {
      ref: containerRef,
      className: "payid-container-target"
    }, instructions && /*#__PURE__*/React.createElement(_PayIDInstructionsContainer__WEBPACK_IMPORTED_MODULE_8__.PayIDInstructionsContainer, {
      instructions: instructions,
      settings: settings,
      paymentStatus: currentPaymentStatus,
      expiryTime: expiryTime,
      paymentFailedReason: paymentFailedReason,
      onPaymentStatusChange: handlePaymentStatusChange,
      onRegenerateInstructions: regeneratePaymentInstructions,
      orderId: orderId
    })));
  };

  /**
   * Label component
   */
  const Label = props => {
    const {
      PaymentMethodLabel
    } = props.components || {};
    return /*#__PURE__*/React.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%'
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontWeight: 600
      }
    }, label), /*#__PURE__*/React.createElement("img", {
      src: `${settings.plugin_url || '/wp-content/plugins/monoova-payments-for-woocommerce/'}assets/images/payid-logo.svg`,
      alt: "PayID logo",
      style: {
        height: '24px',
        width: 'auto'
      }
    }));
  };

  /**
   * Monoova PayID payment method config object
  /**
   * Edit component (for block editor - simplified view)
   */
  const Edit = () => {
    const description = (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__.decodeEntities)(settings.description || '');
    return /*#__PURE__*/React.createElement("div", {
      className: "monoova-payid-content"
    }, /*#__PURE__*/React.createElement("div", {
      className: "monoova-payid-description",
      dangerouslySetInnerHTML: {
        __html: description
      }
    }), /*#__PURE__*/React.createElement("div", {
      className: "payid-instructions-preview"
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        border: '1px dashed #ccc',
        padding: '20px',
        textAlign: 'center',
        borderRadius: '8px',
        color: '#666',
        margin: '15px 0'
      }
    }, /*#__PURE__*/React.createElement("p", null, /*#__PURE__*/React.createElement("strong", null, "PayID / Bank Transfer Instructions")), /*#__PURE__*/React.createElement("p", null, "Payment instructions will appear here during checkout"))));
  };

  /**
   * Monoova PayID payment method config object
   */
  const MonoovaPayID = {
    name: 'monoova_payid',
    label: /*#__PURE__*/React.createElement(Label, null),
    content: /*#__PURE__*/React.createElement(Content, null),
    edit: /*#__PURE__*/React.createElement(Edit, null),
    canMakePayment: function () {
      return true;
    },
    ariaLabel: label,
    supports: {
      features: settings.supports || []
    },
    icons: settings.icons || null
  };

  // Register the payment method
  try {
    (0,_woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__.registerPaymentMethod)(MonoovaPayID);
  } catch (error) {
    console.error('Monoova PayID Block: Failed to register payment method:', error);
  }
}
})();

/******/ })()
;
//# sourceMappingURL=monoova-payid-block.js.map