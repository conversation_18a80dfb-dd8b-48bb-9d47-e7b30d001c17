document.addEventListener("DOMContentLoaded",(function(){const e=document.getElementById("monoova-payment-instructions");if(!e)return;e.addEventListener("click",(function(t){const o=t.target.closest(".monoova-copy-button");if(!o)return;const n=o.dataset.targetId,a=e.querySelector(`.copy-target[data-copy-id="${n}"]`);if(!a)return;const d=a.innerText.trim();navigator.clipboard.writeText(d).then((()=>{})).catch((e=>console.error("Failed to copy: ",e)))}));const t=document.getElementById("monoova-qr-code");if(t){const e=t.getAttribute("data-payload");e&&"undefined"!=typeof QRCode&&(t.innerHTML="",new QRCode(t,{text:e,width:180,height:180,colorDark:"#000000",colorLight:"#ffffff",correctLevel:QRCode.CorrectLevel.H}))}const o=document.getElementById("monoova-method-switcher");if(o){const e=document.getElementById("monoova-payid-view"),t=document.getElementById("monoova-bank-view");o.addEventListener("change",(function(){"payid"===this.value?(e.style.display="block",t.style.display="none"):(e.style.display="none",t.style.display="block")}))}const n=e.dataset.orderId,a=e.dataset.orderKey,d=e.dataset.nonce,r=e.dataset.ajaxUrl,s=e.dataset.expiryTimestamp,c=document.getElementById("monoova-payment-pending"),l=document.getElementById("monoova-payment-confirmed"),i=document.getElementById("monoova-payment-expired"),y=document.getElementById("monoova-payment-rejected"),m=document.getElementById("monoova-rejection-reason"),p=document.getElementById("monoova-expiry-info");let u,g;const h=(e,t="")=>{switch(u&&clearInterval(u),c.style.display="none",l.style.display="none",y.style.display="none",i.style.display="none",e){case"paid":l.style.display="block";break;case"failed":m&&t&&(0===t.length&&(t="Unexpected Payment issue"),m.innerText=`Reason: ${t}`),y.style.display="block";break;case"cancelled":case"expired":i.style.display="block";break;default:c.style.display="block"}},v=()=>{const e=new FormData;e.append("action","monoova_check_payment_status"),e.append("order_id",n),e.append("order_key",a),e.append("nonce",d),fetch(r,{method:"POST",body:e}).then((e=>e.json())).then((e=>{e.success&&"pending"!==e.data.status&&h(e.data.status,e.data.reason)})).catch((e=>console.error("Error polling for payment status:",e)))};v(),u=setInterval(v,1e4),!s||s<=0||!p||(g=setInterval((()=>{const e=1e3*s-(new Date).getTime();if(e<0)return clearInterval(g),void h("expired");const t=Math.floor(e/864e5),o=Math.floor(e%864e5/36e5),n=Math.floor(e%36e5/6e4),a=Math.floor(e%6e4/1e3),d=p.querySelector(".monoova-expiry-label"),r=p.querySelector(".monoova-expiry-time");if(d&&r){r.innerHTML=`<strong>${new Date(1e3*s).toLocaleString()}</strong>`;let e=[];t>0&&e.push(`${t} ${1===t?"day":"days"}`),o>0&&e.push(`${o} ${1===o?"hour":"hours"}`),n>0&&e.push(`${n} ${1===n?"minute":"minutes"}`),e.length<3&&e.push(`${a} ${1===a?"second":"seconds"}`),r.innerHTML+=` (${e.join(", ")} remaining)`}}),1e3))}));