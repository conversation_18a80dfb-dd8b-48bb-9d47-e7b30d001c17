(()=>{"use strict";const e=wc.wcBlocksRegistry,t=wc.wcSettings,a=wc.wcBlocksData,n=window.wp.htmlEntities,o=window.wp.i18n,r=window.wp.element,i=window.wp.data,l=window.wp.components,c=new class{constructor(){this.containers=new Map,this.activeContainers=new Set,this.isInitialized=!1}getOrCreateContainer(e,t){const a=`${e}_${t}`;if(!this.containers.has(a)){const e=document.createElement("div");e.id=t,e.className="primer-checkout-persistent-container",e.style.cssText="\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            ",document.body.appendChild(e),this.containers.set(a,{element:e,isInitialized:!1,isVisible:!1,orderId:null,clientToken:null})}return this.containers.get(a)}showContainer(e,t,a){const n=`${e}_${t}`,o=this.containers.get(n);o&&o.element&&(this.hideAllContainers(),a&&(a.appendChild(o.element),o.element.style.cssText="\n                    position: relative;\n                    top: auto;\n                    left: auto;\n                    width: 100%;\n                    visibility: visible;\n                    opacity: 1;\n                    pointer-events: auto;\n                    transition: all 0.3s ease;\n                ",o.isVisible=!0,this.activeContainers.add(n)))}hideContainer(e,t){const a=`${e}_${t}`,n=this.containers.get(a);n&&n.element&&(document.body.appendChild(n.element),n.element.style.cssText="\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            ",n.isVisible=!1,this.activeContainers.delete(a))}hideAllContainers(){this.containers.forEach(((e,t)=>{e.isVisible&&this.hideContainer(t.split("_")[0],t.split("_")[1])}))}clearOrderData(e,t){const a=`${e}_${t}`,n=this.containers.get(a);n&&(n.orderId=null,n.clientToken=null,n.instructions=null,n.isInitialized=!1)}setContainerInitialized(e,t){const a=`${e}_${t}`,n=this.containers.get(a);n&&(n.isInitialized=!0)}isContainerInitialized(e,t){const a=`${e}_${t}`,n=this.containers.get(a);return!!n&&n.isInitialized}setOrderData(e,t,a,n,o=null){const r=`${e}_${t}`,i=this.containers.get(r);i?(i.orderId=a,i.clientToken=n,i.instructions=o):console.warn(`[PrimerCheckoutManager] Container ${r} not found for setting order data`)}getOrderData(e,t){const a=`${e}_${t}`,n=this.containers.get(a);return n?{orderId:n.orderId,clientToken:n.clientToken,instructions:n.instructions}:{orderId:null,clientToken:null,instructions:null}}getContainerElement(e,t){const a=`${e}_${t}`,n=this.containers.get(a);return n?n.element:null}cleanup(){this.containers.forEach((e=>{e.element&&e.element.parentNode&&e.element.parentNode.removeChild(e.element)})),this.containers.clear(),this.activeContainers.clear()}};"undefined"!=typeof window&&window.addEventListener("beforeunload",(()=>{c.cleanup()}));if("undefined"!=typeof document){const e=document.createElement("style");e.type="text/css",e.innerText="\n@keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n}\n\n/* Custom styling for TextControl components */\n.components-text-control__input.has-error {\n    border-color: #ef4444 !important;\n}\n\n.components-text-control__input {\n    font-weight: 500;\n    font-size: 16px;\n    line-height: 1.21em;\n    color: #333;\n    padding: 12px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    background-color: #f8f9fa;\n    min-height: 48px;\n    box-sizing: border-box;\n}\n\n.components-text-control__input:focus {\n    border-color: #2CB5C5;\n    box-shadow: 0 0 0 1px #2CB5C5;\n}\n\n.components-text-control__input[readonly] {\n    background-color: #f8f9fa;\n    color: #333;\n    cursor: default;\n}\n\n/* Input adornment styling for currency prefix */\n.text-control-with-prefix {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n}\n\n.text-control-with-prefix .components-text-control__input {\n    padding-left: 24px !important;\n}\n\n.text-control-prefix {\n    position: absolute;\n    left: 10px;\n    top: 20.5%;\n    font-weight: 500;\n    font-size: 16px;\n    color: #333;\n    pointer-events: none;\n    z-index: 1;\n}\n\n/* Error state for prefixed input */\n.text-control-with-prefix .components-text-control__input.has-error + .text-control-prefix,\n.text-control-with-prefix .has-error .components-text-control__input + .text-control-prefix {\n    color: #ef4444;\n}\n",document.head.appendChild(e)}if("function"!=typeof e.registerPaymentMethod)console.warn("Monoova PayTo Block: registerPaymentMethod not available. Available globals:",Object.keys(window.wc||{}));else{let s={};if("function"==typeof t.getSetting)try{s=(0,t.getSetting)("monoova_payto_data",{})}catch(e){console.log("Monoova PayTo Block: getSetting failed:",e)}s&&0!==Object.keys(s).length||(s=window.monoova_payto_blocks_params||{},console.log("Monoova PayTo Block: Using fallback settings:",s)),s&&0!==Object.keys(s).length||(console.warn("Monoova PayTo Block: No settings found, using defaults"),s={title:"PayTo",description:"Set up PayTo directly from your bank using BSB and Account Number or PayID.",supports:[]});const m=(0,o.__)("PayTo","monoova-payments-for-woocommerce"),d=(0,n.decodeEntities)(s.title)||m,p=({eventRegistration:e,emitResponse:t,billing:n})=>{const{orderId:m}=(0,i.useSelect)((e=>({orderId:e(a.CHECKOUT_STORE_KEY).getOrderId()}))),d=()=>{const e=n?.billingAddress;return!!e&&!!(e.email&&e.first_name&&e.last_name&&e.address_1&&e.city&&e.postcode&&e.country&&e.state&&e.city)},{containerRef:p,isInitialized:u,setContainerInitialized:y,paymentMethod:g,setPaymentMethod:x,payidValue:f,setPayidValue:E,payidType:R,setPayidType:h,accountName:_,setAccountName:b,bsb:v,setBsb:w,accountNumber:C,setAccountNumber:S,maximumAmount:I,setMaximumAmount:T,errors:k,setErrors:N,paymentStatus:P,setPaymentStatus:A,paymentInstructions:D,setPaymentInstructions:F,isSubmitting:z,setIsSubmitting:B,pollInterval:O,setPollInterval:M,hasExpressCheckout:W,setHasExpressCheckout:j,errorDetails:$,setErrorDetails:L,mandateInfo:H,setMandateInfo:V,countdown:G,setCountdown:U,resetStates:q,stopPolling:Y,saveCurrentState:K}=(({settings:e,billing:t,orderId:a,containerId:n="payto-payment-container",paymentMethodId:o="monoova_payto",hasRequiredInfo:i=!0})=>{const[l,s]=(0,r.useState)("payid"),[m,d]=(0,r.useState)(""),[p,u]=(0,r.useState)(""),[y,g]=(0,r.useState)(""),[x,f]=(0,r.useState)(""),[E,R]=(0,r.useState)(""),[h,_]=(0,r.useState)(e.maximum_amount||1e3),[b,v]=(0,r.useState)({}),[w,C]=(0,r.useState)("form"),[S,I]=(0,r.useState)(null),[T,k]=(0,r.useState)(!1),[N,P]=(0,r.useState)(null),[A,D]=(0,r.useState)(!1),[F,z]=(0,r.useState)(null),[B,O]=(0,r.useState)(null),[M,W]=(0,r.useState)(86400),j=((0,r.useRef)(!1),(0,r.useRef)(null)),{targetRef:$,containerElement:L,containerIdActive:H,isContainerInitialized:V,setContainerInitialized:G,setOrderData:U,getOrderData:q,clearOrderData:Y,showContainer:K,hideContainer:X}=((e,t)=>{const a=(0,r.useRef)(null),n=(0,r.useRef)(!1),o=`persistent-${e}-${t}`;return(0,r.useEffect)((()=>{const r=c.getOrCreateContainer(e,t);return r.element&&(r.element.id=o),a.current&&!n.current&&(c.showContainer(e,t,a.current),n.current=!0),()=>{n.current&&(c.hideContainer(e,t),n.current=!1)}}),[e,t]),{targetRef:a,containerElement:c.getContainerElement(e,t),containerIdActive:o,isContainerInitialized:c.isContainerInitialized(e,t),setContainerInitialized:()=>c.setContainerInitialized(e,t),setOrderData:(a,n,o=null)=>c.setOrderData(e,t,a,n,o),getOrderData:()=>c.getOrderData(e,t),clearOrderData:()=>{c.clearOrderData(e,t)},showContainer:()=>{a.current&&!n.current&&(c.showContainer(e,t,a.current),n.current=!0)},hideContainer:()=>{n.current&&(c.hideContainer(e,t),n.current=!1)}}})(o,n),J=q(),Q=J?.instructions,Z=V&&Q,ee=(0,r.useCallback)((()=>{j.current&&(clearInterval(j.current),j.current=null),N&&(clearInterval(N),P(null))}),[N]);(0,r.useEffect)((()=>{Z&&Q&&(console.log("PayTo: Restoring persistent payment data:",Q),Q.formData&&(s(Q.formData.paymentMethod||"payid"),d(Q.formData.payidValue||""),u(Q.formData.payidType||""),g(Q.formData.accountName||""),f(Q.formData.bsb||""),R(Q.formData.accountNumber||""),_(Q.formData.maximumAmount||e.maximum_amount||1e3)),Q.paymentInstructions&&(I(Q.paymentInstructions),C(Q.paymentStatus||"instructions")),void 0!==Q.hasExpressCheckout&&D(Q.hasExpressCheckout),Q.mandateInfo&&O(Q.mandateInfo))}),[Z,Q,e.maximum_amount]);const te=(0,r.useCallback)((()=>{U(a,null,{formData:{paymentMethod:l,payidValue:m,payidType:p,accountName:y,bsb:x,accountNumber:E,maximumAmount:h},paymentInstructions:S,paymentStatus:w,hasExpressCheckout:A,mandateInfo:B,errors:b,errorDetails:F})}),[l,m,p,y,x,E,h,S,w,A,B,b,F,a,U]);(0,r.useEffect)((()=>{V&&te()}),[te,V]);const ae=(0,r.useCallback)((()=>{s("payid"),d(""),u(""),g(""),f(""),R(""),_(e.maximum_amount||1e3),v({}),C("form"),I(null),k(!1),D(!1),z(null),O(null),W(86400),ee(),Y&&Y()}),[e.maximum_amount,ee,Y]);return(0,r.useEffect)((()=>()=>{ee()}),[ee]),{containerRef:$,containerElement:L,containerIdActive:H,isInitialized:V,setContainerInitialized:G,showContainer:K,hideContainer:X,paymentMethod:l,setPaymentMethod:s,payidValue:m,setPayidValue:d,payidType:p,setPayidType:u,accountName:y,setAccountName:g,bsb:x,setBsb:f,accountNumber:E,setAccountNumber:R,maximumAmount:h,setMaximumAmount:_,errors:b,setErrors:v,paymentStatus:w,setPaymentStatus:C,paymentInstructions:S,setPaymentInstructions:I,isSubmitting:T,setIsSubmitting:k,pollInterval:N,setPollInterval:P,hasExpressCheckout:A,setHasExpressCheckout:D,errorDetails:F,setErrorDetails:z,mandateInfo:B,setMandateInfo:O,countdown:M,setCountdown:W,resetStates:ae,stopPolling:ee,saveCurrentState:te,persistentData:J,shouldUseExistingData:Z}})({settings:s,billing:n,orderId:m,containerId:"payto-payment-container",paymentMethodId:"monoova_payto",hasRequiredInfo:d()}),X=(0,i.useSelect)((e=>{const t=e("wc/store/cart");return t&&t.getCartTotals?t.getCartTotals():null}),[]);(0,r.useEffect)((()=>{const e=document.querySelector(".wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button");return e&&(e.style.display="none"),()=>{e&&(e.style.display="")}}),[]);const J=X?.total_price?parseFloat(X.total_price)/100:0,Q=X?.currency_code||s.currency||"AUD",{onPaymentSetup:Z}=e,{responseTypes:ee,noticeContexts:te}=t;(0,r.useEffect)((()=>{console.log("PayTo: Component mounted, checking express checkout availability..."),ae()}),[]),(0,r.useEffect)((()=>()=>{O&&clearInterval(O)}),[O]),(0,r.useEffect)((()=>{const e=setInterval((()=>{U((t=>t<=1?(clearInterval(e),0):t-1))}),1e3);return()=>clearInterval(e)}),[]);const ae=async()=>{if(s.is_user_logged_in)try{const e=await fetch(s.ajax_url||"/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"monoova_check_express_checkout",nonce:s.nonce})}),t=await e.json();t.success&&t.data.available?(j(!0),t.data.mandate_info&&V(t.data.mandate_info)):console.log("Express checkout not available or failed:",t)}catch(e){console.error("Error checking express checkout availability:",e)}else console.log("PayTo: User not logged in, skipping express checkout check")},ne=()=>{const e={};"payid"===g?f.trim()?R||(e.payidValue=(0,o.__)("Please enter a valid email address or phone number.","monoova-payments-for-woocommerce")):e.payidValue=(0,o.__)("PayID is required.","monoova-payments-for-woocommerce"):(_.trim()||(e.accountName=(0,o.__)("Account name is required.","monoova-payments-for-woocommerce")),v.trim()?/^\d{3}-?\d{3}$/.test(v.trim())||(e.bsb=(0,o.__)("Please enter a valid BSB (6 digits).","monoova-payments-for-woocommerce")):e.bsb=(0,o.__)("BSB is required.","monoova-payments-for-woocommerce"),C.trim()||(e.accountNumber=(0,o.__)("Account number is required.","monoova-payments-for-woocommerce")));const t=parseFloat(I);return(!I||""===I||isNaN(t)||t<=0)&&(e.maximumAmount=(0,o.__)("Maximum amount must be greater than 0.","monoova-payments-for-woocommerce")),N(e),0===Object.keys(e).length};React.useEffect((()=>Z((()=>ne()?{type:ee.SUCCESS,meta:{paymentMethodData:{payto_payment_method:g,payto_payid_type:"payid"===g?R:"",payto_payid_value:"payid"===g?f:"",payto_account_name:"bsb_account"===g?_:"",payto_bsb:"bsb_account"===g?v:"",payto_account_number:"bsb_account"===g?C:"",payto_maximum_amount:I.toString()}}}:{type:ee.ERROR,message:(0,o.__)("Please correct the errors in the PayTo form.","monoova-payments-for-woocommerce"),messageContext:te.PAYMENTS}))),[Z,g,R,f,_,v,C,I,ne]);const oe=e=>{const t=setInterval((async()=>{try{const a=await fetch(s.ajax_url||"/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"get_payto_agreement_payment_initiation_status",nonce:s.nonce,order_id:e})}),n=await a.json();if(n.success){const e=n.data.agreement_status||n.data.mandate_status,a=n.data.payment_initiation_status,o=n.data.order_status,r=n.data.error_details;if(console.log("PayTo polling status:",{agreementStatus:e,paymentStatus:a,orderStatus:o,errorDetails:r}),r&&r.requires_new_agreement){clearInterval(t),M(null),A("agreement_limit_exceeded");const e=r.errors||[],a=e.map((e=>e.message)).join(". ");return N({general:a}),void L({message:a,code:"AGREEMENT_LIMIT_EXCEEDED",type:"AGREEMENT_LIMIT_ERROR",context:"payment_initiation",agreement_uid:r.agreement_uid,requires_new_agreement:!0,limit_errors:e})}if(D&&F((t=>({...t,status:e||a||"Processing",agreement_status:e,payment_initiation_status:a,order_status:o,order_key:n.data.order_key||t.order_key,order_received_url:n.data.order_received_url||t.order_received_url}))),"completed"===a||"success"===a||"completed"===o||"processing"===o)clearInterval(t),M(null),A("success"),setTimeout((()=>{const e=n.data.order_received_url||D?.order_received_url;e&&(window.location.href=e)}),2e3);else if("failed"===a||"cancelled"===a||"failed"===e||"cancelled"===e||"rejected"===e)if(clearInterval(t),M(null),"payto_agreement_limit_exceeded"===r?.code||"AGREEMENT_LIMIT_ERROR"===r?.type||"payto_agreement_limit_exceeded"===n.data.error_code)A("agreement_limit_exceeded"),N({general:r?.message||n.data.message||"Payment agreement limits have been exceeded"}),L({message:r?.message||n.data.message||"Payment agreement limits have been exceeded",code:"AGREEMENT_LIMIT_EXCEEDED",type:"AGREEMENT_LIMIT_ERROR",context:"payment_polling",requires_new_agreement:!0,limit_errors:n.data?.errors||r?.limit_errors||[],agreement_status:e,payment_status:a,order_status:o});else{A("failed");let t="Payment was cancelled or failed",n="UNKNOWN_ERROR",r="PAYMENT_FAILED";"rejected"===e?(t="PayTo agreement was rejected by your bank",n="AGREEMENT_REJECTED",r="AGREEMENT_ERROR"):"cancelled"===e?(t="PayTo agreement was cancelled",n="AGREEMENT_CANCELLED",r="AGREEMENT_ERROR"):"failed"===e?(t="PayTo agreement creation failed",n="AGREEMENT_FAILED",r="AGREEMENT_ERROR"):"failed"===a?(t="Payment initiation failed",n="PAYMENT_FAILED",r="PAYMENT_ERROR"):"cancelled"===a&&(t="Payment was cancelled",n="PAYMENT_CANCELLED",r="PAYMENT_ERROR"),N({general:t}),L({message:t,code:n,type:r,context:"payment_polling",agreement_status:e,payment_status:a,order_status:o})}}}catch(e){console.error("Error checking payment status:",e)}}),5e3);M(t)};return"instructions"===P?D?React.createElement("div",{className:"monoova-payto-instructions-wrapper monoova-payid-bank-transfer-instructions-wrapper"},React.createElement("div",{style:{textAlign:"center",marginTop:"24px"}},React.createElement("div",{className:"monoova-scan-pay"},React.createElement("div",{className:"pay-label"},"Pay"),React.createElement("div",{class:"amount"},React.createElement("span",{class:"woocommerce-Price-amount amount"},React.createElement("bdi",null,React.createElement("span",{class:"woocommerce-Price-currencySymbol"},"$"),D.amount))))),React.createElement("div",null,React.createElement("div",{className:"monoova-instruction-method-selection"},React.createElement("div",{style:{fontWeight:600}},"Pay with"),React.createElement("div",{style:{display:"flex",gap:"16px",alignItems:"center"}},React.createElement("label",{style:{display:"flex",alignItems:"center",cursor:"pointer",gap:"8px"}},React.createElement("div",{style:{position:"relative"}},React.createElement("div",{style:{width:"24px",height:"24px",border:"1.5px solid "+("payid"===g?"#2CB5C5":"#ABABAB"),borderRadius:"50%",position:"relative"}},"payid"===g&&React.createElement("div",{style:{width:"14px",height:"14px",backgroundColor:"#2CB5C5",borderRadius:"50%",position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)"}}))),React.createElement("span",{style:{fontWeight:"400",fontSize:"16px",lineHeight:"1.5em",color:"#333"}},"PayID")),React.createElement("label",{style:{display:"flex",alignItems:"center",cursor:"pointer",gap:"8px"}},React.createElement("div",{style:{position:"relative"}},React.createElement("div",{style:{width:"24px",height:"24px",border:"1.5px solid "+("bsb_account"===g?"#2CB5C5":"#ABABAB"),borderRadius:"50%",position:"relative"}},"bsb_account"===g&&React.createElement("div",{style:{width:"14px",height:"14px",backgroundColor:"#2CB5C5",borderRadius:"50%",position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)"}}))),React.createElement("span",{style:{fontWeight:"400",fontSize:"16px",lineHeight:"1.5em",color:"#333"}},"BSB and account number"))))),React.createElement("div",{style:{border:"1px solid #E8E8E8",borderRadius:"16px",padding:"24px 24px 32px",gap:"40px",display:"flex",flexDirection:"column",alignItems:"center"}},"payid"===g&&React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Enter your PayID")),React.createElement(l.TextControl,{value:f||"",readOnly:!0,placeholder:"0412 345 678"})),"bsb_account"===g&&React.createElement(React.Fragment,null,React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000",display:"block",marginBottom:"4px"}},"Name associated with bank account")),React.createElement(l.TextControl,{value:_||"",readOnly:!0,placeholder:"Enter your name"})),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"500",fontSize:"12px",lineHeight:"1.5em",color:"#666",display:"block",marginBottom:"4px"}},"BSB")),React.createElement(l.TextControl,{value:v||"",readOnly:!0,placeholder:"123-456"})),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"500",fontSize:"12px",lineHeight:"1.5em",color:"#666",display:"block",marginBottom:"4px"}},"Account Number")),React.createElement(l.TextControl,{value:C||"",readOnly:!0,placeholder:"Enter your account number"}))),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column",gap:"12px"}},React.createElement("div",{style:{display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"12px",lineHeight:"1.5em",color:"#666",display:"block",marginBottom:"4px"}},"Maximum payment agreement amount")),React.createElement("div",{className:"text-control-with-prefix"},React.createElement("span",{className:"text-control-prefix"},"$"),React.createElement(l.TextControl,{value:`${parseFloat(I)||1e3}`,readOnly:!0}))),React.createElement("div",{style:{fontWeight:"400",fontSize:"14px",lineHeight:"1.5",color:"#555"}},"This is the maximum amount that can be charged under this PayTo agreement. You can modify this amount if needed.")),React.createElement(l.Button,{style:{width:"100%",display:"flex",justifyContent:"center",alignItems:"center",gap:"10px",backgroundColor:"#2CB5C5",borderRadius:"8px",minHeight:"48px",opacity:.6,cursor:"not-allowed"}},React.createElement("span",{style:{fontWeight:"700",fontSize:"16px",lineHeight:"1.21em",color:"#000000"}},"Accept and Continue")),React.createElement("div",{style:{width:"100%"}},React.createElement("h4",{style:{fontSize:"18px",fontWeight:"600",color:"#333",marginBottom:"16px"}},"Authorise recurring payments"),(()=>{D?.status?.toLowerCase();const e=D?.agreement_status?.toLowerCase()||"pending",t=D?.payment_initiation_status?.toLowerCase()||"pending";return"completed"===t||"success"===t||"processed"===t?React.createElement("div",null,React.createElement("div",{style:{marginBottom:"16px"}},React.createElement("div",{style:{display:"flex",alignItems:"center",gap:"12px",borderRadius:"8px",marginBottom:"8px"}},React.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",backgroundColor:"#2CB5C5",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0}},React.createElement("svg",{width:"12",height:"9",viewBox:"0 0 12 9",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement("path",{d:"M1 4.5L4.5 8L11 1.5",stroke:"#ffffff",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}))),React.createElement("span",{style:{fontSize:"16px",color:"#484848",fontWeight:"400"}},"Payment received"))),React.createElement("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px",padding:"14px 10px",backgroundColor:"#E6F7FF",borderRadius:"8px",marginBottom:"16px"}},React.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",backgroundColor:"#1890FF",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,marginTop:"1px"}},React.createElement("span",{style:{color:"#ffffff",fontSize:"12px",fontWeight:"bold"}},"i")),React.createElement("p",{style:{margin:0,fontSize:"14px",color:"#1890FF",lineHeight:"1.5",fontWeight:"500"}},"We will redirect you to Order Received page in"," ",React.createElement("strong",null,"5 sec")))):"authorized"!==e&&"approved"!==e||"initiated"!==t&&"processing"!==t?"failed"===t||"cancelled"===t||"rejected"===t||"failed"===e||"cancelled"===e||"rejected"===e?React.createElement("div",null,React.createElement("div",{style:{marginBottom:"16px",textAlign:"center"}},React.createElement("div",{style:{width:"22px",height:"22px",borderRadius:"50%",backgroundColor:"#FF0000",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 16px"}},React.createElement("span",{style:{color:"#ffffff",fontSize:"14px",fontWeight:"bold"}},"i")),React.createElement("h3",{style:{fontSize:"24px",fontWeight:"600",color:"#484848",margin:"0 0 8px 0"}},"Payment failed"),React.createElement("p",{style:{fontSize:"16px",color:"#484848",margin:"0 0 24px 0",lineHeight:"1.5",fontWeight:"400"}},k.general||$?.message||"Something went wrong with the payment.",React.createElement("br",null),"Please try again."),$&&React.createElement("div",{style:{marginBottom:"24px",padding:"16px",backgroundColor:"#FFF2F2",border:"1px solid #FFCDD2",borderRadius:"8px",textAlign:"left",fontSize:"14px"}},React.createElement("h4",{style:{margin:"0 0 12px 0",color:"#D32F2F",fontSize:"14px",fontWeight:"600"}},"Error Details"),$.code&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Error Code:")," ",React.createElement("span",{style:{color:"#666"}},$.code)),$.type&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Error Type:")," ",React.createElement("span",{style:{color:"#666"}},$.type)),$.context&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Context:")," ",React.createElement("span",{style:{color:"#666"}},$.context)),$.validation_errors&&React.createElement("div",{style:{marginTop:"12px",fontSize:"13px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Validation Errors:"),React.createElement("div",{style:{marginTop:"4px",color:"#666"}},"object"==typeof $.validation_errors?Object.entries($.validation_errors).map((([e,t],a)=>React.createElement("div",{key:a,style:{marginBottom:"4px"}},React.createElement("strong",null,e,":")," ",t))):$.validation_errors))),React.createElement("button",{onClick:()=>{A("form"),N({}),L(null),F(null)},style:{width:"100%",padding:"12px 20px",backgroundColor:"#2CB5C5",color:"#000000",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"700",lineHeight:"1.21em"}},"Try Again"))):React.createElement("div",null,React.createElement("div",{style:{marginBottom:"16px"}},React.createElement("div",{style:{display:"flex",alignItems:"flex-start",gap:"12px",padding:"14px 10px",backgroundColor:"#EDFDFF",borderRadius:"8px",marginBottom:"8px"}},React.createElement("div",{style:{width:"20px",height:"20px",borderRadius:"50%",backgroundColor:"#007A89",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,marginTop:"1px"}},React.createElement("span",{style:{color:"#ffffff",fontSize:"12px",fontWeight:"bold"}},"!")),React.createElement("p",{style:{margin:0,fontSize:"14px",color:"#007A89",lineHeight:"1.5",fontWeight:"500"}},"Approve your recurring payment in your online banking or banking app")),React.createElement("div",{style:{display:"flex",alignItems:"center",gap:"12px",borderRadius:"8px"}},React.createElement("div",{style:{width:"20px",height:"20px",border:"2px solid #ffffff",borderTop:"2px solid #007A89",borderRadius:"50%",animation:"spin 1s linear infinite",flexShrink:0}}),React.createElement("span",{style:{fontSize:"16px",color:"#000000",fontWeight:"500"}},"Awaiting your approval")))):React.createElement("div",null,React.createElement("div",{style:{marginBottom:"16px"}},React.createElement("div",{style:{display:"flex",alignItems:"center",gap:"12px",borderRadius:"8px",marginBottom:"8px"}},React.createElement("div",{style:{width:"20px",height:"20px",border:"2px solid #ffffff",borderTop:"2px solid #2CB5C5",borderRadius:"50%",animation:"spin 1s linear infinite",flexShrink:0}}),React.createElement("span",{style:{fontSize:"16px",color:"#484848",fontWeight:"400"}},"Initiate payment"))))})()))):null:"agreement_limit_exceeded"===P?React.createElement(React.Fragment,null,React.createElement("div",null,React.createElement("div",{style:{textAlign:"center",margin:"24px 0"}},React.createElement("div",{style:{fontSize:"24px",fontWeight:"700",color:"#000",marginBottom:"8px"}},"Payment Agreement Limit Exceeded"),React.createElement("p",{style:{fontSize:"14px",color:"#6b7280",margin:"0"}},"Your current payment agreement has reached its limits.")),React.createElement("div",{style:{backgroundColor:"#FFF2F2",border:"1px solid #FFCDD2",borderRadius:"8px",padding:"16px",marginBottom:"24px"}},React.createElement("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"}},React.createElement("span",{style:{fontSize:"18px",marginRight:"8px"}},"⚠️"),React.createElement("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#D32F2F",margin:"0"}},"Agreement Limits Exceeded")),React.createElement("p",{style:{fontSize:"16px",color:"#000000",margin:"0 0 16px 0",lineHeight:"1.5"}},k.general||$?.message||"Payment agreement limits have been exceeded."),$?.limit_errors&&$.limit_errors.length>0&&React.createElement("div",{style:{marginBottom:"16px"}},React.createElement("strong",{style:{color:"#D32F2F",fontSize:"14px"}},"Specific Issues:"),React.createElement("ul",{style:{margin:"8px 0 0 20px",color:"#666",fontSize:"14px"}},$.limit_errors.map(((e,t)=>React.createElement("li",{key:t,style:{marginBottom:"4px"}},e.message))))),React.createElement("p",{style:{fontSize:"14px",color:"#666",margin:"0"}},"To continue with your payment, you'll need to create a new payment agreement with updated limits.")),React.createElement("button",{onClick:async()=>{B(!0),N({}),L(null);try{const e=n?.billingAddress||{},t=await fetch(s.ajax_url||"/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"monoova_payto_reset_agreement",nonce:s.nonce,order_id:D?.order_id||m,customer_email:e.email||""})}),a=await t.json();a.success?(F(null),A("form"),N({}),L(null),x("payid"),h("email"),E(""),b(""),w(""),S(""),T(J)):N({general:a.data?.message||"Failed to reset payment agreement"})}catch(e){N({general:"Network error occurred while resetting payment agreement"})}finally{B(!1)}},disabled:z,style:{width:"100%",padding:"12px 20px",backgroundColor:z?"#ccc":"#2CB5C5",color:"white",border:"none",borderRadius:"8px",fontSize:"16px",fontWeight:"600",cursor:z?"not-allowed":"pointer",transition:"background-color 0.2s",marginBottom:"16px"},onMouseOver:e=>{z||(e.target.style.backgroundColor="#1a9aa8")},onMouseOut:e=>{z||(e.target.style.backgroundColor="#2CB5C5")}},z?React.createElement(React.Fragment,null,React.createElement("span",{style:{display:"inline-block",width:"16px",height:"16px",border:"2px solid #ffffff",borderTop:"2px solid transparent",borderRadius:"50%",animation:"spin 1s linear infinite",marginRight:"8px"}}),"Resetting Agreement..."):"Try Again with New Agreement"))):"failed"===P?React.createElement(React.Fragment,null,React.createElement("div",null,React.createElement("div",{style:{textAlign:"center",margin:"24px 0"}},React.createElement("div",{className:"monoova-scan-pay"},React.createElement("div",{className:"pay-label"},"Pay"),React.createElement("div",{class:"amount"},React.createElement("span",{class:"woocommerce-Price-amount amount"},React.createElement("bdi",null,React.createElement("span",{class:"woocommerce-Price-currencySymbol"},"$"),J.toFixed(2)))))),React.createElement("div",{style:{border:"1px solid #E8E8E8",borderRadius:"16px",padding:"24px 24px 32px",gap:"16px",display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center"}},React.createElement("div",{style:{width:"45px",height:"45px",borderRadius:"50%",backgroundColor:"#FF0000",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 16px"}},React.createElement("span",{style:{color:"#ffffff",fontSize:"24px",fontWeight:"bold"}},"i")),React.createElement("h3",{style:{fontSize:"24px",fontWeight:"600",color:"#000000",margin:"0 0 8px 0"}},"Payment failed"),React.createElement("p",{style:{fontSize:"16px",color:"#000000",margin:"0 0 24px 0",lineHeight:"1.5"}},k.general||$?.message||"Something went wrong with the payment."),$&&React.createElement("div",{style:{marginBottom:"24px",padding:"16px",backgroundColor:"#FFF2F2",border:"1px solid #FFCDD2",borderRadius:"8px",textAlign:"left",fontSize:"14px"}},React.createElement("h4",{style:{margin:"0 0 12px 0",color:"#D32F2F",fontSize:"14px",fontWeight:"600"}},"Error Details"),$.code&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Error Code:")," ",React.createElement("span",{style:{color:"#666"}},$.code)),$.type&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Error Type:")," ",React.createElement("span",{style:{color:"#666"}},$.type)),$.context&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Context:")," ",React.createElement("span",{style:{color:"#666"}},$.context)),($.agreement_status||$.payment_status||$.order_status)&&React.createElement("div",{style:{marginBottom:"8px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Status:")," ",React.createElement("span",{style:{color:"#666"}},[$.agreement_status,$.payment_status,$.order_status].filter(Boolean).join(", "))),$.details&&React.createElement("div",{style:{marginTop:"12px",fontSize:"13px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Technical Details:"),React.createElement("div",{style:{marginTop:"4px",padding:"8px",backgroundColor:"#FFFFFF",border:"1px solid #E0E0E0",borderRadius:"4px",color:"#666",fontFamily:"monospace",fontSize:"12px",wordBreak:"break-word"}},"string"==typeof $.details?$.details:JSON.stringify($.details,null,2))),$.validation_errors&&React.createElement("div",{style:{marginTop:"12px",fontSize:"13px"}},React.createElement("strong",{style:{color:"#D32F2F"}},"Validation Errors:"),React.createElement("div",{style:{marginTop:"4px",color:"#666"}},"object"==typeof $.validation_errors?Object.entries($.validation_errors).map((([e,t],a)=>React.createElement("div",{key:a,style:{marginBottom:"4px"}},React.createElement("strong",null,e,":")," ",t))):$.validation_errors))),React.createElement("button",{onClick:()=>{A("form"),N({}),L(null),F(null)},style:{width:"100%",padding:"12px 20px",backgroundColor:"#2CB5C5",color:"#000000",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"700",lineHeight:"1.21em"}},"Try Again")))):React.createElement(React.Fragment,null,React.createElement("div",{className:"monoova-payto-instructions-wrapper monoova-payid-bank-transfer-instructions-wrapper",ref:p},React.createElement("div",{style:{textAlign:"center",marginTop:"24px"}},React.createElement("div",{className:"monoova-scan-pay"},React.createElement("div",{className:"pay-label"},"Pay"),React.createElement("div",{class:"amount"},React.createElement("span",{class:"woocommerce-Price-amount amount"},React.createElement("bdi",null,React.createElement("span",{class:"woocommerce-Price-currencySymbol"},"$"),J.toFixed(2)))))),!d()&&React.createElement("div",{style:{padding:"16px",backgroundColor:"#f3f4f6",borderRadius:"8px",margin:"16px 0",textAlign:"center"}},React.createElement("p",{style:{margin:"0 0 8px 0",fontSize:"14px",color:"#374151"}},"Please complete your billing information to initialize the payment form."),React.createElement("small",{style:{fontSize:"12px",color:"#6b7280"}},"Required: Email, Name, Address, City, Postcode, and Country")),W&&s.is_user_logged_in&&React.createElement("div",{style:{marginBottom:"20px",textAlign:"center"}},H&&React.createElement("div",{style:{marginBottom:"16px",padding:"16px",backgroundColor:"#f0f8ff",border:"1px solid #0073aa",borderRadius:"8px",textAlign:"left"}},React.createElement("h4",{style:{margin:"0 0 12px 0",color:"#0073aa",fontSize:"16px"}},(0,o.__)("Express Checkout Available","monoova-payments-for-woocommerce")),React.createElement("p",{style:{margin:"0 0 12px 0",color:"#666",fontSize:"14px"}},(0,o.__)("You have an active PayTo mandate that can be used for faster checkout.","monoova-payments-for-woocommerce")),React.createElement("div",{style:{backgroundColor:"#ffffff",padding:"12px",borderRadius:"4px",fontSize:"13px",color:"#333"}},React.createElement("div",{style:{marginBottom:"6px"}},React.createElement("strong",null,(0,o.__)("Agreement:","monoova-payments-for-woocommerce"))," ",H.agreement_id?`...${H.agreement_id.slice(-8)}`:"Available"),React.createElement("div",{style:{marginBottom:"6px"}},React.createElement("strong",null,(0,o.__)("Maximum Amount:","monoova-payments-for-woocommerce"))," ",Q," $",parseFloat(H.maximum_amount||0).toFixed(2)),React.createElement("div",{style:{marginBottom:"6px"}},React.createElement("strong",null,(0,o.__)("Status:","monoova-payments-for-woocommerce"))," ",React.createElement("span",{style:{color:"authorized"===H.status?"#10b981":"#f59e0b",fontWeight:"500"}},H.status||"Active")),H.num_of_transactions_permitted&&React.createElement("div",null,React.createElement("strong",null,(0,o.__)("Transactions:","monoova-payments-for-woocommerce"))," ",React.createElement("span",{style:{color:H.remaining_transactions>0?"#10b981":"#ef4444",fontWeight:"500"}},H.remaining_transactions||0," remaining")," ",React.createElement("span",{style:{color:"#6b7280",fontSize:"12px"}},"(",H.num_of_paid_transactions||0,"/",H.num_of_transactions_permitted||10," used)")))),React.createElement("button",{type:"button",onClick:async()=>{B(!0),A("processing"),N({}),L(null);try{const e=await fetch(s.ajax_url||"/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"monoova_process_payment_with_existing_mandate",nonce:s.nonce,order_id:m,payment_agreement_uid:H?.agreement_id})}),t=await e.json();if(t.success)F({order_id:t.data.order_id,currency:Q,amount:J.toFixed(2),status:"Processing",agreement_reference:t.data.order_id,message:"Payment initiated with existing mandate"}),A("instructions"),oe(t.data.order_id);else{const e=t.data?.message||"Express checkout failed",a=t.data?.error_code||"UNKNOWN_ERROR",n=t.data?.error_type||"EXPRESS_CHECKOUT_ERROR";"payto_agreement_limit_exceeded"===a||"AGREEMENT_LIMIT_ERROR"===n?(A("agreement_limit_exceeded"),N({general:e}),L({message:e,code:"AGREEMENT_LIMIT_EXCEEDED",type:"AGREEMENT_LIMIT_ERROR",context:"express_checkout",requires_new_agreement:!0,limit_errors:t.data?.errors||[],agreement_uid:t.data?.agreement_uid})):(A("failed"),N({general:e}),L({message:e,code:a,type:n,context:"express_checkout",details:t.data?.error_details||null}))}}catch(e){A("failed"),N({general:"Network error occurred during express checkout"}),L({message:"Network error occurred during express checkout",code:"NETWORK_ERROR",type:"CONNECTION_ERROR",context:"express_checkout",details:e.message})}finally{B(!1)}},disabled:z,style:{width:"100%",padding:"12px 20px",backgroundColor:z?"#9ca3af":"#10b981",color:"#ffffff",border:"none",borderRadius:"8px",cursor:z?"not-allowed":"pointer",fontSize:"14px",fontWeight:"600",marginBottom:"12px"}},z?(0,o.__)("Processing...","monoova-payments-for-woocommerce"):(0,o.__)("Pay with existing PayTo mandate","monoova-payments-for-woocommerce")),React.createElement("div",{style:{fontSize:"12px",color:"#6b7280",marginBottom:"16px"}},(0,o.__)("or set up a new payment method below","monoova-payments-for-woocommerce"))),d()&&React.createElement(React.Fragment,null,React.createElement("div",{style:{marginBottom:"24px"}},React.createElement("div",{className:"monoova-instruction-method-selection"},React.createElement("div",{style:{fontWeight:600}},"Pay with"),React.createElement(l.RadioControl,{selected:g,options:[{label:"PayID",value:"payid"},{label:"BSB and account number",value:"bsb_account"}],onChange:e=>x(e)}))),React.createElement("div",{style:{border:"1px solid #E8E8E8",borderRadius:"16px",padding:"24px 24px 32px",gap:"16px",display:"flex",flexDirection:"column",alignItems:"center"}},"payid"===g&&React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Enter your PayID")),React.createElement(l.TextControl,{value:f,onChange:e=>{E(e),(e=>{if(!e.trim())return h(""),void N((e=>({...e,payidValue:""})));/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?(h("Email"),N((e=>({...e,payidValue:""})))):/^(\+61|0)[0-9]{9}$/.test(e)?(h("PhoneNumber"),N((e=>({...e,payidValue:""})))):(h(""),N((e=>({...e,payidValue:(0,o.__)("Please enter a valid email address or phone number.","monoova-payments-for-woocommerce")}))))})(e)},placeholder:"0412 345 678",className:k.payidValue?"has-error":""}),k.payidValue&&React.createElement("div",{style:{color:"#ef4444",fontSize:"12px",marginTop:"6px",display:"flex",alignItems:"center",gap:"4px"}},k.payidValue)),"bsb_account"===g&&React.createElement(React.Fragment,null,React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Name associated with bank account")),React.createElement(l.TextControl,{value:_,onChange:b,placeholder:"Enter your name",className:k.accountName?"has-error":""}),k.accountName&&React.createElement("div",{style:{color:"#ef4444",fontSize:"12px",marginTop:"6px",display:"flex",alignItems:"center",gap:"4px"}},k.accountName)),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"BSB")),React.createElement(l.TextControl,{value:v,onChange:w,placeholder:"123-456",className:k.bsb?"has-error":""}),k.bsb&&React.createElement("div",{style:{color:"#ef4444",fontSize:"12px",marginTop:"6px",display:"flex",alignItems:"center",gap:"4px"}},k.bsb)),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Account Number")),React.createElement(l.TextControl,{value:C,onChange:S,placeholder:"Enter your account number",className:k.accountNumber?"has-error":""}),k.accountNumber&&React.createElement("div",{style:{color:"#ef4444",fontSize:"12px",marginTop:"6px",display:"flex",alignItems:"center",gap:"4px"}},k.accountNumber))),React.createElement("div",{style:{width:"100%",display:"flex",flexDirection:"column",gap:"12px"}},React.createElement("div",{style:{display:"flex",flexDirection:"column"}},React.createElement("div",{style:{display:"flex",alignItems:"center"}},React.createElement("label",{style:{fontWeight:"600",fontSize:"16px",lineHeight:"1.5em",color:"#000000"}},"Maximum payment agreement amount")),React.createElement("div",{className:"text-control-with-prefix"},React.createElement("span",{className:"text-control-prefix"},"$"),React.createElement(l.TextControl,{type:"number",value:I,onChange:e=>{if(""===e||null==e)return void T("");const t=e.toString().replace(",",".");if("."===t||t.endsWith(".")||/^\d*\.?\d*$/.test(t))return void T(t);const a=parseFloat(t);isNaN(a)||T(a)},min:"0.01",step:"0.01",placeholder:"1000",className:k.maximumAmount?"has-error":""})),k.maximumAmount&&React.createElement("div",{style:{color:"#ef4444",fontSize:"12px",marginTop:"6px",display:"flex",alignItems:"center",gap:"4px"}},k.maximumAmount)),React.createElement("div",{style:{fontWeight:"500",fontSize:"14px",lineHeight:"1.29em",color:"#909090"}},"This is the maximum amount that can be charged under this PayTo agreement. You can modify this amount if needed.")),React.createElement(l.Button,{style:{width:"100%",display:"flex",justifyContent:"center",alignItems:"center",gap:"10px",backgroundColor:"#2CB5C5",borderRadius:"8px",minHeight:"48px",cursor:z?"not-allowed":"pointer"},onClick:z?void 0:async e=>{if(e&&(e.preventDefault(),e.stopPropagation()),ne()){B(!0),A("processing"),N({}),L(null),V(null),j(!1);try{let e=n?.billingAddress||{};const t=await fetch(s.ajax_url||"/wp-admin/admin-ajax.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"monoova_create_payto_agreement",nonce:s.nonce,payto_payment_method:g,payto_payid_type:"payid"===g?R:"",payto_payid_value:"payid"===g?f:"",payto_account_name:"bsb_account"===g?_:"",payto_bsb:"bsb_account"===g?v:"",payto_account_number:"bsb_account"===g?C:"",payto_maximum_amount:I.toString(),billing_first_name:e.first_name||"",billing_last_name:e.last_name||"",billing_email:e.email||"",order_id:m,isCheckoutPage:!0})}),a=await t.json();if(a.success)F({order_id:a.data.order_id,order_key:a.data.order_key,order_received_url:a.data.order_received_url,currency:Q,amount:J.toFixed(2),status:"Processing",agreement_reference:a.data.order_id,message:"Payment agreement created successfully"}),A("instructions"),oe(a.data.order_id);else{const e=a.data?.message||"Failed to create payment agreement",t=a.data?.error_code||"UNKNOWN_ERROR",n=a.data?.error_type||"AGREEMENT_CREATION_ERROR";A("failed"),N({general:e}),L({message:e,code:t,type:n,context:"agreement_creation",details:a.data?.error_details||null,validation_errors:a.data?.validation_errors||null})}}catch(e){A("failed"),N({general:"Network error occurred while creating payment agreement"}),L({message:"Network error occurred while creating payment agreement",code:"NETWORK_ERROR",type:"CONNECTION_ERROR",context:"agreement_creation",details:e.message})}finally{B(!1)}}}},React.createElement("span",{style:{fontWeight:"700",fontSize:"16px",lineHeight:"1.21em",color:"#000000"}},z||"processing"===P?"Processing...":"Accept and Continue"))))))},u=function(e){const t=(0,n.decodeEntities)(s.description||"");return React.createElement("div",{className:"monoova-payto-content"},React.createElement("div",{className:"monoova-payto-description",dangerouslySetInnerHTML:{__html:t}}),React.createElement(p,e))},y=e=>React.createElement("div",{style:{width:"100%"}},React.createElement("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"}},React.createElement("span",{style:{fontWeight:600}},d),React.createElement("img",{src:`${s.plugin_url||"/wp-content/plugins/monoova-payments-for-woocommerce/"}assets/images/payto-logo.svg`,alt:"PayTo logo",style:{height:"24px",width:"auto"}}))),g={name:"monoova_payto",label:React.createElement(y,null),content:React.createElement(u,null),edit:React.createElement(u,null),canMakePayment:()=>!0,ariaLabel:d,supports:{features:s.supports||[]},icons:s.icons||null};try{(0,e.registerPaymentMethod)(g)}catch(e){console.error("Monoova PayTo Block: Failed to register payment method:",e)}}})();