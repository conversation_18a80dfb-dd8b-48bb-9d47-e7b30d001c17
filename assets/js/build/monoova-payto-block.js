/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./assets/js/src/hooks/usePayToPaymentContainer.js":
/*!*********************************************************!*\
  !*** ./assets/js/src/hooks/usePayToPaymentContainer.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   usePayToPaymentContainer: () => (/* binding */ usePayToPaymentContainer)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _usePersistentPaymentDetailsContainer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./usePersistentPaymentDetailsContainer */ "./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js");


const usePayToPaymentContainer = ({
  settings,
  billing,
  orderId: existingOrderId,
  containerId = "payto-payment-container",
  paymentMethodId = "monoova_payto",
  hasRequiredInfo = true
}) => {
  // PayTo-specific state management
  const [paymentMethod, setPaymentMethod] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)("payid");
  const [payidValue, setPayidValue] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)("");
  const [payidType, setPayidType] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)("");
  const [accountName, setAccountName] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)("");
  const [bsb, setBsb] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)("");
  const [accountNumber, setAccountNumber] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)("");
  const [maximumAmount, setMaximumAmount] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(settings.maximum_amount || 1000);
  const [errors, setErrors] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)({});
  const [paymentStatus, setPaymentStatus] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)("form");
  const [paymentInstructions, setPaymentInstructions] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [isSubmitting, setIsSubmitting] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [pollInterval, setPollInterval] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [hasExpressCheckout, setHasExpressCheckout] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [errorDetails, setErrorDetails] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [mandateInfo, setMandateInfo] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const [countdown, setCountdown] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(86400);

  // Use refs to prevent duplicate API calls
  const isProcessingRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
  const pollingIntervalRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);

  // Use persistent container management
  const {
    targetRef,
    containerElement,
    containerIdActive,
    isContainerInitialized,
    setContainerInitialized,
    setOrderData,
    getOrderData,
    clearOrderData,
    showContainer,
    hideContainer
  } = (0,_usePersistentPaymentDetailsContainer__WEBPACK_IMPORTED_MODULE_1__.usePersistentPaymentDetailsContainer)(paymentMethodId, containerId);

  // Get persistent data
  const persistentData = getOrderData();
  const persistentPaymentData = persistentData?.instructions;
  const persistentOrderId = persistentData?.orderId;

  // Check if we should use existing data
  const shouldUseExistingData = isContainerInitialized && persistentPaymentData;

  // Stop polling utility
  const stopPolling = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    if (pollInterval) {
      clearInterval(pollInterval);
      setPollInterval(null);
    }
  }, [pollInterval]);

  // Initialize with persistent data if available
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    if (shouldUseExistingData && persistentPaymentData) {
      console.log("PayTo: Restoring persistent payment data:", persistentPaymentData);

      // Restore form state
      if (persistentPaymentData.formData) {
        setPaymentMethod(persistentPaymentData.formData.paymentMethod || "payid");
        setPayidValue(persistentPaymentData.formData.payidValue || "");
        setPayidType(persistentPaymentData.formData.payidType || "");
        setAccountName(persistentPaymentData.formData.accountName || "");
        setBsb(persistentPaymentData.formData.bsb || "");
        setAccountNumber(persistentPaymentData.formData.accountNumber || "");
        setMaximumAmount(persistentPaymentData.formData.maximumAmount || settings.maximum_amount || 1000);
      }

      // Restore payment state
      if (persistentPaymentData.paymentInstructions) {
        setPaymentInstructions(persistentPaymentData.paymentInstructions);
        setPaymentStatus(persistentPaymentData.paymentStatus || "instructions");
      }

      // Restore express checkout state
      if (persistentPaymentData.hasExpressCheckout !== undefined) {
        setHasExpressCheckout(persistentPaymentData.hasExpressCheckout);
      }
      if (persistentPaymentData.mandateInfo) {
        setMandateInfo(persistentPaymentData.mandateInfo);
      }
    }
  }, [shouldUseExistingData, persistentPaymentData, settings.maximum_amount]);

  // Save current state to persistent storage
  const saveCurrentState = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {
    const currentState = {
      formData: {
        paymentMethod,
        payidValue,
        payidType,
        accountName,
        bsb,
        accountNumber,
        maximumAmount
      },
      paymentInstructions,
      paymentStatus,
      hasExpressCheckout,
      mandateInfo,
      errors,
      errorDetails
    };
    setOrderData(existingOrderId, null, currentState);
  }, [paymentMethod, payidValue, payidType, accountName, bsb, accountNumber, maximumAmount, paymentInstructions, paymentStatus, hasExpressCheckout, mandateInfo, errors, errorDetails, existingOrderId, setOrderData]);

  // Save state whenever it changes
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    if (isContainerInitialized) {
      saveCurrentState();
    }
  }, [saveCurrentState, isContainerInitialized]);

  // Reset all states
  const resetStates = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {
    setPaymentMethod("payid");
    setPayidValue("");
    setPayidType("");
    setAccountName("");
    setBsb("");
    setAccountNumber("");
    setMaximumAmount(settings.maximum_amount || 1000);
    setErrors({});
    setPaymentStatus("form");
    setPaymentInstructions(null);
    setIsSubmitting(false);
    setHasExpressCheckout(false);
    setErrorDetails(null);
    setMandateInfo(null);
    setCountdown(86400);
    stopPolling();
    if (clearOrderData) {
      clearOrderData();
    }
  }, [settings.maximum_amount, stopPolling, clearOrderData]);

  // Cleanup on unmount
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);
  return {
    // Container management
    containerRef: targetRef,
    containerElement,
    containerIdActive,
    isInitialized: isContainerInitialized,
    setContainerInitialized,
    showContainer,
    hideContainer,
    // State management
    paymentMethod,
    setPaymentMethod,
    payidValue,
    setPayidValue,
    payidType,
    setPayidType,
    accountName,
    setAccountName,
    bsb,
    setBsb,
    accountNumber,
    setAccountNumber,
    maximumAmount,
    setMaximumAmount,
    errors,
    setErrors,
    paymentStatus,
    setPaymentStatus,
    paymentInstructions,
    setPaymentInstructions,
    isSubmitting,
    setIsSubmitting,
    pollInterval,
    setPollInterval,
    hasExpressCheckout,
    setHasExpressCheckout,
    errorDetails,
    setErrorDetails,
    mandateInfo,
    setMandateInfo,
    countdown,
    setCountdown,
    // Utilities
    resetStates,
    stopPolling,
    saveCurrentState,
    // Persistent data
    persistentData,
    shouldUseExistingData
  };
};

/***/ }),

/***/ "./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js":
/*!*********************************************************************!*\
  !*** ./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   usePersistentPaymentDetailsContainer: () => (/* binding */ usePersistentPaymentDetailsContainer)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);


// Global container management for persistent Primer checkout
class PrimerCheckoutManager {
  constructor() {
    this.containers = new Map();
    this.activeContainers = new Set();
    this.isInitialized = false;
  }

  // Create or get existing container
  getOrCreateContainer(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    if (!this.containers.has(containerKey)) {
      // Create container element
      const container = document.createElement('div');
      container.id = containerId;
      container.className = 'primer-checkout-persistent-container';
      container.style.cssText = `
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 100%;
                visibility: hidden;
                opacity: 0;
                pointer-events: none;
                transition: all 0.3s ease;
            `;

      // Append to body to keep it persistent
      document.body.appendChild(container);
      this.containers.set(containerKey, {
        element: container,
        isInitialized: false,
        isVisible: false,
        orderId: null,
        // Store orderId for payment completion
        clientToken: null // Store clientToken as well
      });
    }
    return this.containers.get(containerKey);
  }

  // Show container in target element
  showContainer(paymentMethodId, containerId, targetElement) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo && containerInfo.element) {
      // Hide all other containers first
      this.hideAllContainers();

      // Move container to target and show it
      if (targetElement) {
        targetElement.appendChild(containerInfo.element);
        containerInfo.element.style.cssText = `
                    position: relative;
                    top: auto;
                    left: auto;
                    width: 100%;
                    visibility: visible;
                    opacity: 1;
                    pointer-events: auto;
                    transition: all 0.3s ease;
                `;
        containerInfo.isVisible = true;
        this.activeContainers.add(containerKey);
      }
    }
  }

  // Hide container
  hideContainer(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo && containerInfo.element) {
      // Move back to body and hide
      document.body.appendChild(containerInfo.element);
      containerInfo.element.style.cssText = `
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 100%;
                visibility: hidden;
                opacity: 0;
                pointer-events: none;
                transition: all 0.3s ease;
            `;
      containerInfo.isVisible = false;
      this.activeContainers.delete(containerKey);
    }
  }

  // Hide all containers
  hideAllContainers() {
    this.containers.forEach((containerInfo, containerKey) => {
      if (containerInfo.isVisible) {
        this.hideContainer(containerKey.split('_')[0], containerKey.split('_')[1]);
      }
    });
  }

  // Clear order data for a specific container
  clearOrderData(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo) {
      containerInfo.orderId = null;
      containerInfo.clientToken = null;
      containerInfo.instructions = null; // Also clear instructions
      containerInfo.isInitialized = false; // Reset initialization state
    }
  }

  // Set initialization status
  setContainerInitialized(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo) {
      containerInfo.isInitialized = true;
    }
  }

  // Check if container is initialized
  isContainerInitialized(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    return containerInfo ? containerInfo.isInitialized : false;
  }

  // Set order and token data
  setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions = null) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    if (containerInfo) {
      containerInfo.orderId = orderId;
      containerInfo.clientToken = clientToken;
      containerInfo.instructions = instructions; // Add instructions storage (using for PayID/Bank Transfer)
    } else {
      console.warn(`[PrimerCheckoutManager] Container ${containerKey} not found for setting order data`);
    }
  }

  // Get order data
  getOrderData(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    const result = containerInfo ? {
      orderId: containerInfo.orderId,
      clientToken: containerInfo.clientToken,
      instructions: containerInfo.instructions // Include instructions in returned data
    } : {
      orderId: null,
      clientToken: null,
      instructions: null
    };
    return result;
  }

  // Get container element
  getContainerElement(paymentMethodId, containerId) {
    const containerKey = `${paymentMethodId}_${containerId}`;
    const containerInfo = this.containers.get(containerKey);
    return containerInfo ? containerInfo.element : null;
  }

  // Cleanup
  cleanup() {
    this.containers.forEach(containerInfo => {
      if (containerInfo.element && containerInfo.element.parentNode) {
        containerInfo.element.parentNode.removeChild(containerInfo.element);
      }
    });
    this.containers.clear();
    this.activeContainers.clear();
  }
}

// Global instance
const primerCheckoutManager = new PrimerCheckoutManager();

// Hook for persistent container management
const usePersistentPaymentDetailsContainer = (paymentMethodId, containerId) => {
  const targetRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  const isActiveRef = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);

  // Generate persistent container ID
  const persistentContainerId = `persistent-${paymentMethodId}-${containerId}`;
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    // Create or get container
    const containerInfo = primerCheckoutManager.getOrCreateContainer(paymentMethodId, containerId);

    // Update the container's ID to match what Primer expects
    if (containerInfo.element) {
      containerInfo.element.id = persistentContainerId;
    }

    // Show container when component mounts
    if (targetRef.current && !isActiveRef.current) {
      primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);
      isActiveRef.current = true;
    }

    // Cleanup on unmount
    return () => {
      if (isActiveRef.current) {
        primerCheckoutManager.hideContainer(paymentMethodId, containerId);
        isActiveRef.current = false;
      }
    };
  }, [paymentMethodId, containerId]);
  return {
    targetRef,
    containerElement: primerCheckoutManager.getContainerElement(paymentMethodId, containerId),
    containerIdActive: persistentContainerId,
    // Return the active container ID for Primer
    isContainerInitialized: primerCheckoutManager.isContainerInitialized(paymentMethodId, containerId),
    setContainerInitialized: () => primerCheckoutManager.setContainerInitialized(paymentMethodId, containerId),
    // Order data persistence methods
    setOrderData: (orderId, clientToken, instructions = null) => primerCheckoutManager.setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions),
    getOrderData: () => primerCheckoutManager.getOrderData(paymentMethodId, containerId),
    clearOrderData: () => {
      primerCheckoutManager.clearOrderData(paymentMethodId, containerId);
    },
    showContainer: () => {
      if (targetRef.current && !isActiveRef.current) {
        primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);
        isActiveRef.current = true;
      }
    },
    hideContainer: () => {
      if (isActiveRef.current) {
        primerCheckoutManager.hideContainer(paymentMethodId, containerId);
        isActiveRef.current = false;
      }
    }
  };
};

// Cleanup function for page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    primerCheckoutManager.cleanup();
  });
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (primerCheckoutManager);

/***/ }),

/***/ "@woocommerce/block-data":
/*!**************************************!*\
  !*** external ["wc","wcBlocksData"] ***!
  \**************************************/
/***/ ((module) => {

module.exports = wc.wcBlocksData;

/***/ }),

/***/ "@woocommerce/blocks-registry":
/*!******************************************!*\
  !*** external ["wc","wcBlocksRegistry"] ***!
  \******************************************/
/***/ ((module) => {

module.exports = wc.wcBlocksRegistry;

/***/ }),

/***/ "@woocommerce/settings":
/*!************************************!*\
  !*** external ["wc","wcSettings"] ***!
  \************************************/
/***/ ((module) => {

module.exports = wc.wcSettings;

/***/ }),

/***/ "@wordpress/data":
/*!******************************!*\
  !*** external ["wp","data"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["data"];

/***/ }),

/***/ "@wordpress/element":
/*!*********************************!*\
  !*** external ["wp","element"] ***!
  \*********************************/
/***/ ((module) => {

module.exports = window["wp"]["element"];

/***/ }),

/***/ "@wordpress/html-entities":
/*!**************************************!*\
  !*** external ["wp","htmlEntities"] ***!
  \**************************************/
/***/ ((module) => {

module.exports = window["wp"]["htmlEntities"];

/***/ }),

/***/ "@wordpress/i18n":
/*!******************************!*\
  !*** external ["wp","i18n"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["i18n"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!*****************************************************!*\
  !*** ./assets/js/src/blocks/monoova-payto-block.js ***!
  \*****************************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @woocommerce/blocks-registry */ "@woocommerce/blocks-registry");
/* harmony import */ var _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @woocommerce/settings */ "@woocommerce/settings");
/* harmony import */ var _woocommerce_settings__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _woocommerce_block_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @woocommerce/block-data */ "@woocommerce/block-data");
/* harmony import */ var _woocommerce_block_data__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_block_data__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/html-entities */ "@wordpress/html-entities");
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @wordpress/data */ "@wordpress/data");
/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _hooks_usePayToPaymentContainer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/usePayToPaymentContainer */ "./assets/js/src/hooks/usePayToPaymentContainer.js");
/**
 * Monoova PayTo Block for WooCommerce Blocks
 *
 * Uses ES6 imports with defensive programming to handle cases where WooCommerce dependencies
 * may not be available (edit mode, missing plugins, etc.)
 */









// Import custom hooks


// Add CSS animation for spinner
const spinnerStyles = `
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
`;

// Inject the styles into the document head
if (typeof document !== "undefined") {
  const styleSheet = document.createElement("style");
  styleSheet.type = "text/css";
  styleSheet.innerText = spinnerStyles;
  document.head.appendChild(styleSheet);
}

// If registerPaymentMethod is not available, we can't register the payment method
if (typeof _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__.registerPaymentMethod !== "function") {
  console.warn("Monoova PayTo Block: registerPaymentMethod not available. Available globals:", Object.keys(window.wc || {}));
} else {
  // Try to get settings
  let settings = {};
  if (typeof _woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__.getSetting === "function") {
    try {
      settings = (0,_woocommerce_settings__WEBPACK_IMPORTED_MODULE_1__.getSetting)("monoova_payto_data", {});
    } catch (error) {
      console.log("Monoova PayTo Block: getSetting failed:", error);
    }
  }

  // Fallback to global variable if getSetting didn't work
  if (!settings || Object.keys(settings).length === 0) {
    settings = window.monoova_payto_blocks_params || {};
    console.log("Monoova PayTo Block: Using fallback settings:", settings);
  }

  // Set defaults if no settings available
  if (!settings || Object.keys(settings).length === 0) {
    console.warn("Monoova PayTo Block: No settings found, using defaults");
    settings = {
      title: "PayTo",
      description: "Set up PayTo directly from your bank using BSB and Account Number or PayID.",
      supports: []
    };
  }
  const defaultLabel = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("PayTo", "monoova-payments-for-woocommerce");
  const label = (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_3__.decodeEntities)(settings.title) || defaultLabel;

  /**
   * PayTo Payment Form Component
   */
  const PayToForm = ({
    eventRegistration,
    emitResponse,
    billing
  }) => {
    // Get current order ID from WooCommerce checkout store
    const {
      orderId
    } = (0,_wordpress_data__WEBPACK_IMPORTED_MODULE_6__.useSelect)(select => {
      const store = select(_woocommerce_block_data__WEBPACK_IMPORTED_MODULE_2__.CHECKOUT_STORE_KEY);
      return {
        orderId: store.getOrderId()
      };
    });

    // Helper function to check if required guest information is available
    const hasRequiredGuestInfo = () => {
      const billingAddress = billing?.billingAddress;
      if (!billingAddress) return false;

      // Required fields for guest customers to generate client token
      return !!(billingAddress.email && billingAddress.first_name && billingAddress.last_name && billingAddress.address_1 && billingAddress.city && billingAddress.postcode && billingAddress.country && billingAddress.state && billingAddress.city);
    };

    // Use the PayTo payment container hook for persistent state management
    const {
      containerRef,
      isInitialized,
      setContainerInitialized,
      // State management from hook
      paymentMethod,
      setPaymentMethod,
      payidValue,
      setPayidValue,
      payidType,
      setPayidType,
      accountName,
      setAccountName,
      bsb,
      setBsb,
      accountNumber,
      setAccountNumber,
      maximumAmount,
      setMaximumAmount,
      errors,
      setErrors,
      paymentStatus,
      setPaymentStatus,
      paymentInstructions,
      setPaymentInstructions,
      isSubmitting,
      setIsSubmitting,
      pollInterval,
      setPollInterval,
      hasExpressCheckout,
      setHasExpressCheckout,
      errorDetails,
      setErrorDetails,
      mandateInfo,
      setMandateInfo,
      countdown,
      setCountdown,
      // Utilities
      resetStates,
      stopPolling,
      saveCurrentState
    } = (0,_hooks_usePayToPaymentContainer__WEBPACK_IMPORTED_MODULE_7__.usePayToPaymentContainer)({
      settings,
      billing,
      orderId,
      containerId: "payto-payment-container",
      paymentMethodId: "monoova_payto",
      hasRequiredInfo: hasRequiredGuestInfo()
    });

    // Get cart totals from WooCommerce
    const cartTotals = (0,_wordpress_data__WEBPACK_IMPORTED_MODULE_6__.useSelect)(select => {
      const store = select("wc/store/cart");
      if (store && store.getCartTotals) {
        return store.getCartTotals();
      }
      return null;
    }, []);
    (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_5__.useEffect)(() => {
      // Selector for the block checkout's "Place Order" button
      const placeOrderButton = document.querySelector(".wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button");
      if (placeOrderButton) {
        // Hide the button when Monoova PayTo is selected
        placeOrderButton.style.display = "none";
      }

      // Cleanup function: runs when component unmounts (e.g., user selects another payment method)
      return () => {
        if (placeOrderButton) {
          // Restore the button's default display style
          placeOrderButton.style.display = "";
        }
      };
    }, []);

    // Get order total amount
    const orderTotal = cartTotals?.total_price ? parseFloat(cartTotals.total_price) / 100 : 0;
    const currency = cartTotals?.currency_code || settings.currency || "AUD";
    const {
      onPaymentSetup
    } = eventRegistration;
    const {
      responseTypes,
      noticeContexts
    } = emitResponse;

    // Helper function to get status colors
    const getStatusColor = status => {
      const statusLower = status?.toLowerCase() || "";
      switch (statusLower) {
        case "completed":
        case "success":
        case "authorized":
          return {
            background: "#dcfce7",
            text: "#166534"
          };
        case "processing":
        case "pending":
        case "created":
          return {
            background: "#fef3c7",
            text: "#92400e"
          };
        case "failed":
        case "cancelled":
        case "rejected":
          return {
            background: "#fee2e2",
            text: "#991b1b"
          };
        default:
          return {
            background: "#f3f4f6",
            text: "#374151"
          };
      }
    };

    // Check for express checkout availability on mount
    (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_5__.useEffect)(() => {
      console.log("PayTo: Component mounted, checking express checkout availability...");
      checkExpressCheckoutAvailability();
    }, []);

    // Cleanup polling on unmount
    (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_5__.useEffect)(() => {
      return () => {
        if (pollInterval) {
          clearInterval(pollInterval);
        }
      };
    }, [pollInterval]);

    // Countdown timer effect
    (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_5__.useEffect)(() => {
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }, []);

    // Check if express checkout is available
    const checkExpressCheckoutAvailability = async () => {
      if (!settings.is_user_logged_in) {
        console.log("PayTo: User not logged in, skipping express checkout check");
        return;
      }
      try {
        const response = await fetch(settings.ajax_url || "/wp-admin/admin-ajax.php", {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded"
          },
          body: new URLSearchParams({
            action: "monoova_check_express_checkout",
            nonce: settings.nonce
          })
        });
        const result = await response.json();
        if (result.success && result.data.available) {
          setHasExpressCheckout(true);
          // Store mandate details for display
          if (result.data.mandate_info) {
            setMandateInfo(result.data.mandate_info);
          }
        } else {
          console.log("Express checkout not available or failed:", result);
        }
      } catch (error) {
        console.error("Error checking express checkout availability:", error);
      }
    };

    // Handle checkout with existing mandate
    const handleCheckoutWithExistingMandate = async () => {
      setIsSubmitting(true);
      setPaymentStatus("processing");
      setErrors({}); // Clear previous errors
      setErrorDetails(null); // Clear previous error details

      try {
        const response = await fetch(settings.ajax_url || "/wp-admin/admin-ajax.php", {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded"
          },
          body: new URLSearchParams({
            action: "monoova_process_payment_with_existing_mandate",
            nonce: settings.nonce,
            order_id: orderId,
            payment_agreement_uid: mandateInfo?.agreement_id
          })
        });
        const result = await response.json();
        if (result.success) {
          setPaymentInstructions({
            order_id: result.data.order_id,
            currency: currency,
            amount: orderTotal.toFixed(2),
            status: "Processing",
            agreement_reference: result.data.order_id,
            message: "Payment initiated with existing mandate"
          });
          setPaymentStatus("instructions");
          startPaymentStatusPolling(result.data.order_id);
        } else {
          const errorMessage = result.data?.message || "Express checkout failed";
          const errorCode = result.data?.error_code || "UNKNOWN_ERROR";
          const errorType = result.data?.error_type || "EXPRESS_CHECKOUT_ERROR";

          // Check if this is an agreement limit error
          if (errorCode === "payto_agreement_limit_exceeded" || errorType === "AGREEMENT_LIMIT_ERROR") {
            setPaymentStatus("agreement_limit_exceeded");
            setErrors({
              general: errorMessage
            });
            setErrorDetails({
              message: errorMessage,
              code: "AGREEMENT_LIMIT_EXCEEDED",
              type: "AGREEMENT_LIMIT_ERROR",
              context: "express_checkout",
              requires_new_agreement: true,
              limit_errors: result.data?.errors || [],
              agreement_uid: result.data?.agreement_uid
            });
          } else {
            setPaymentStatus("failed");
            setErrors({
              general: errorMessage
            });
            setErrorDetails({
              message: errorMessage,
              code: errorCode,
              type: errorType,
              context: "express_checkout",
              details: result.data?.error_details || null
            });
          }
        }
      } catch (error) {
        setPaymentStatus("failed");
        setErrors({
          general: "Network error occurred during express checkout"
        });
        setErrorDetails({
          message: "Network error occurred during express checkout",
          code: "NETWORK_ERROR",
          type: "CONNECTION_ERROR",
          context: "express_checkout",
          details: error.message
        });
      } finally {
        setIsSubmitting(false);
      }
    };

    // Note: We're using a custom "Accept and Continue" button instead of the default place order button

    // PayID validation and auto-detection
    const validatePayID = value => {
      if (!value.trim()) {
        setPayidType("");
        setErrors(prev => ({
          ...prev,
          payidValue: ""
        }));
        return;
      }

      // Email regex pattern
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      // Phone regex pattern (Australian format)
      const phonePattern = /^(\+61|0)[0-9]{9}$/;
      if (emailPattern.test(value)) {
        setPayidType("Email");
        setErrors(prev => ({
          ...prev,
          payidValue: ""
        }));
      } else if (phonePattern.test(value)) {
        setPayidType("PhoneNumber");
        setErrors(prev => ({
          ...prev,
          payidValue: ""
        }));
      } else {
        setPayidType("");
        setErrors(prev => ({
          ...prev,
          payidValue: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Please enter a valid email address or phone number.", "monoova-payments-for-woocommerce")
        }));
      }
    };

    // Validation function
    const validateForm = () => {
      const newErrors = {};
      if (paymentMethod === "payid") {
        if (!payidValue.trim()) {
          newErrors.payidValue = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("PayID is required.", "monoova-payments-for-woocommerce");
        } else if (!payidType) {
          newErrors.payidValue = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Please enter a valid email address or phone number.", "monoova-payments-for-woocommerce");
        }
      } else {
        if (!accountName.trim()) {
          newErrors.accountName = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Account name is required.", "monoova-payments-for-woocommerce");
        }
        if (!bsb.trim()) {
          newErrors.bsb = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("BSB is required.", "monoova-payments-for-woocommerce");
        } else if (!/^\d{3}-?\d{3}$/.test(bsb.trim())) {
          newErrors.bsb = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Please enter a valid BSB (6 digits).", "monoova-payments-for-woocommerce");
        }
        if (!accountNumber.trim()) {
          newErrors.accountNumber = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Account number is required.", "monoova-payments-for-woocommerce");
        }
      }

      // Validate maximum amount
      if (!maximumAmount || maximumAmount <= 0) {
        newErrors.maximumAmount = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Maximum amount must be greater than 0.", "monoova-payments-for-woocommerce");
      }
      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };

    // Register payment setup handler
    React.useEffect(() => {
      const unsubscribe = onPaymentSetup(() => {
        if (!validateForm()) {
          return {
            type: responseTypes.ERROR,
            message: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Please correct the errors in the PayTo form.", "monoova-payments-for-woocommerce"),
            messageContext: noticeContexts.PAYMENTS
          };
        }

        // Return payment data
        return {
          type: responseTypes.SUCCESS,
          meta: {
            paymentMethodData: {
              payto_payment_method: paymentMethod,
              payto_payid_type: paymentMethod === "payid" ? payidType : "",
              payto_payid_value: paymentMethod === "payid" ? payidValue : "",
              payto_account_name: paymentMethod === "bsb_account" ? accountName : "",
              payto_bsb: paymentMethod === "bsb_account" ? bsb : "",
              payto_account_number: paymentMethod === "bsb_account" ? accountNumber : "",
              payto_maximum_amount: maximumAmount.toString()
            }
          }
        };
      });
      return unsubscribe;
    }, [onPaymentSetup, paymentMethod, payidType, payidValue, accountName, bsb, accountNumber, maximumAmount, validateForm]);

    // Custom submit handler for PayTo
    const handlePayToSubmit = async event => {
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }
      if (!validateForm()) {
        return;
      }
      setIsSubmitting(true);
      setPaymentStatus("processing");
      setErrors({}); // Clear previous errors
      setErrorDetails(null); // Clear previous error details

      // Clear mandate info when creating new agreement
      setMandateInfo(null);
      setHasExpressCheckout(false);
      try {
        // Get billing data from props (provided by WooCommerce Blocks)
        let billingInfo = billing?.billingAddress || {};
        const response = await fetch(settings.ajax_url || "/wp-admin/admin-ajax.php", {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded"
          },
          body: new URLSearchParams({
            action: "monoova_create_payto_agreement",
            nonce: settings.nonce,
            payto_payment_method: paymentMethod,
            payto_payid_type: paymentMethod === "payid" ? payidType : "",
            payto_payid_value: paymentMethod === "payid" ? payidValue : "",
            payto_account_name: paymentMethod === "bsb_account" ? accountName : "",
            payto_bsb: paymentMethod === "bsb_account" ? bsb : "",
            payto_account_number: paymentMethod === "bsb_account" ? accountNumber : "",
            payto_maximum_amount: maximumAmount.toString(),
            billing_first_name: billingInfo.first_name || "",
            billing_last_name: billingInfo.last_name || "",
            billing_email: billingInfo.email || "",
            order_id: orderId,
            isCheckoutPage: true
          })
        });
        const result = await response.json();
        if (result.success) {
          setPaymentInstructions({
            order_id: result.data.order_id,
            order_key: result.data.order_key,
            order_received_url: result.data.order_received_url,
            currency: currency,
            amount: orderTotal.toFixed(2),
            status: "Processing",
            agreement_reference: result.data.order_id,
            message: "Payment agreement created successfully"
          });
          setPaymentStatus("instructions");
          startPaymentStatusPolling(result.data.order_id);
        } else {
          const errorMessage = result.data?.message || "Failed to create payment agreement";
          const errorCode = result.data?.error_code || "UNKNOWN_ERROR";
          const errorType = result.data?.error_type || "AGREEMENT_CREATION_ERROR";

          // Reset payment status to allow user to try again
          setPaymentStatus("failed");
          setErrors({
            general: errorMessage
          });
          setErrorDetails({
            message: errorMessage,
            code: errorCode,
            type: errorType,
            context: "agreement_creation",
            details: result.data?.error_details || null,
            validation_errors: result.data?.validation_errors || null
          });
        }
      } catch (error) {
        setPaymentStatus("failed");
        setErrors({
          general: "Network error occurred while creating payment agreement"
        });
        setErrorDetails({
          message: "Network error occurred while creating payment agreement",
          code: "NETWORK_ERROR",
          type: "CONNECTION_ERROR",
          context: "agreement_creation",
          details: error.message
        });
      } finally {
        setIsSubmitting(false);
      }
    };

    // Reset payment agreement when limits exceeded
    const resetPaymentAgreement = async () => {
      setIsSubmitting(true);
      setErrors({});
      setErrorDetails(null);
      try {
        const billingInfo = billing?.billingAddress || {};
        const response = await fetch(settings.ajax_url || "/wp-admin/admin-ajax.php", {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded"
          },
          body: new URLSearchParams({
            action: "monoova_payto_reset_agreement",
            nonce: settings.nonce,
            order_id: paymentInstructions?.order_id || orderId,
            customer_email: billingInfo.email || ""
          })
        });
        const result = await response.json();
        if (result.success) {
          // Reset all payment-related state
          setPaymentInstructions(null);
          setPaymentStatus("form");
          setErrors({});
          setErrorDetails(null);

          // Reset form fields to allow new agreement creation
          setPaymentMethod("payid");
          setPayidType("email");
          setPayidValue("");
          setAccountName("");
          setBsb("");
          setAccountNumber("");
          setMaximumAmount(orderTotal);
        } else {
          setErrors({
            general: result.data?.message || "Failed to reset payment agreement"
          });
        }
      } catch (error) {
        setErrors({
          general: "Network error occurred while resetting payment agreement"
        });
      } finally {
        setIsSubmitting(false);
      }
    };

    // Payment status polling with auto-redirect (5-second interval)
    const startPaymentStatusPolling = orderId => {
      const interval = setInterval(async () => {
        try {
          const response = await fetch(settings.ajax_url || "/wp-admin/admin-ajax.php", {
            method: "POST",
            headers: {
              "Content-Type": "application/x-www-form-urlencoded"
            },
            body: new URLSearchParams({
              action: "get_payto_agreement_payment_initiation_status",
              nonce: settings.nonce,
              order_id: orderId
            })
          });
          const result = await response.json();
          if (result.success) {
            const agreementStatus = result.data.agreement_status || result.data.mandate_status;
            const paymentStatus = result.data.payment_initiation_status;
            const orderStatus = result.data.order_status;
            const errorDetails = result.data.error_details;
            console.log("PayTo polling status:", {
              agreementStatus,
              paymentStatus,
              orderStatus,
              errorDetails
            });

            // Check for agreement limit errors first
            if (errorDetails && errorDetails.requires_new_agreement) {
              clearInterval(interval);
              setPollInterval(null);
              setPaymentStatus("agreement_limit_exceeded");
              const limitErrors = errorDetails.errors || [];
              const errorMessages = limitErrors.map(err => err.message).join(". ");
              setErrors({
                general: errorMessages
              });
              setErrorDetails({
                message: errorMessages,
                code: "AGREEMENT_LIMIT_EXCEEDED",
                type: "AGREEMENT_LIMIT_ERROR",
                context: "payment_initiation",
                agreement_uid: errorDetails.agreement_uid,
                requires_new_agreement: true,
                limit_errors: limitErrors
              });
              return;
            }

            // Update payment instructions with current status
            if (paymentInstructions) {
              setPaymentInstructions(prev => ({
                ...prev,
                status: agreementStatus || paymentStatus || "Processing",
                agreement_status: agreementStatus,
                payment_initiation_status: paymentStatus,
                order_status: orderStatus,
                order_key: result.data.order_key || prev.order_key,
                order_received_url: result.data.order_received_url || prev.order_received_url
              }));
            }

            // Check for completed payment
            if (paymentStatus === "completed" || paymentStatus === "success" || orderStatus === "completed" || orderStatus === "processing") {
              clearInterval(interval);
              setPollInterval(null);
              setPaymentStatus("success");

              // Auto-redirect to order received page (like PayID implementation)
              setTimeout(() => {
                const orderReceivedUrl = result.data.order_received_url || paymentInstructions?.order_received_url;
                if (orderReceivedUrl) {
                  window.location.href = orderReceivedUrl;
                }
              }, 2000); // 2 second delay to show success message
            } else if (paymentStatus === "failed" || paymentStatus === "cancelled" || agreementStatus === "failed" || agreementStatus === "cancelled" || agreementStatus === "rejected") {
              clearInterval(interval);
              setPollInterval(null);

              // Check if this is an agreement limit error
              if (errorDetails?.code === "payto_agreement_limit_exceeded" || errorDetails?.type === "AGREEMENT_LIMIT_ERROR" || result.data.error_code === "payto_agreement_limit_exceeded") {
                setPaymentStatus("agreement_limit_exceeded");
                setErrors({
                  general: errorDetails?.message || result.data.message || "Payment agreement limits have been exceeded"
                });
                setErrorDetails({
                  message: errorDetails?.message || result.data.message || "Payment agreement limits have been exceeded",
                  code: "AGREEMENT_LIMIT_EXCEEDED",
                  type: "AGREEMENT_LIMIT_ERROR",
                  context: "payment_polling",
                  requires_new_agreement: true,
                  limit_errors: result.data?.errors || errorDetails?.limit_errors || [],
                  agreement_status: agreementStatus,
                  payment_status: paymentStatus,
                  order_status: orderStatus
                });
              } else {
                setPaymentStatus("failed");

                // Set detailed error information based on status
                let errorMessage = "Payment was cancelled or failed";
                let errorCode = "UNKNOWN_ERROR";
                let errorType = "PAYMENT_FAILED";
                if (agreementStatus === "rejected") {
                  errorMessage = "PayTo agreement was rejected by your bank";
                  errorCode = "AGREEMENT_REJECTED";
                  errorType = "AGREEMENT_ERROR";
                } else if (agreementStatus === "cancelled") {
                  errorMessage = "PayTo agreement was cancelled";
                  errorCode = "AGREEMENT_CANCELLED";
                  errorType = "AGREEMENT_ERROR";
                } else if (agreementStatus === "failed") {
                  errorMessage = "PayTo agreement creation failed";
                  errorCode = "AGREEMENT_FAILED";
                  errorType = "AGREEMENT_ERROR";
                } else if (paymentStatus === "failed") {
                  errorMessage = "Payment initiation failed";
                  errorCode = "PAYMENT_FAILED";
                  errorType = "PAYMENT_ERROR";
                } else if (paymentStatus === "cancelled") {
                  errorMessage = "Payment was cancelled";
                  errorCode = "PAYMENT_CANCELLED";
                  errorType = "PAYMENT_ERROR";
                }
                setErrors({
                  general: errorMessage
                });
                setErrorDetails({
                  message: errorMessage,
                  code: errorCode,
                  type: errorType,
                  context: "payment_polling",
                  agreement_status: agreementStatus,
                  payment_status: paymentStatus,
                  order_status: orderStatus
                });
              }
            }
            // For "pending", "created", "authorized" or other statuses, continue polling
          }
        } catch (error) {
          console.error("Error checking payment status:", error);
        }
      }, 5000); // Poll every 5 seconds

      setPollInterval(interval);
    };

    // Render payment instructions
    const renderPaymentInstructions = () => {
      if (!paymentInstructions) return null;
      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: "center",
          margin: "24px 0"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: "24px",
          fontWeight: "700",
          color: "#000",
          marginBottom: "8px"
        }
      }, "Pay", " ", /*#__PURE__*/React.createElement("span", {
        style: {
          fontWeight: "700",
          color: "#2CB5C5"
        }
      }, "$", paymentInstructions.amount)), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: "14px",
          color: "#6b7280",
          margin: "0"
        }
      }, "You're about to approve this payment from your banking app.")), /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: "24px"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          gap: "24px",
          alignItems: "center",
          marginBottom: "16px"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          fontWeight: "500",
          fontSize: "18px",
          lineHeight: "1.21em",
          color: "#000000",
          marginBottom: "16px"
        }
      }, "Pay with"), /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          gap: "16px",
          alignItems: "center"
        }
      }, /*#__PURE__*/React.createElement("label", {
        style: {
          display: "flex",
          alignItems: "center",
          cursor: "pointer",
          gap: "8px"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          position: "relative"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          width: "24px",
          height: "24px",
          border: `1.5px solid ${paymentMethod === "payid" ? "#2CB5C5" : "#ABABAB"}`,
          borderRadius: "50%",
          position: "relative"
        }
      }, paymentMethod === "payid" && /*#__PURE__*/React.createElement("div", {
        style: {
          width: "14px",
          height: "14px",
          backgroundColor: "#2CB5C5",
          borderRadius: "50%",
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)"
        }
      }))), /*#__PURE__*/React.createElement("span", {
        style: {
          fontWeight: "400",
          fontSize: "16px",
          lineHeight: "1.5em",
          color: "#000000"
        }
      }, "PayID")), /*#__PURE__*/React.createElement("label", {
        style: {
          display: "flex",
          alignItems: "center",
          cursor: "pointer",
          gap: "8px"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          position: "relative"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          width: "24px",
          height: "24px",
          border: `1.5px solid ${paymentMethod === "bsb_account" ? "#2CB5C5" : "#ABABAB"}`,
          borderRadius: "50%",
          position: "relative"
        }
      }, paymentMethod === "bsb_account" && /*#__PURE__*/React.createElement("div", {
        style: {
          width: "14px",
          height: "14px",
          backgroundColor: "#2CB5C5",
          borderRadius: "50%",
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)"
        }
      }))), /*#__PURE__*/React.createElement("span", {
        style: {
          fontWeight: "400",
          fontSize: "16px",
          lineHeight: "1.5em",
          color: "#000000"
        }
      }, "BSB and account number"))))), /*#__PURE__*/React.createElement("div", {
        style: {
          border: "1px solid #E8E8E8",
          borderRadius: "16px",
          padding: "24px 24px 32px",
          gap: "40px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center"
        }
      }, paymentMethod === "payid" && /*#__PURE__*/React.createElement("div", {
        style: {
          width: "100%",
          display: "flex",
          flexDirection: "column"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "center"
        }
      }, /*#__PURE__*/React.createElement("label", {
        style: {
          fontWeight: "600",
          fontSize: "16px",
          lineHeight: "1.5em",
          color: "#000000"
        }
      }, "Enter your PayID Email or Mobile number")), /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "stretch",
          gap: "10px",
          padding: "0px 12px",
          backgroundColor: "#FAFAFA",
          border: "1px solid #E8E8E8",
          borderRadius: "8px",
          minHeight: "48px"
        }
      }, /*#__PURE__*/React.createElement("input", {
        type: "text",
        value: payidValue || "",
        readOnly: true,
        placeholder: "Email or mobile number",
        style: {
          flex: 1,
          border: "none",
          backgroundColor: "transparent",
          outline: "none",
          fontWeight: "500",
          fontSize: "16px",
          lineHeight: "1.21em",
          color: "#9D9C9C",
          padding: "12px 0"
        }
      }))), paymentMethod === "bsb_account" && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("div", {
        style: {
          width: "100%",
          display: "flex",
          flexDirection: "column"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "center"
        }
      }, /*#__PURE__*/React.createElement("label", {
        style: {
          fontWeight: "600",
          fontSize: "16px",
          lineHeight: "1.5em",
          color: "#000000"
        }
      }, "Name associated with bank account")), /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "stretch",
          gap: "10px",
          padding: "0px 12px",
          backgroundColor: "#FAFAFA",
          border: "1px solid #E8E8E8",
          borderRadius: "8px",
          minHeight: "48px"
        }
      }, /*#__PURE__*/React.createElement("input", {
        type: "text",
        value: accountName || "",
        readOnly: true,
        placeholder: "Enter your name",
        style: {
          flex: 1,
          border: "none",
          backgroundColor: "transparent",
          outline: "none",
          fontWeight: "500",
          fontSize: "16px",
          lineHeight: "1.21em",
          color: "#9D9C9C",
          padding: "12px 0"
        }
      }))), /*#__PURE__*/React.createElement("div", {
        style: {
          width: "100%",
          display: "flex",
          flexDirection: "column"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "center"
        }
      }, /*#__PURE__*/React.createElement("label", {
        style: {
          fontWeight: "600",
          fontSize: "16px",
          lineHeight: "1.5em",
          color: "#000000"
        }
      }, "BSB")), /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "stretch",
          gap: "10px",
          padding: "0px 12px",
          backgroundColor: "#FAFAFA",
          border: "1px solid #E8E8E8",
          borderRadius: "8px",
          minHeight: "48px"
        }
      }, /*#__PURE__*/React.createElement("input", {
        type: "text",
        value: bsb || "",
        readOnly: true,
        placeholder: "123-456",
        style: {
          flex: 1,
          border: "none",
          backgroundColor: "transparent",
          outline: "none",
          fontWeight: "500",
          fontSize: "16px",
          lineHeight: "1.21em",
          color: "#9D9C9C",
          padding: "12px 0"
        }
      }))), /*#__PURE__*/React.createElement("div", {
        style: {
          width: "100%",
          display: "flex",
          flexDirection: "column"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "center"
        }
      }, /*#__PURE__*/React.createElement("label", {
        style: {
          fontWeight: "600",
          fontSize: "16px",
          lineHeight: "1.5em",
          color: "#000000"
        }
      }, "Account Number")), /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "stretch",
          gap: "10px",
          padding: "0px 12px",
          backgroundColor: "#FAFAFA",
          border: "1px solid #E8E8E8",
          borderRadius: "8px",
          minHeight: "48px"
        }
      }, /*#__PURE__*/React.createElement("input", {
        type: "text",
        value: accountNumber || "",
        readOnly: true,
        placeholder: "Enter your account number",
        style: {
          flex: 1,
          border: "none",
          backgroundColor: "transparent",
          outline: "none",
          fontWeight: "500",
          fontSize: "16px",
          lineHeight: "1.21em",
          color: "#9D9C9C",
          padding: "12px 0"
        }
      })))), /*#__PURE__*/React.createElement("div", {
        style: {
          width: "100%",
          display: "flex",
          flexDirection: "column",
          gap: "12px"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          flexDirection: "column"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "center"
        }
      }, /*#__PURE__*/React.createElement("label", {
        style: {
          fontWeight: "600",
          fontSize: "16px",
          lineHeight: "1.5em",
          color: "#000000"
        }
      }, "Maximum payment agreement amount")), /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "stretch",
          gap: "10px",
          padding: "0px 12px",
          backgroundColor: "#FAFAFA",
          border: "1px solid #E8E8E8",
          borderRadius: "8px",
          minHeight: "48px"
        }
      }, /*#__PURE__*/React.createElement("input", {
        type: "text",
        value: `$${maximumAmount || 1000}`,
        readOnly: true,
        style: {
          flex: 1,
          border: "none",
          backgroundColor: "transparent",
          outline: "none",
          fontWeight: "500",
          fontSize: "16px",
          lineHeight: "1.21em",
          color: "#000000",
          padding: "12px 0"
        }
      }))), /*#__PURE__*/React.createElement("div", {
        style: {
          fontWeight: "500",
          fontSize: "14px",
          lineHeight: "1.29em",
          color: "#909090"
        }
      }, "This is the maximum amount that can be charged under this PayTo agreement. You can modify this amount if needed.")), /*#__PURE__*/React.createElement("div", {
        style: {
          width: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          gap: "10px",
          backgroundColor: "#2CB5C5",
          borderRadius: "8px",
          minHeight: "48px",
          opacity: 0.6,
          cursor: "not-allowed"
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontWeight: "700",
          fontSize: "16px",
          lineHeight: "1.21em",
          color: "#000000"
        }
      }, "Accept and Continue")), /*#__PURE__*/React.createElement("div", {
        style: {
          width: "100%"
        }
      }, /*#__PURE__*/React.createElement("h4", {
        style: {
          fontSize: "18px",
          fontWeight: "600",
          color: "#000000",
          marginBottom: "16px"
        }
      }, "Authorise recurring payments"), (() => {
        const currentStatus = paymentInstructions?.status?.toLowerCase() || "pending";
        const agreementStatus = paymentInstructions?.agreement_status?.toLowerCase() || "pending";
        const paymentStatus = paymentInstructions?.payment_initiation_status?.toLowerCase() || "pending";

        // 3. Done State - Payment completed/successful (final state)
        if (paymentStatus === "completed" || paymentStatus === "success" || paymentStatus === "processed") {
          return /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
            style: {
              marginBottom: "16px"
            }
          }, /*#__PURE__*/React.createElement("div", {
            style: {
              display: "flex",
              alignItems: "center",
              gap: "12px",
              borderRadius: "8px",
              marginBottom: "8px"
            }
          }, /*#__PURE__*/React.createElement("div", {
            style: {
              width: "20px",
              height: "20px",
              borderRadius: "50%",
              backgroundColor: "#2CB5C5",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              flexShrink: 0
            }
          }, /*#__PURE__*/React.createElement("svg", {
            width: "12",
            height: "9",
            viewBox: "0 0 12 9",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg"
          }, /*#__PURE__*/React.createElement("path", {
            d: "M1 4.5L4.5 8L11 1.5",
            stroke: "#ffffff",
            strokeWidth: "2",
            strokeLinecap: "round",
            strokeLinejoin: "round"
          }))), /*#__PURE__*/React.createElement("span", {
            style: {
              fontSize: "16px",
              color: "#000000",
              fontWeight: "500"
            }
          }, "Payment received"))), /*#__PURE__*/React.createElement("div", {
            style: {
              display: "flex",
              alignItems: "flex-start",
              gap: "12px",
              padding: "14px 10px",
              backgroundColor: "#E6F7FF",
              borderRadius: "8px",
              marginBottom: "16px"
            }
          }, /*#__PURE__*/React.createElement("div", {
            style: {
              width: "20px",
              height: "20px",
              borderRadius: "50%",
              backgroundColor: "#1890FF",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              flexShrink: 0,
              marginTop: "1px"
            }
          }, /*#__PURE__*/React.createElement("span", {
            style: {
              color: "#ffffff",
              fontSize: "12px",
              fontWeight: "bold"
            }
          }, "i")), /*#__PURE__*/React.createElement("p", {
            style: {
              margin: 0,
              fontSize: "14px",
              color: "#1890FF",
              lineHeight: "1.5",
              fontWeight: "500"
            }
          }, "We will redirect you to Order Received page in", " ", /*#__PURE__*/React.createElement("strong", null, "5 sec"))));
        }

        // 2. Initiation State - Agreement authorized, payment being initiated
        if ((agreementStatus === "authorized" || agreementStatus === "approved") && (paymentStatus === "initiated" || paymentStatus === "processing")) {
          return /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
            style: {
              marginBottom: "16px"
            }
          }, /*#__PURE__*/React.createElement("div", {
            style: {
              display: "flex",
              alignItems: "center",
              gap: "12px",
              borderRadius: "8px",
              marginBottom: "8px"
            }
          }, /*#__PURE__*/React.createElement("div", {
            style: {
              width: "20px",
              height: "20px",
              border: "2px solid #ffffff",
              borderTop: "2px solid #2CB5C5",
              borderRadius: "50%",
              animation: "spin 1s linear infinite",
              flexShrink: 0
            }
          }), /*#__PURE__*/React.createElement("span", {
            style: {
              fontSize: "16px",
              color: "#000000",
              fontWeight: "500"
            }
          }, "Initiate payment"))));
        }

        // 4. Failed State - Payment failed/cancelled/rejected
        if (paymentStatus === "failed" || paymentStatus === "cancelled" || paymentStatus === "rejected" || agreementStatus === "failed" || agreementStatus === "cancelled" || agreementStatus === "rejected") {
          return /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
            style: {
              marginBottom: "16px",
              textAlign: "center"
            }
          }, /*#__PURE__*/React.createElement("div", {
            style: {
              width: "22px",
              height: "22px",
              borderRadius: "50%",
              backgroundColor: "#FF0000",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              margin: "0 auto 16px"
            }
          }, /*#__PURE__*/React.createElement("span", {
            style: {
              color: "#ffffff",
              fontSize: "14px",
              fontWeight: "bold"
            }
          }, "i")), /*#__PURE__*/React.createElement("h3", {
            style: {
              fontSize: "24px",
              fontWeight: "400",
              color: "#000000",
              margin: "0 0 8px 0"
            }
          }, "Payment failed"), /*#__PURE__*/React.createElement("p", {
            style: {
              fontSize: "16px",
              color: "#000000",
              margin: "0 0 24px 0",
              lineHeight: "1.5"
            }
          }, errors.general || errorDetails?.message || "Something went wrong with the payment.", /*#__PURE__*/React.createElement("br", null), "Please try again."), errorDetails && /*#__PURE__*/React.createElement("div", {
            style: {
              marginBottom: "24px",
              padding: "16px",
              backgroundColor: "#FFF2F2",
              border: "1px solid #FFCDD2",
              borderRadius: "8px",
              textAlign: "left",
              fontSize: "14px"
            }
          }, /*#__PURE__*/React.createElement("h4", {
            style: {
              margin: "0 0 12px 0",
              color: "#D32F2F",
              fontSize: "14px",
              fontWeight: "600"
            }
          }, "Error Details"), errorDetails.code && /*#__PURE__*/React.createElement("div", {
            style: {
              marginBottom: "8px"
            }
          }, /*#__PURE__*/React.createElement("strong", {
            style: {
              color: "#D32F2F"
            }
          }, "Error Code:"), " ", /*#__PURE__*/React.createElement("span", {
            style: {
              color: "#666"
            }
          }, errorDetails.code)), errorDetails.type && /*#__PURE__*/React.createElement("div", {
            style: {
              marginBottom: "8px"
            }
          }, /*#__PURE__*/React.createElement("strong", {
            style: {
              color: "#D32F2F"
            }
          }, "Error Type:"), " ", /*#__PURE__*/React.createElement("span", {
            style: {
              color: "#666"
            }
          }, errorDetails.type)), errorDetails.context && /*#__PURE__*/React.createElement("div", {
            style: {
              marginBottom: "8px"
            }
          }, /*#__PURE__*/React.createElement("strong", {
            style: {
              color: "#D32F2F"
            }
          }, "Context:"), " ", /*#__PURE__*/React.createElement("span", {
            style: {
              color: "#666"
            }
          }, errorDetails.context)), errorDetails.validation_errors && /*#__PURE__*/React.createElement("div", {
            style: {
              marginTop: "12px",
              fontSize: "13px"
            }
          }, /*#__PURE__*/React.createElement("strong", {
            style: {
              color: "#D32F2F"
            }
          }, "Validation Errors:"), /*#__PURE__*/React.createElement("div", {
            style: {
              marginTop: "4px",
              color: "#666"
            }
          }, typeof errorDetails.validation_errors === "object" ? Object.entries(errorDetails.validation_errors).map(([field, error], index) => /*#__PURE__*/React.createElement("div", {
            key: index,
            style: {
              marginBottom: "4px"
            }
          }, /*#__PURE__*/React.createElement("strong", null, field, ":"), " ", error)) : errorDetails.validation_errors))), /*#__PURE__*/React.createElement("button", {
            onClick: () => {
              setPaymentStatus("form");
              setErrors({});
              setErrorDetails(null);
              setPaymentInstructions(null);
            },
            style: {
              width: "100%",
              padding: "12px 20px",
              backgroundColor: "#2CB5C5",
              color: "#000000",
              border: "none",
              borderRadius: "8px",
              cursor: "pointer",
              fontSize: "16px",
              fontWeight: "700",
              lineHeight: "1.21em"
            }
          }, "Pay Again")));
        }

        // 1. Waiting State - Default state waiting for agreement approval
        return /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
          style: {
            marginBottom: "16px"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            display: "flex",
            alignItems: "flex-start",
            gap: "12px",
            padding: "14px 10px",
            backgroundColor: "#EDFDFF",
            borderRadius: "8px",
            marginBottom: "8px"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            width: "20px",
            height: "20px",
            borderRadius: "50%",
            backgroundColor: "#007A89",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flexShrink: 0,
            marginTop: "1px"
          }
        }, /*#__PURE__*/React.createElement("span", {
          style: {
            color: "#ffffff",
            fontSize: "12px",
            fontWeight: "bold"
          }
        }, "!")), /*#__PURE__*/React.createElement("p", {
          style: {
            margin: 0,
            fontSize: "14px",
            color: "#007A89",
            lineHeight: "1.5",
            fontWeight: "500"
          }
        }, "Approve your recurring payment in your online banking or banking app!")), /*#__PURE__*/React.createElement("div", {
          style: {
            display: "flex",
            alignItems: "center",
            gap: "12px",
            borderRadius: "8px"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            width: "20px",
            height: "20px",
            border: "2px solid #ffffff",
            borderTop: "2px solid #007A89",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
            flexShrink: 0
          }
        }), /*#__PURE__*/React.createElement("span", {
          style: {
            fontSize: "16px",
            color: "#000000",
            fontWeight: "500"
          }
        }, "Awaiting your approval"))));
      })())));
    };

    // Show different views based on payment status
    if (paymentStatus === "instructions") {
      return renderPaymentInstructions();
    }

    // Show agreement limit exceeded state with Pay Again option
    if (paymentStatus === "agreement_limit_exceeded") {
      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: "center",
          margin: "24px 0"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: "24px",
          fontWeight: "700",
          color: "#000",
          marginBottom: "8px"
        }
      }, "Payment Agreement Limit Exceeded"), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: "14px",
          color: "#6b7280",
          margin: "0"
        }
      }, "Your current payment agreement has reached its limits.")), /*#__PURE__*/React.createElement("div", {
        style: {
          backgroundColor: "#FFF2F2",
          border: "1px solid #FFCDD2",
          borderRadius: "8px",
          padding: "16px",
          marginBottom: "24px"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          display: "flex",
          alignItems: "center",
          marginBottom: "12px"
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          fontSize: "18px",
          marginRight: "8px"
        }
      }, "\u26A0\uFE0F"), /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: "16px",
          fontWeight: "600",
          color: "#D32F2F",
          margin: "0"
        }
      }, "Agreement Limits Exceeded")), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: "16px",
          color: "#000000",
          margin: "0 0 16px 0",
          lineHeight: "1.5"
        }
      }, errors.general || errorDetails?.message || "Payment agreement limits have been exceeded."), errorDetails?.limit_errors && errorDetails.limit_errors.length > 0 && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: "16px"
        }
      }, /*#__PURE__*/React.createElement("strong", {
        style: {
          color: "#D32F2F",
          fontSize: "14px"
        }
      }, "Specific Issues:"), /*#__PURE__*/React.createElement("ul", {
        style: {
          margin: "8px 0 0 20px",
          color: "#666",
          fontSize: "14px"
        }
      }, errorDetails.limit_errors.map((error, index) => /*#__PURE__*/React.createElement("li", {
        key: index,
        style: {
          marginBottom: "4px"
        }
      }, error.message)))), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: "14px",
          color: "#666",
          margin: "0"
        }
      }, "To continue with your payment, you'll need to create a new payment agreement with updated limits.")), /*#__PURE__*/React.createElement("button", {
        onClick: resetPaymentAgreement,
        disabled: isSubmitting,
        style: {
          width: "100%",
          padding: "12px 20px",
          backgroundColor: isSubmitting ? "#ccc" : "#2CB5C5",
          color: "white",
          border: "none",
          borderRadius: "8px",
          fontSize: "16px",
          fontWeight: "600",
          cursor: isSubmitting ? "not-allowed" : "pointer",
          transition: "background-color 0.2s",
          marginBottom: "16px"
        },
        onMouseOver: e => {
          if (!isSubmitting) {
            e.target.style.backgroundColor = "#1a9aa8";
          }
        },
        onMouseOut: e => {
          if (!isSubmitting) {
            e.target.style.backgroundColor = "#2CB5C5";
          }
        }
      }, isSubmitting ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("span", {
        style: {
          display: "inline-block",
          width: "16px",
          height: "16px",
          border: "2px solid #ffffff",
          borderTop: "2px solid transparent",
          borderRadius: "50%",
          animation: "spin 1s linear infinite",
          marginRight: "8px"
        }
      }), "Resetting Agreement...") : "Pay Again with New Agreement")));
    }

    // Show failure state in main form
    if (paymentStatus === "failed") {
      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("div", {
        style: {
          textAlign: "center",
          margin: "24px 0"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          fontSize: "24px",
          fontWeight: "700",
          color: "#000",
          marginBottom: "8px"
        }
      }, "Pay", " ", /*#__PURE__*/React.createElement("span", {
        style: {
          fontWeight: "700",
          color: "#2CB5C5"
        }
      }, "$", orderTotal.toFixed(2))), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: "14px",
          color: "#6b7280",
          margin: "0"
        }
      }, "You're about to approve this payment from your banking app.")), /*#__PURE__*/React.createElement("div", {
        style: {
          border: "1px solid #E8E8E8",
          borderRadius: "16px",
          padding: "24px 24px 32px",
          gap: "16px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          textAlign: "center"
        }
      }, /*#__PURE__*/React.createElement("div", {
        style: {
          width: "45px",
          height: "45px",
          borderRadius: "50%",
          backgroundColor: "#FF0000",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          margin: "0 auto 16px"
        }
      }, /*#__PURE__*/React.createElement("span", {
        style: {
          color: "#ffffff",
          fontSize: "24px",
          fontWeight: "bold"
        }
      }, "i")), /*#__PURE__*/React.createElement("h3", {
        style: {
          fontSize: "24px",
          fontWeight: "600",
          color: "#000000",
          margin: "0 0 8px 0"
        }
      }, "Payment failed"), /*#__PURE__*/React.createElement("p", {
        style: {
          fontSize: "16px",
          color: "#000000",
          margin: "0 0 24px 0",
          lineHeight: "1.5"
        }
      }, errors.general || errorDetails?.message || "Something went wrong with the payment."), errorDetails && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: "24px",
          padding: "16px",
          backgroundColor: "#FFF2F2",
          border: "1px solid #FFCDD2",
          borderRadius: "8px",
          textAlign: "left",
          fontSize: "14px"
        }
      }, /*#__PURE__*/React.createElement("h4", {
        style: {
          margin: "0 0 12px 0",
          color: "#D32F2F",
          fontSize: "14px",
          fontWeight: "600"
        }
      }, "Error Details"), errorDetails.code && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: "8px"
        }
      }, /*#__PURE__*/React.createElement("strong", {
        style: {
          color: "#D32F2F"
        }
      }, "Error Code:"), " ", /*#__PURE__*/React.createElement("span", {
        style: {
          color: "#666"
        }
      }, errorDetails.code)), errorDetails.type && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: "8px"
        }
      }, /*#__PURE__*/React.createElement("strong", {
        style: {
          color: "#D32F2F"
        }
      }, "Error Type:"), " ", /*#__PURE__*/React.createElement("span", {
        style: {
          color: "#666"
        }
      }, errorDetails.type)), errorDetails.context && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: "8px"
        }
      }, /*#__PURE__*/React.createElement("strong", {
        style: {
          color: "#D32F2F"
        }
      }, "Context:"), " ", /*#__PURE__*/React.createElement("span", {
        style: {
          color: "#666"
        }
      }, errorDetails.context)), (errorDetails.agreement_status || errorDetails.payment_status || errorDetails.order_status) && /*#__PURE__*/React.createElement("div", {
        style: {
          marginBottom: "8px"
        }
      }, /*#__PURE__*/React.createElement("strong", {
        style: {
          color: "#D32F2F"
        }
      }, "Status:"), " ", /*#__PURE__*/React.createElement("span", {
        style: {
          color: "#666"
        }
      }, [errorDetails.agreement_status, errorDetails.payment_status, errorDetails.order_status].filter(Boolean).join(", "))), errorDetails.details && /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: "12px",
          fontSize: "13px"
        }
      }, /*#__PURE__*/React.createElement("strong", {
        style: {
          color: "#D32F2F"
        }
      }, "Technical Details:"), /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: "4px",
          padding: "8px",
          backgroundColor: "#FFFFFF",
          border: "1px solid #E0E0E0",
          borderRadius: "4px",
          color: "#666",
          fontFamily: "monospace",
          fontSize: "12px",
          wordBreak: "break-word"
        }
      }, typeof errorDetails.details === "string" ? errorDetails.details : JSON.stringify(errorDetails.details, null, 2))), errorDetails.validation_errors && /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: "12px",
          fontSize: "13px"
        }
      }, /*#__PURE__*/React.createElement("strong", {
        style: {
          color: "#D32F2F"
        }
      }, "Validation Errors:"), /*#__PURE__*/React.createElement("div", {
        style: {
          marginTop: "4px",
          color: "#666"
        }
      }, typeof errorDetails.validation_errors === "object" ? Object.entries(errorDetails.validation_errors).map(([field, error], index) => /*#__PURE__*/React.createElement("div", {
        key: index,
        style: {
          marginBottom: "4px"
        }
      }, /*#__PURE__*/React.createElement("strong", null, field, ":"), " ", error)) : errorDetails.validation_errors))), /*#__PURE__*/React.createElement("button", {
        onClick: () => {
          setPaymentStatus("form");
          setErrors({});
          setErrorDetails(null);
          setPaymentInstructions(null);
        },
        style: {
          width: "100%",
          padding: "12px 20px",
          backgroundColor: "#2CB5C5",
          color: "#000000",
          border: "none",
          borderRadius: "8px",
          cursor: "pointer",
          fontSize: "16px",
          fontWeight: "700",
          lineHeight: "1.21em"
        }
      }, "Pay Again"))));
    }
    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("div", {
      ref: containerRef
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        textAlign: "center",
        margin: "24px 0"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        fontSize: "24px",
        fontWeight: "700",
        color: "#000",
        marginBottom: "8px"
      }
    }, "Pay ", /*#__PURE__*/React.createElement("span", {
      style: {
        fontWeight: "700",
        color: "#2CB5C5"
      }
    }, "$", orderTotal.toFixed(2))), /*#__PURE__*/React.createElement("p", {
      style: {
        fontSize: "14px",
        color: "#6b7280",
        margin: "0"
      }
    }, "You're about to approve this payment from your banking app.")), !hasRequiredGuestInfo() && /*#__PURE__*/React.createElement("div", {
      style: {
        padding: "16px",
        backgroundColor: "#f3f4f6",
        borderRadius: "8px",
        margin: "16px 0",
        textAlign: "center"
      }
    }, /*#__PURE__*/React.createElement("p", {
      style: {
        margin: "0 0 8px 0",
        fontSize: "14px",
        color: "#374151"
      }
    }, "Please complete your billing information to initialize the payment form."), /*#__PURE__*/React.createElement("small", {
      style: {
        fontSize: "12px",
        color: "#6b7280"
      }
    }, "Required: Email, Name, Address, City, Postcode, and Country")), hasExpressCheckout && settings.is_user_logged_in && /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: "20px",
        textAlign: "center"
      }
    }, mandateInfo && /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: "16px",
        padding: "16px",
        backgroundColor: "#f0f8ff",
        border: "1px solid #0073aa",
        borderRadius: "8px",
        textAlign: "left"
      }
    }, /*#__PURE__*/React.createElement("h4", {
      style: {
        margin: "0 0 12px 0",
        color: "#0073aa",
        fontSize: "16px"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Express Checkout Available", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("p", {
      style: {
        margin: "0 0 12px 0",
        color: "#666",
        fontSize: "14px"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("You have an active PayTo mandate that can be used for faster checkout.", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("div", {
      style: {
        backgroundColor: "#ffffff",
        padding: "12px",
        borderRadius: "4px",
        fontSize: "13px",
        color: "#333"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: "6px"
      }
    }, /*#__PURE__*/React.createElement("strong", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Agreement:", "monoova-payments-for-woocommerce")), " ", mandateInfo.agreement_id ? `...${mandateInfo.agreement_id.slice(-8)}` : "Available"), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: "6px"
      }
    }, /*#__PURE__*/React.createElement("strong", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Maximum Amount:", "monoova-payments-for-woocommerce")), " ", currency, " $", parseFloat(mandateInfo.maximum_amount || 0).toFixed(2)), /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: "6px"
      }
    }, /*#__PURE__*/React.createElement("strong", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Status:", "monoova-payments-for-woocommerce")), " ", /*#__PURE__*/React.createElement("span", {
      style: {
        color: mandateInfo.status === "authorized" ? "#10b981" : "#f59e0b",
        fontWeight: "500"
      }
    }, mandateInfo.status || "Active")), mandateInfo.num_of_transactions_permitted && /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("strong", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Transactions:", "monoova-payments-for-woocommerce")), " ", /*#__PURE__*/React.createElement("span", {
      style: {
        color: mandateInfo.remaining_transactions > 0 ? "#10b981" : "#ef4444",
        fontWeight: "500"
      }
    }, mandateInfo.remaining_transactions || 0, " remaining"), " ", /*#__PURE__*/React.createElement("span", {
      style: {
        color: "#6b7280",
        fontSize: "12px"
      }
    }, "(", mandateInfo.num_of_paid_transactions || 0, "/", mandateInfo.num_of_transactions_permitted || 10, " used)")))), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: handleCheckoutWithExistingMandate,
      disabled: isSubmitting,
      style: {
        width: "100%",
        padding: "12px 20px",
        backgroundColor: isSubmitting ? "#9ca3af" : "#10b981",
        color: "#ffffff",
        border: "none",
        borderRadius: "8px",
        cursor: isSubmitting ? "not-allowed" : "pointer",
        fontSize: "14px",
        fontWeight: "600",
        marginBottom: "12px"
      }
    }, isSubmitting ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Processing...", "monoova-payments-for-woocommerce") : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("Pay with existing PayTo mandate", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("div", {
      style: {
        fontSize: "12px",
        color: "#6b7280",
        marginBottom: "16px"
      }
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)("or set up a new payment method below", "monoova-payments-for-woocommerce"))), hasRequiredGuestInfo() && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("div", {
      style: {
        marginBottom: "24px"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        gap: "24px",
        alignItems: "center",
        marginBottom: "16px"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        fontWeight: "500",
        fontSize: "18px",
        lineHeight: "1.21em",
        color: "#000000",
        marginBottom: "16px"
      }
    }, "Pay with"), /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        gap: "16px",
        alignItems: "center"
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        display: "flex",
        alignItems: "center",
        cursor: "pointer",
        gap: "8px"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        position: "relative"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: "24px",
        height: "24px",
        border: `1.5px solid ${paymentMethod === "payid" ? "#2CB5C5" : "#ABABAB"}`,
        borderRadius: "50%",
        position: "relative"
      }
    }, paymentMethod === "payid" && /*#__PURE__*/React.createElement("div", {
      style: {
        width: "14px",
        height: "14px",
        backgroundColor: "#2CB5C5",
        borderRadius: "50%",
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)"
      }
    })), /*#__PURE__*/React.createElement("input", {
      type: "radio",
      name: "payto_payment_method",
      value: "payid",
      checked: paymentMethod === "payid",
      onChange: e => setPaymentMethod(e.target.value),
      style: {
        display: "none"
      }
    })), /*#__PURE__*/React.createElement("span", {
      style: {
        fontWeight: "400",
        fontSize: "16px",
        lineHeight: "1.5em",
        color: "#000000"
      }
    }, "PayID")), /*#__PURE__*/React.createElement("label", {
      style: {
        display: "flex",
        alignItems: "center",
        cursor: "pointer",
        gap: "8px"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        position: "relative"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        width: "24px",
        height: "24px",
        border: `1.5px solid ${paymentMethod === "bsb" ? "#2CB5C5" : "#ABABAB"}`,
        borderRadius: "50%",
        position: "relative"
      }
    }, paymentMethod === "bsb_account" && /*#__PURE__*/React.createElement("div", {
      style: {
        width: "14px",
        height: "14px",
        backgroundColor: "#2CB5C5",
        borderRadius: "50%",
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)"
      }
    })), /*#__PURE__*/React.createElement("input", {
      type: "radio",
      name: "payto_payment_method",
      value: "bsb_account",
      checked: paymentMethod === "bsb_account",
      onChange: e => setPaymentMethod(e.target.value),
      style: {
        display: "none"
      }
    })), /*#__PURE__*/React.createElement("span", {
      style: {
        fontWeight: "400",
        fontSize: "16px",
        lineHeight: "1.5em",
        color: "#000000"
      }
    }, "BSB and account number"))))), /*#__PURE__*/React.createElement("div", {
      style: {
        border: "1px solid #E8E8E8",
        borderRadius: "16px",
        padding: "24px 24px 32px",
        gap: "16px",
        display: "flex",
        flexDirection: "column",
        alignItems: "center"
      }
    }, paymentMethod === "payid" && /*#__PURE__*/React.createElement("div", {
      style: {
        width: "100%",
        display: "flex",
        flexDirection: "column"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        alignItems: "center"
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        fontWeight: "600",
        fontSize: "16px",
        lineHeight: "1.5em",
        color: "#000000"
      }
    }, "Enter your PayID Email or Mobile number")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        alignItems: "stretch",
        gap: "10px",
        padding: "0px 12px",
        backgroundColor: "#FAFAFA",
        border: `1px solid ${errors.payidValue ? "#ef4444" : "#E8E8E8"}`,
        borderRadius: "8px",
        minHeight: "48px"
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      value: payidValue,
      onChange: e => {
        const value = e.target.value;
        setPayidValue(value);
        validatePayID(value);
      },
      placeholder: "Email or mobile number",
      style: {
        flex: 1,
        border: "none",
        backgroundColor: "transparent",
        outline: "none",
        fontWeight: "500",
        fontSize: "16px",
        lineHeight: "1.21em",
        color: errors.payidValue ? "#ef4444" : "#9D9C9C",
        padding: "12px 0"
      }
    })), errors.payidValue && /*#__PURE__*/React.createElement("div", {
      style: {
        color: "#ef4444",
        fontSize: "12px",
        marginTop: "6px",
        display: "flex",
        alignItems: "center",
        gap: "4px"
      }
    }, errors.payidValue)), paymentMethod === "bsb_account" && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("div", {
      style: {
        width: "100%",
        display: "flex",
        flexDirection: "column"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        alignItems: "center"
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        fontWeight: "600",
        fontSize: "16px",
        lineHeight: "1.5em",
        color: "#000000"
      }
    }, "Name associated with bank account")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        alignItems: "stretch",
        gap: "10px",
        padding: "0px 12px",
        backgroundColor: "#FAFAFA",
        border: `1px solid ${errors.accountName ? "#ef4444" : "#E8E8E8"}`,
        borderRadius: "8px",
        minHeight: "48px"
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      value: accountName,
      onChange: e => setAccountName(e.target.value),
      placeholder: "Enter your name",
      style: {
        flex: 1,
        border: "none",
        backgroundColor: "transparent",
        outline: "none",
        fontWeight: "500",
        fontSize: "16px",
        lineHeight: "1.21em",
        color: errors.accountName ? "#ef4444" : "#9D9C9C",
        padding: "12px 0"
      }
    })), errors.accountName && /*#__PURE__*/React.createElement("div", {
      style: {
        color: "#ef4444",
        fontSize: "12px",
        marginTop: "6px",
        display: "flex",
        alignItems: "center",
        gap: "4px"
      }
    }, errors.accountName)), /*#__PURE__*/React.createElement("div", {
      style: {
        width: "100%",
        display: "flex",
        flexDirection: "column"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        alignItems: "center"
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        fontWeight: "600",
        fontSize: "16px",
        lineHeight: "1.5em",
        color: "#000000"
      }
    }, "BSB")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        alignItems: "stretch",
        gap: "10px",
        padding: "0px 12px",
        backgroundColor: "#FAFAFA",
        border: `1px solid ${errors.bsb ? "#ef4444" : "#E8E8E8"}`,
        borderRadius: "8px",
        minHeight: "48px"
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      value: bsb,
      onChange: e => setBsb(e.target.value),
      placeholder: "123-456",
      style: {
        flex: 1,
        border: "none",
        backgroundColor: "transparent",
        outline: "none",
        fontWeight: "500",
        fontSize: "16px",
        lineHeight: "1.21em",
        color: errors.bsb ? "#ef4444" : "#9D9C9C",
        padding: "12px 0"
      }
    })), errors.bsb && /*#__PURE__*/React.createElement("div", {
      style: {
        color: "#ef4444",
        fontSize: "12px",
        marginTop: "6px",
        display: "flex",
        alignItems: "center",
        gap: "4px"
      }
    }, errors.bsb)), /*#__PURE__*/React.createElement("div", {
      style: {
        width: "100%",
        display: "flex",
        flexDirection: "column"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        alignItems: "center"
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        fontWeight: "600",
        fontSize: "16px",
        lineHeight: "1.5em",
        color: "#000000"
      }
    }, "Account Number")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        alignItems: "stretch",
        gap: "10px",
        padding: "0px 12px",
        backgroundColor: "#FAFAFA",
        border: `1px solid ${errors.accountNumber ? "#ef4444" : "#E8E8E8"}`,
        borderRadius: "8px",
        minHeight: "48px"
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "text",
      value: accountNumber,
      onChange: e => setAccountNumber(e.target.value),
      placeholder: "Enter your account number",
      style: {
        flex: 1,
        border: "none",
        backgroundColor: "transparent",
        outline: "none",
        fontWeight: "500",
        fontSize: "16px",
        lineHeight: "1.21em",
        color: errors.accountNumber ? "#ef4444" : "#9D9C9C",
        padding: "12px 0"
      }
    })), errors.accountNumber && /*#__PURE__*/React.createElement("div", {
      style: {
        color: "#ef4444",
        fontSize: "12px",
        marginTop: "6px",
        display: "flex",
        alignItems: "center",
        gap: "4px"
      }
    }, errors.accountNumber))), /*#__PURE__*/React.createElement("div", {
      style: {
        width: "100%",
        display: "flex",
        flexDirection: "column",
        gap: "12px"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        flexDirection: "column"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        alignItems: "center"
      }
    }, /*#__PURE__*/React.createElement("label", {
      style: {
        fontWeight: "600",
        fontSize: "16px",
        lineHeight: "1.5em",
        color: "#000000"
      }
    }, "Maximum payment agreement amount")), /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        alignItems: "stretch",
        gap: "10px",
        padding: "0px 12px",
        backgroundColor: "#FAFAFA",
        border: `1px solid ${errors.maximumAmount ? "#ef4444" : "#E8E8E8"}`,
        borderRadius: "8px",
        minHeight: "48px"
      }
    }, /*#__PURE__*/React.createElement("input", {
      type: "number",
      value: maximumAmount,
      onChange: e => setMaximumAmount(parseFloat(e.target.value) || 0),
      min: "0.01",
      step: "0.01",
      placeholder: "$1,000",
      style: {
        flex: 1,
        border: "none",
        backgroundColor: "transparent",
        outline: "none",
        fontWeight: "500",
        fontSize: "16px",
        lineHeight: "1.21em",
        color: "#000000",
        padding: "12px 0"
      }
    })), errors.maximumAmount && /*#__PURE__*/React.createElement("div", {
      style: {
        color: "#ef4444",
        fontSize: "12px",
        marginTop: "6px",
        display: "flex",
        alignItems: "center",
        gap: "4px"
      }
    }, errors.maximumAmount)), /*#__PURE__*/React.createElement("div", {
      style: {
        fontWeight: "500",
        fontSize: "14px",
        lineHeight: "1.29em",
        color: "#909090"
      }
    }, "This is the maximum amount that can be charged under this PayTo agreement. You can modify this amount if needed.")), /*#__PURE__*/React.createElement("div", {
      style: {
        width: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        gap: "10px",
        padding: "0px 12px",
        backgroundColor: "#2CB5C5",
        borderRadius: "8px",
        minHeight: "48px",
        cursor: isSubmitting ? "not-allowed" : "pointer"
      },
      onClick: isSubmitting ? undefined : handlePayToSubmit
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontWeight: "700",
        fontSize: "16px",
        lineHeight: "1.21em",
        color: "#000000"
      }
    }, isSubmitting || paymentStatus === "processing" ? "Processing..." : "Accept and Continue"))))));
  };

  /**
   * Content component for PayTo
   */
  const Content = function (props) {
    return /*#__PURE__*/React.createElement(PayToForm, props);
  };

  /**
   * Label component with PayTo icon and description
   */
  const Label = props => {
    return /*#__PURE__*/React.createElement("div", {
      style: {
        width: "100%"
      }
    }, /*#__PURE__*/React.createElement("div", {
      style: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%"
      }
    }, /*#__PURE__*/React.createElement("span", {
      style: {
        fontWeight: 600
      }
    }, label), /*#__PURE__*/React.createElement("img", {
      src: `${settings.plugin_url || "/wp-content/plugins/monoova-payments-for-woocommerce/"}assets/images/payto-logo.svg`,
      alt: "PayTo logo",
      style: {
        height: "24px",
        width: "auto"
      }
    })), settings.description && /*#__PURE__*/React.createElement("div", {
      style: {
        marginTop: "8px",
        fontSize: "14px",
        color: "#6b7280",
        lineHeight: "1.4"
      },
      dangerouslySetInnerHTML: {
        __html: (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_3__.decodeEntities)(settings.description || "")
      }
    }));
  };

  /**
   * Monoova PayTo payment method config object
   */
  const MonoovaPayTo = {
    name: "monoova_payto",
    label: /*#__PURE__*/React.createElement(Label, null),
    content: /*#__PURE__*/React.createElement(Content, null),
    edit: /*#__PURE__*/React.createElement(Content, null),
    canMakePayment: () => {
      return true;
    },
    ariaLabel: label,
    supports: {
      features: settings.supports || []
    },
    icons: settings.icons || null
  };

  // Register the payment method
  try {
    (0,_woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__.registerPaymentMethod)(MonoovaPayTo);
  } catch (error) {
    console.error("Monoova PayTo Block: Failed to register payment method:", error);
  }
}
})();

/******/ })()
;
//# sourceMappingURL=monoova-payto-block.js.map