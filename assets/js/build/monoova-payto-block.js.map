{"version": 3, "file": "monoova-payto-block.js", "mappings": ";;;;;;;;;;;;;;;;;AAA6E;AACgB;AAEtF,MAAMK,wBAAwB,GAAGA,CAAC;EACrCC,QAAQ;EACRC,OAAO;EACPC,OAAO,EAAEC,eAAe;EACxBC,WAAW,GAAG,yBAAyB;EACvCC,eAAe,GAAG,eAAe;EACjCC,eAAe,GAAG;AACtB,CAAC,KAAK;EACF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,4DAAQ,CAAC,OAAO,CAAC;EAC3D,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,4DAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,4DAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,4DAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,GAAG,EAAEC,MAAM,CAAC,GAAGtB,4DAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,4DAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,4DAAQ,CAACM,QAAQ,CAACqB,cAAc,IAAI,IAAI,CAAC;EACnF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG7B,4DAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,4DAAQ,CAAC,MAAM,CAAC;EAC1D,MAAM,CAACgC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjC,4DAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,4DAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,4DAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvC,4DAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,4DAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,4DAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,4DAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM8C,eAAe,GAAG3C,0DAAM,CAAC,KAAK,CAAC;EACrC,MAAM4C,kBAAkB,GAAG5C,0DAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM;IACF6C,SAAS;IACTC,gBAAgB;IAChBC,iBAAiB;IACjBC,sBAAsB;IACtBC,uBAAuB;IACvBC,YAAY;IACZC,YAAY;IACZC,cAAc;IACdC,aAAa;IACbC;EACJ,CAAC,GAAGrD,2GAAoC,CAACO,eAAe,EAAED,WAAW,CAAC;;EAEtE;EACA,MAAMgD,cAAc,GAAGJ,YAAY,CAAC,CAAC;EACrC,MAAMK,qBAAqB,GAAGD,cAAc,EAAEE,YAAY;EAC1D,MAAMC,iBAAiB,GAAGH,cAAc,EAAElD,OAAO;;EAEjD;EACA,MAAMsD,qBAAqB,GAAGX,sBAAsB,IAAIQ,qBAAqB;;EAE7E;EACA,MAAMI,WAAW,GAAG7D,+DAAW,CAAC,MAAM;IAClC,IAAI6C,kBAAkB,CAACiB,OAAO,EAAE;MAC5BC,aAAa,CAAClB,kBAAkB,CAACiB,OAAO,CAAC;MACzCjB,kBAAkB,CAACiB,OAAO,GAAG,IAAI;IACrC;IACA,IAAI5B,YAAY,EAAE;MACd6B,aAAa,CAAC7B,YAAY,CAAC;MAC3BC,eAAe,CAAC,IAAI,CAAC;IACzB;EACJ,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;;EAElB;EACAnC,6DAAS,CAAC,MAAM;IACZ,IAAI6D,qBAAqB,IAAIH,qBAAqB,EAAE;MAChDO,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAER,qBAAqB,CAAC;;MAE/E;MACA,IAAIA,qBAAqB,CAACS,QAAQ,EAAE;QAChCtD,gBAAgB,CAAC6C,qBAAqB,CAACS,QAAQ,CAACvD,aAAa,IAAI,OAAO,CAAC;QACzEG,aAAa,CAAC2C,qBAAqB,CAACS,QAAQ,CAACrD,UAAU,IAAI,EAAE,CAAC;QAC9DG,YAAY,CAACyC,qBAAqB,CAACS,QAAQ,CAACnD,SAAS,IAAI,EAAE,CAAC;QAC5DG,cAAc,CAACuC,qBAAqB,CAACS,QAAQ,CAACjD,WAAW,IAAI,EAAE,CAAC;QAChEG,MAAM,CAACqC,qBAAqB,CAACS,QAAQ,CAAC/C,GAAG,IAAI,EAAE,CAAC;QAChDG,gBAAgB,CAACmC,qBAAqB,CAACS,QAAQ,CAAC7C,aAAa,IAAI,EAAE,CAAC;QACpEG,gBAAgB,CAACiC,qBAAqB,CAACS,QAAQ,CAAC3C,aAAa,IAAInB,QAAQ,CAACqB,cAAc,IAAI,IAAI,CAAC;MACrG;;MAEA;MACA,IAAIgC,qBAAqB,CAAC3B,mBAAmB,EAAE;QAC3CC,sBAAsB,CAAC0B,qBAAqB,CAAC3B,mBAAmB,CAAC;QACjED,gBAAgB,CAAC4B,qBAAqB,CAAC7B,aAAa,IAAI,cAAc,CAAC;MAC3E;;MAEA;MACA,IAAI6B,qBAAqB,CAACrB,kBAAkB,KAAK+B,SAAS,EAAE;QACxD9B,qBAAqB,CAACoB,qBAAqB,CAACrB,kBAAkB,CAAC;MACnE;MAEA,IAAIqB,qBAAqB,CAACjB,WAAW,EAAE;QACnCC,cAAc,CAACgB,qBAAqB,CAACjB,WAAW,CAAC;MACrD;IACJ;EACJ,CAAC,EAAE,CAACoB,qBAAqB,EAAEH,qBAAqB,EAAErD,QAAQ,CAACqB,cAAc,CAAC,CAAC;;EAE3E;EACA,MAAM2C,gBAAgB,GAAGpE,+DAAW,CAAC,MAAM;IACvC,MAAMqE,YAAY,GAAG;MACjBH,QAAQ,EAAE;QACNvD,aAAa;QACbE,UAAU;QACVE,SAAS;QACTE,WAAW;QACXE,GAAG;QACHE,aAAa;QACbE;MACJ,CAAC;MACDO,mBAAmB;MACnBF,aAAa;MACbQ,kBAAkB;MAClBI,WAAW;MACXd,MAAM;MACNY;IACJ,CAAC;IAEDa,YAAY,CAAC5C,eAAe,EAAE,IAAI,EAAE8D,YAAY,CAAC;EACrD,CAAC,EAAE,CACC1D,aAAa,EACbE,UAAU,EACVE,SAAS,EACTE,WAAW,EACXE,GAAG,EACHE,aAAa,EACbE,aAAa,EACbO,mBAAmB,EACnBF,aAAa,EACbQ,kBAAkB,EAClBI,WAAW,EACXd,MAAM,EACNY,YAAY,EACZ/B,eAAe,EACf4C,YAAY,CACf,CAAC;;EAEF;EACApD,6DAAS,CAAC,MAAM;IACZ,IAAIkD,sBAAsB,EAAE;MACxBmB,gBAAgB,CAAC,CAAC;IACtB;EACJ,CAAC,EAAE,CAACA,gBAAgB,EAAEnB,sBAAsB,CAAC,CAAC;;EAE9C;EACA,MAAMqB,WAAW,GAAGtE,+DAAW,CAAC,MAAM;IAClCY,gBAAgB,CAAC,OAAO,CAAC;IACzBE,aAAa,CAAC,EAAE,CAAC;IACjBE,YAAY,CAAC,EAAE,CAAC;IAChBE,cAAc,CAAC,EAAE,CAAC;IAClBE,MAAM,CAAC,EAAE,CAAC;IACVE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,gBAAgB,CAACpB,QAAQ,CAACqB,cAAc,IAAI,IAAI,CAAC;IACjDE,SAAS,CAAC,CAAC,CAAC,CAAC;IACbE,gBAAgB,CAAC,MAAM,CAAC;IACxBE,sBAAsB,CAAC,IAAI,CAAC;IAC5BE,eAAe,CAAC,KAAK,CAAC;IACtBI,qBAAqB,CAAC,KAAK,CAAC;IAC5BE,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,IAAI,CAAC;IACpBE,YAAY,CAAC,KAAK,CAAC;IACnBkB,WAAW,CAAC,CAAC;IAEb,IAAIR,cAAc,EAAE;MAChBA,cAAc,CAAC,CAAC;IACpB;EACJ,CAAC,EAAE,CAACjD,QAAQ,CAACqB,cAAc,EAAEoC,WAAW,EAAER,cAAc,CAAC,CAAC;;EAE1D;EACAtD,6DAAS,CAAC,MAAM;IACZ,OAAO,MAAM;MACT8D,WAAW,CAAC,CAAC;IACjB,CAAC;EACL,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB,OAAO;IACH;IACAU,YAAY,EAAEzB,SAAS;IACvBC,gBAAgB;IAChBC,iBAAiB;IACjBwB,aAAa,EAAEvB,sBAAsB;IACrCC,uBAAuB;IACvBI,aAAa;IACbC,aAAa;IAEb;IACA5C,aAAa;IACbC,gBAAgB;IAChBC,UAAU;IACVC,aAAa;IACbC,SAAS;IACTC,YAAY;IACZC,WAAW;IACXC,cAAc;IACdC,GAAG;IACHC,MAAM;IACNC,aAAa;IACbC,gBAAgB;IAChBC,aAAa;IACbC,gBAAgB;IAChBE,MAAM;IACNC,SAAS;IACTC,aAAa;IACbC,gBAAgB;IAChBC,mBAAmB;IACnBC,sBAAsB;IACtBC,YAAY;IACZC,eAAe;IACfC,YAAY;IACZC,eAAe;IACfC,kBAAkB;IAClBC,qBAAqB;IACrBC,YAAY;IACZC,eAAe;IACfC,WAAW;IACXC,cAAc;IACdC,SAAS;IACTC,YAAY;IAEZ;IACA2B,WAAW;IACXT,WAAW;IACXO,gBAAgB;IAEhB;IACAZ,cAAc;IACdI;EACJ,CAAC;AACL,CAAC;;;;;;;;;;;;;;;;;ACtOsD;;AAEvD;AACA,MAAMa,qBAAqB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC,IAAI,CAACN,aAAa,GAAG,KAAK;EAC9B;;EAEA;EACAO,oBAAoBA,CAACtE,eAAe,EAAED,WAAW,EAAE;IAC/C,MAAMwE,YAAY,GAAG,GAAGvE,eAAe,IAAID,WAAW,EAAE;IAExD,IAAI,CAAC,IAAI,CAACmE,UAAU,CAACM,GAAG,CAACD,YAAY,CAAC,EAAE;MACpC;MACA,MAAME,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/CF,SAAS,CAACG,EAAE,GAAG7E,WAAW;MAC1B0E,SAAS,CAACI,SAAS,GAAG,sCAAsC;MAC5DJ,SAAS,CAACK,KAAK,CAACC,OAAO,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;MAED;MACAL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,SAAS,CAAC;MAEpC,IAAI,CAACP,UAAU,CAACgB,GAAG,CAACX,YAAY,EAAE;QAC9BY,OAAO,EAAEV,SAAS;QAClBV,aAAa,EAAE,KAAK;QACpBqB,SAAS,EAAE,KAAK;QAChBvF,OAAO,EAAE,IAAI;QAAE;QACfwF,WAAW,EAAE,IAAI,CAAC;MACtB,CAAC,CAAC;IACN;IAEA,OAAO,IAAI,CAACnB,UAAU,CAACoB,GAAG,CAACf,YAAY,CAAC;EAC5C;;EAEA;EACA1B,aAAaA,CAAC7C,eAAe,EAAED,WAAW,EAAEwF,aAAa,EAAE;IACvD,MAAMhB,YAAY,GAAG,GAAGvE,eAAe,IAAID,WAAW,EAAE;IACxD,MAAMyF,aAAa,GAAG,IAAI,CAACtB,UAAU,CAACoB,GAAG,CAACf,YAAY,CAAC;IAEvD,IAAIiB,aAAa,IAAIA,aAAa,CAACL,OAAO,EAAE;MACxC;MACA,IAAI,CAACM,iBAAiB,CAAC,CAAC;;MAExB;MACA,IAAIF,aAAa,EAAE;QACfA,aAAa,CAACN,WAAW,CAACO,aAAa,CAACL,OAAO,CAAC;QAChDK,aAAa,CAACL,OAAO,CAACL,KAAK,CAACC,OAAO,GAAG;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;QACDS,aAAa,CAACJ,SAAS,GAAG,IAAI;QAC9B,IAAI,CAAChB,gBAAgB,CAACsB,GAAG,CAACnB,YAAY,CAAC;MAC3C;IACJ;EACJ;;EAEA;EACAzB,aAAaA,CAAC9C,eAAe,EAAED,WAAW,EAAE;IACxC,MAAMwE,YAAY,GAAG,GAAGvE,eAAe,IAAID,WAAW,EAAE;IACxD,MAAMyF,aAAa,GAAG,IAAI,CAACtB,UAAU,CAACoB,GAAG,CAACf,YAAY,CAAC;IAEvD,IAAIiB,aAAa,IAAIA,aAAa,CAACL,OAAO,EAAE;MACxC;MACAT,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACO,aAAa,CAACL,OAAO,CAAC;MAChDK,aAAa,CAACL,OAAO,CAACL,KAAK,CAACC,OAAO,GAAG;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;MACDS,aAAa,CAACJ,SAAS,GAAG,KAAK;MAC/B,IAAI,CAAChB,gBAAgB,CAACuB,MAAM,CAACpB,YAAY,CAAC;IAC9C;EACJ;;EAEA;EACAkB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvB,UAAU,CAAC0B,OAAO,CAAC,CAACJ,aAAa,EAAEjB,YAAY,KAAK;MACrD,IAAIiB,aAAa,CAACJ,SAAS,EAAE;QACzB,IAAI,CAACtC,aAAa,CAACyB,YAAY,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEtB,YAAY,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9E;IACJ,CAAC,CAAC;EACN;;EAEA;EACAjD,cAAcA,CAAC5C,eAAe,EAAED,WAAW,EAAE;IACzC,MAAMwE,YAAY,GAAG,GAAGvE,eAAe,IAAID,WAAW,EAAE;IACxD,MAAMyF,aAAa,GAAG,IAAI,CAACtB,UAAU,CAACoB,GAAG,CAACf,YAAY,CAAC;IAEvD,IAAIiB,aAAa,EAAE;MACfA,aAAa,CAAC3F,OAAO,GAAG,IAAI;MAC5B2F,aAAa,CAACH,WAAW,GAAG,IAAI;MAChCG,aAAa,CAACvC,YAAY,GAAG,IAAI,CAAC,CAAC;MACnCuC,aAAa,CAACzB,aAAa,GAAG,KAAK,CAAC,CAAC;IACzC;EACJ;;EAEA;EACAtB,uBAAuBA,CAACzC,eAAe,EAAED,WAAW,EAAE;IAClD,MAAMwE,YAAY,GAAG,GAAGvE,eAAe,IAAID,WAAW,EAAE;IACxD,MAAMyF,aAAa,GAAG,IAAI,CAACtB,UAAU,CAACoB,GAAG,CAACf,YAAY,CAAC;IACvD,IAAIiB,aAAa,EAAE;MACfA,aAAa,CAACzB,aAAa,GAAG,IAAI;IACtC;EACJ;;EAEA;EACAvB,sBAAsBA,CAACxC,eAAe,EAAED,WAAW,EAAE;IACjD,MAAMwE,YAAY,GAAG,GAAGvE,eAAe,IAAID,WAAW,EAAE;IACxD,MAAMyF,aAAa,GAAG,IAAI,CAACtB,UAAU,CAACoB,GAAG,CAACf,YAAY,CAAC;IACvD,OAAOiB,aAAa,GAAGA,aAAa,CAACzB,aAAa,GAAG,KAAK;EAC9D;;EAEA;EACArB,YAAYA,CAAC1C,eAAe,EAAED,WAAW,EAAEF,OAAO,EAAEwF,WAAW,EAAEpC,YAAY,GAAG,IAAI,EAAE;IAClF,MAAMsB,YAAY,GAAG,GAAGvE,eAAe,IAAID,WAAW,EAAE;IACxD,MAAMyF,aAAa,GAAG,IAAI,CAACtB,UAAU,CAACoB,GAAG,CAACf,YAAY,CAAC;IACvD,IAAIiB,aAAa,EAAE;MACfA,aAAa,CAAC3F,OAAO,GAAGA,OAAO;MAC/B2F,aAAa,CAACH,WAAW,GAAGA,WAAW;MACvCG,aAAa,CAACvC,YAAY,GAAGA,YAAY,CAAC,CAAC;IAC/C,CAAC,MAAM;MACHM,OAAO,CAACuC,IAAI,CAAC,qCAAqCvB,YAAY,mCAAmC,CAAC;IACtG;EACJ;;EAEA;EACA5B,YAAYA,CAAC3C,eAAe,EAAED,WAAW,EAAE;IACvC,MAAMwE,YAAY,GAAG,GAAGvE,eAAe,IAAID,WAAW,EAAE;IACxD,MAAMyF,aAAa,GAAG,IAAI,CAACtB,UAAU,CAACoB,GAAG,CAACf,YAAY,CAAC;IACvD,MAAMwB,MAAM,GAAGP,aAAa,GAAG;MAC3B3F,OAAO,EAAE2F,aAAa,CAAC3F,OAAO;MAC9BwF,WAAW,EAAEG,aAAa,CAACH,WAAW;MACtCpC,YAAY,EAAEuC,aAAa,CAACvC,YAAY,CAAC;IAC7C,CAAC,GAAG;MAAEpD,OAAO,EAAE,IAAI;MAAEwF,WAAW,EAAE,IAAI;MAAEpC,YAAY,EAAE;IAAK,CAAC;IAE5D,OAAO8C,MAAM;EACjB;;EAEA;EACAC,mBAAmBA,CAAChG,eAAe,EAAED,WAAW,EAAE;IAC9C,MAAMwE,YAAY,GAAG,GAAGvE,eAAe,IAAID,WAAW,EAAE;IACxD,MAAMyF,aAAa,GAAG,IAAI,CAACtB,UAAU,CAACoB,GAAG,CAACf,YAAY,CAAC;IACvD,OAAOiB,aAAa,GAAGA,aAAa,CAACL,OAAO,GAAG,IAAI;EACvD;;EAEA;EACAc,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC/B,UAAU,CAAC0B,OAAO,CAAEJ,aAAa,IAAK;MACvC,IAAIA,aAAa,CAACL,OAAO,IAAIK,aAAa,CAACL,OAAO,CAACe,UAAU,EAAE;QAC3DV,aAAa,CAACL,OAAO,CAACe,UAAU,CAACC,WAAW,CAACX,aAAa,CAACL,OAAO,CAAC;MACvE;IACJ,CAAC,CAAC;IACF,IAAI,CAACjB,UAAU,CAACkC,KAAK,CAAC,CAAC;IACvB,IAAI,CAAChC,gBAAgB,CAACgC,KAAK,CAAC,CAAC;EACjC;AACJ;;AAEA;AACA,MAAMC,qBAAqB,GAAG,IAAIrC,qBAAqB,CAAC,CAAC;;AAEzD;AACO,MAAMvE,oCAAoC,GAAGA,CAACO,eAAe,EAAED,WAAW,KAAK;EAClF,MAAMsC,SAAS,GAAG7C,0DAAM,CAAC,IAAI,CAAC;EAC9B,MAAM8G,WAAW,GAAG9G,0DAAM,CAAC,KAAK,CAAC;;EAEjC;EACA,MAAM+G,qBAAqB,GAAG,cAAcvG,eAAe,IAAID,WAAW,EAAE;EAE5ET,6DAAS,CAAC,MAAM;IACZ;IACA,MAAMkG,aAAa,GAAGa,qBAAqB,CAAC/B,oBAAoB,CAACtE,eAAe,EAAED,WAAW,CAAC;;IAE9F;IACA,IAAIyF,aAAa,CAACL,OAAO,EAAE;MACvBK,aAAa,CAACL,OAAO,CAACP,EAAE,GAAG2B,qBAAqB;IACpD;;IAEA;IACA,IAAIlE,SAAS,CAACgB,OAAO,IAAI,CAACiD,WAAW,CAACjD,OAAO,EAAE;MAC3CgD,qBAAqB,CAACxD,aAAa,CAAC7C,eAAe,EAAED,WAAW,EAAEsC,SAAS,CAACgB,OAAO,CAAC;MACpFiD,WAAW,CAACjD,OAAO,GAAG,IAAI;IAC9B;;IAEA;IACA,OAAO,MAAM;MACT,IAAIiD,WAAW,CAACjD,OAAO,EAAE;QACrBgD,qBAAqB,CAACvD,aAAa,CAAC9C,eAAe,EAAED,WAAW,CAAC;QACjEuG,WAAW,CAACjD,OAAO,GAAG,KAAK;MAC/B;IACJ,CAAC;EACL,CAAC,EAAE,CAACrD,eAAe,EAAED,WAAW,CAAC,CAAC;EAElC,OAAO;IACHsC,SAAS;IACTC,gBAAgB,EAAE+D,qBAAqB,CAACL,mBAAmB,CAAChG,eAAe,EAAED,WAAW,CAAC;IACzFwC,iBAAiB,EAAEgE,qBAAqB;IAAE;IAC1C/D,sBAAsB,EAAE6D,qBAAqB,CAAC7D,sBAAsB,CAACxC,eAAe,EAAED,WAAW,CAAC;IAClG0C,uBAAuB,EAAEA,CAAA,KAAM4D,qBAAqB,CAAC5D,uBAAuB,CAACzC,eAAe,EAAED,WAAW,CAAC;IAC1G;IACA2C,YAAY,EAAEA,CAAC7C,OAAO,EAAEwF,WAAW,EAAEpC,YAAY,GAAG,IAAI,KAAKoD,qBAAqB,CAAC3D,YAAY,CAAC1C,eAAe,EAAED,WAAW,EAAEF,OAAO,EAAEwF,WAAW,EAAEpC,YAAY,CAAC;IACjKN,YAAY,EAAEA,CAAA,KAAM0D,qBAAqB,CAAC1D,YAAY,CAAC3C,eAAe,EAAED,WAAW,CAAC;IACpF6C,cAAc,EAAEA,CAAA,KAAM;MAClByD,qBAAqB,CAACzD,cAAc,CAAC5C,eAAe,EAAED,WAAW,CAAC;IACtE,CAAC;IACD8C,aAAa,EAAEA,CAAA,KAAM;MACjB,IAAIR,SAAS,CAACgB,OAAO,IAAI,CAACiD,WAAW,CAACjD,OAAO,EAAE;QAC3CgD,qBAAqB,CAACxD,aAAa,CAAC7C,eAAe,EAAED,WAAW,EAAEsC,SAAS,CAACgB,OAAO,CAAC;QACpFiD,WAAW,CAACjD,OAAO,GAAG,IAAI;MAC9B;IACJ,CAAC;IACDP,aAAa,EAAEA,CAAA,KAAM;MACjB,IAAIwD,WAAW,CAACjD,OAAO,EAAE;QACrBgD,qBAAqB,CAACvD,aAAa,CAAC9C,eAAe,EAAED,WAAW,CAAC;QACjEuG,WAAW,CAACjD,OAAO,GAAG,KAAK;MAC/B;IACJ;EACJ,CAAC;AACL,CAAC;;AAED;AACA,IAAI,OAAOmD,MAAM,KAAK,WAAW,EAAE;EAC/BA,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAE,MAAM;IAC1CJ,qBAAqB,CAACJ,OAAO,CAAC,CAAC;EACnC,CAAC,CAAC;AACN;AAEA,iEAAeI,qBAAqB;;;;;;;;;;ACxPpC;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;;AAEoE;AAClB;AACU;AACH;AACrB;AACoB;AACb;AAC+B;AAC1E;AAC4E;;AAE5E;AACA,MAAMc,aAAa,GAAG;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOzC,QAAQ,KAAK,WAAW,EAAE;EACjC,MAAM0C,UAAU,GAAG1C,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;EAClDyC,UAAU,CAACC,IAAI,GAAG,UAAU;EAC5BD,UAAU,CAACE,SAAS,GAAGH,aAAa;EACpCzC,QAAQ,CAAC6C,IAAI,CAACtC,WAAW,CAACmC,UAAU,CAAC;AACzC;;AAEA;AACA,IAAI,OAAOV,+EAAqB,KAAK,UAAU,EAAE;EAC7CnD,OAAO,CAACuC,IAAI,CACR,8EAA8E,EAC9E0B,MAAM,CAACC,IAAI,CAACjB,MAAM,CAACkB,EAAE,IAAI,CAAC,CAAC,CAC/B,CAAC;AACL,CAAC,MAAM;EACH;EACA,IAAI/H,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAI,OAAOgH,6DAAU,KAAK,UAAU,EAAE;IAClC,IAAI;MACAhH,QAAQ,GAAGgH,iEAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACZpE,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEmE,KAAK,CAAC;IACjE;EACJ;;EAEA;EACA,IAAI,CAAChI,QAAQ,IAAI6H,MAAM,CAACC,IAAI,CAAC9H,QAAQ,CAAC,CAACiI,MAAM,KAAK,CAAC,EAAE;IACjDjI,QAAQ,GAAG6G,MAAM,CAACqB,2BAA2B,IAAI,CAAC,CAAC;IACnDtE,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE7D,QAAQ,CAAC;EAC1E;;EAEA;EACA,IAAI,CAACA,QAAQ,IAAI6H,MAAM,CAACC,IAAI,CAAC9H,QAAQ,CAAC,CAACiI,MAAM,KAAK,CAAC,EAAE;IACjDrE,OAAO,CAACuC,IAAI,CAAC,wDAAwD,CAAC;IACtEnG,QAAQ,GAAG;MACPmI,KAAK,EAAE,OAAO;MACdC,WAAW,EAAE,6EAA6E;MAC1FC,QAAQ,EAAE;IACd,CAAC;EACL;EAEA,MAAMC,YAAY,GAAGnB,mDAAE,CAAC,OAAO,EAAE,kCAAkC,CAAC;EACpE,MAAMoB,KAAK,GAAGrB,wEAAc,CAAClH,QAAQ,CAACmI,KAAK,CAAC,IAAIG,YAAY;;EAE5D;AACJ;AACA;EACI,MAAME,SAAS,GAAGA,CAAC;IAAEC,iBAAiB;IAAEC,YAAY;IAAEzI;EAAQ,CAAC,KAAK;IAChE;IACA,MAAM;MAAEC;IAAQ,CAAC,GAAGkH,0DAAS,CAACuB,MAAM,IAAI;MACpC,MAAMC,KAAK,GAAGD,MAAM,CAAC1B,uEAAkB,CAAC;MACxC,OAAO;QACH/G,OAAO,EAAE0I,KAAK,CAACC,UAAU,CAAC;MAC9B,CAAC;IACL,CAAC,CAAC;;IAEF;IACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;MAC/B,MAAMC,cAAc,GAAG9I,OAAO,EAAE8I,cAAc;MAC9C,IAAI,CAACA,cAAc,EAAE,OAAO,KAAK;;MAEjC;MACA,OAAO,CAAC,EACJA,cAAc,CAACC,KAAK,IACpBD,cAAc,CAACE,UAAU,IACzBF,cAAc,CAACG,SAAS,IACxBH,cAAc,CAACI,SAAS,IACxBJ,cAAc,CAACK,IAAI,IACnBL,cAAc,CAACM,QAAQ,IACvBN,cAAc,CAACO,OAAO,IACtBP,cAAc,CAACQ,KAAK,IACpBR,cAAc,CAACK,IAAI,CACtB;IACL,CAAC;;IAED;IACA,MAAM;MACFjF,YAAY;MACZC,aAAa;MACbtB,uBAAuB;MAEvB;MACAvC,aAAa;MACbC,gBAAgB;MAChBC,UAAU;MACVC,aAAa;MACbC,SAAS;MACTC,YAAY;MACZC,WAAW;MACXC,cAAc;MACdC,GAAG;MACHC,MAAM;MACNC,aAAa;MACbC,gBAAgB;MAChBC,aAAa;MACbC,gBAAgB;MAChBE,MAAM;MACNC,SAAS;MACTC,aAAa;MACbC,gBAAgB;MAChBC,mBAAmB;MACnBC,sBAAsB;MACtBC,YAAY;MACZC,eAAe;MACfC,YAAY;MACZC,eAAe;MACfC,kBAAkB;MAClBC,qBAAqB;MACrBC,YAAY;MACZC,eAAe;MACfC,WAAW;MACXC,cAAc;MACdC,SAAS;MACTC,YAAY;MAEZ;MACA2B,WAAW;MACXT,WAAW;MACXO;IACJ,CAAC,GAAGjE,yFAAwB,CAAC;MACzBC,QAAQ;MACRC,OAAO;MACPC,OAAO;MACPE,WAAW,EAAE,yBAAyB;MACtCC,eAAe,EAAE,eAAe;MAChCC,eAAe,EAAEwI,oBAAoB,CAAC;IAC1C,CAAC,CAAC;;IAEF;IACA,MAAMU,UAAU,GAAGpC,0DAAS,CAACuB,MAAM,IAAI;MACnC,MAAMC,KAAK,GAAGD,MAAM,CAAC,eAAe,CAAC;MACrC,IAAIC,KAAK,IAAIA,KAAK,CAACa,aAAa,EAAE;QAC9B,OAAOb,KAAK,CAACa,aAAa,CAAC,CAAC;MAChC;MACA,OAAO,IAAI;IACf,CAAC,EAAE,EAAE,CAAC;IAEN9J,6DAAS,CAAC,MAAM;MACZ;MACA,MAAM+J,gBAAgB,GAAG3E,QAAQ,CAAC4E,aAAa,CAC3C,+FACJ,CAAC;MAED,IAAID,gBAAgB,EAAE;QAClB;QACAA,gBAAgB,CAACvE,KAAK,CAACyE,OAAO,GAAG,MAAM;MAC3C;;MAEA;MACA,OAAO,MAAM;QACT,IAAIF,gBAAgB,EAAE;UAClB;UACAA,gBAAgB,CAACvE,KAAK,CAACyE,OAAO,GAAG,EAAE;QACvC;MACJ,CAAC;IACL,CAAC,EAAE,EAAE,CAAC;;IAEN;IACA,MAAMC,UAAU,GAAGL,UAAU,EAAEM,WAAW,GAAGC,UAAU,CAACP,UAAU,CAACM,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;IACzF,MAAME,QAAQ,GAAGR,UAAU,EAAES,aAAa,IAAIjK,QAAQ,CAACgK,QAAQ,IAAI,KAAK;IAExE,MAAM;MAAEE;IAAe,CAAC,GAAGzB,iBAAiB;IAC5C,MAAM;MAAE0B,aAAa;MAAEC;IAAe,CAAC,GAAG1B,YAAY;;IAEtD;IACA,MAAM2B,cAAc,GAAGC,MAAM,IAAI;MAC7B,MAAMC,WAAW,GAAGD,MAAM,EAAEE,WAAW,CAAC,CAAC,IAAI,EAAE;MAC/C,QAAQD,WAAW;QACf,KAAK,WAAW;QAChB,KAAK,SAAS;QACd,KAAK,YAAY;UACb,OAAO;YAAEE,UAAU,EAAE,SAAS;YAAEC,IAAI,EAAE;UAAU,CAAC;QACrD,KAAK,YAAY;QACjB,KAAK,SAAS;QACd,KAAK,SAAS;UACV,OAAO;YAAED,UAAU,EAAE,SAAS;YAAEC,IAAI,EAAE;UAAU,CAAC;QACrD,KAAK,QAAQ;QACb,KAAK,WAAW;QAChB,KAAK,UAAU;UACX,OAAO;YAAED,UAAU,EAAE,SAAS;YAAEC,IAAI,EAAE;UAAU,CAAC;QACrD;UACI,OAAO;YAAED,UAAU,EAAE,SAAS;YAAEC,IAAI,EAAE;UAAU,CAAC;MACzD;IACJ,CAAC;;IAED;IACA/K,6DAAS,CAAC,MAAM;MACZiE,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;MAClF8G,gCAAgC,CAAC,CAAC;IACtC,CAAC,EAAE,EAAE,CAAC;;IAEN;IACAhL,6DAAS,CAAC,MAAM;MACZ,OAAO,MAAM;QACT,IAAImC,YAAY,EAAE;UACd6B,aAAa,CAAC7B,YAAY,CAAC;QAC/B;MACJ,CAAC;IACL,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;IAElB;IACAnC,6DAAS,CAAC,MAAM;MACZ,MAAMiL,KAAK,GAAGC,WAAW,CAAC,MAAM;QAC5BtI,YAAY,CAACuI,IAAI,IAAI;UACjB,IAAIA,IAAI,IAAI,CAAC,EAAE;YACXnH,aAAa,CAACiH,KAAK,CAAC;YACpB,OAAO,CAAC;UACZ;UACA,OAAOE,IAAI,GAAG,CAAC;QACnB,CAAC,CAAC;MACN,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMnH,aAAa,CAACiH,KAAK,CAAC;IACrC,CAAC,EAAE,EAAE,CAAC;;IAEN;IACA,MAAMD,gCAAgC,GAAG,MAAAA,CAAA,KAAY;MACjD,IAAI,CAAC3K,QAAQ,CAAC+K,iBAAiB,EAAE;QAC7BnH,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;QACzE;MACJ;MAEA,IAAI;QACA,MAAMmH,QAAQ,GAAG,MAAMC,KAAK,CAACjL,QAAQ,CAACkL,QAAQ,IAAI,0BAA0B,EAAE;UAC1EC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACL,cAAc,EAAE;UACpB,CAAC;UACD/F,IAAI,EAAE,IAAIgG,eAAe,CAAC;YACtBC,MAAM,EAAE,gCAAgC;YACxCC,KAAK,EAAEvL,QAAQ,CAACuL;UACpB,CAAC;QACL,CAAC,CAAC;QAEF,MAAMnF,MAAM,GAAG,MAAM4E,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAEpC,IAAIpF,MAAM,CAACqF,OAAO,IAAIrF,MAAM,CAACsF,IAAI,CAACC,SAAS,EAAE;UACzC1J,qBAAqB,CAAC,IAAI,CAAC;UAC3B;UACA,IAAImE,MAAM,CAACsF,IAAI,CAACE,YAAY,EAAE;YAC1BvJ,cAAc,CAAC+D,MAAM,CAACsF,IAAI,CAACE,YAAY,CAAC;UAC5C;QACJ,CAAC,MAAM;UACHhI,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEuC,MAAM,CAAC;QACpE;MACJ,CAAC,CAAC,OAAO4B,KAAK,EAAE;QACZpE,OAAO,CAACoE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACzE;IACJ,CAAC;;IAED;IACA,MAAM6D,iCAAiC,GAAG,MAAAA,CAAA,KAAY;MAClDhK,eAAe,CAAC,IAAI,CAAC;MACrBJ,gBAAgB,CAAC,YAAY,CAAC;MAC9BF,SAAS,CAAC,CAAC,CAAC,CAAC,EAAC;MACdY,eAAe,CAAC,IAAI,CAAC,EAAC;;MAEtB,IAAI;QACA,MAAM6I,QAAQ,GAAG,MAAMC,KAAK,CAACjL,QAAQ,CAACkL,QAAQ,IAAI,0BAA0B,EAAE;UAC1EC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACL,cAAc,EAAE;UACpB,CAAC;UACD/F,IAAI,EAAE,IAAIgG,eAAe,CAAC;YACtBC,MAAM,EAAE,+CAA+C;YACvDC,KAAK,EAAEvL,QAAQ,CAACuL,KAAK;YACrBO,QAAQ,EAAE5L,OAAO;YACjB6L,qBAAqB,EAAE3J,WAAW,EAAE4J;UACxC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM5F,MAAM,GAAG,MAAM4E,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAEpC,IAAIpF,MAAM,CAACqF,OAAO,EAAE;UAChB9J,sBAAsB,CAAC;YACnBmK,QAAQ,EAAE1F,MAAM,CAACsF,IAAI,CAACI,QAAQ;YAC9B9B,QAAQ,EAAEA,QAAQ;YAClBiC,MAAM,EAAEpC,UAAU,CAACqC,OAAO,CAAC,CAAC,CAAC;YAC7B5B,MAAM,EAAE,YAAY;YACpB6B,mBAAmB,EAAE/F,MAAM,CAACsF,IAAI,CAACI,QAAQ;YACzCM,OAAO,EAAE;UACb,CAAC,CAAC;UACF3K,gBAAgB,CAAC,cAAc,CAAC;UAChC4K,yBAAyB,CAACjG,MAAM,CAACsF,IAAI,CAACI,QAAQ,CAAC;QACnD,CAAC,MAAM;UACH,MAAMQ,YAAY,GAAGlG,MAAM,CAACsF,IAAI,EAAEU,OAAO,IAAI,yBAAyB;UACtE,MAAMG,SAAS,GAAGnG,MAAM,CAACsF,IAAI,EAAEc,UAAU,IAAI,eAAe;UAC5D,MAAMC,SAAS,GAAGrG,MAAM,CAACsF,IAAI,EAAEgB,UAAU,IAAI,wBAAwB;;UAErE;UACA,IAAIH,SAAS,KAAK,gCAAgC,IAAIE,SAAS,KAAK,uBAAuB,EAAE;YACzFhL,gBAAgB,CAAC,0BAA0B,CAAC;YAC5CF,SAAS,CAAC;cAAEoL,OAAO,EAAEL;YAAa,CAAC,CAAC;YACpCnK,eAAe,CAAC;cACZiK,OAAO,EAAEE,YAAY;cACrBM,IAAI,EAAE,0BAA0B;cAChClF,IAAI,EAAE,uBAAuB;cAC7BmF,OAAO,EAAE,kBAAkB;cAC3BC,sBAAsB,EAAE,IAAI;cAC5BC,YAAY,EAAE3G,MAAM,CAACsF,IAAI,EAAEpK,MAAM,IAAI,EAAE;cACvC0L,aAAa,EAAE5G,MAAM,CAACsF,IAAI,EAAEsB;YAChC,CAAC,CAAC;UACN,CAAC,MAAM;YACHvL,gBAAgB,CAAC,QAAQ,CAAC;YAC1BF,SAAS,CAAC;cAAEoL,OAAO,EAAEL;YAAa,CAAC,CAAC;YACpCnK,eAAe,CAAC;cACZiK,OAAO,EAAEE,YAAY;cACrBM,IAAI,EAAEL,SAAS;cACf7E,IAAI,EAAE+E,SAAS;cACfI,OAAO,EAAE,kBAAkB;cAC3BI,OAAO,EAAE7G,MAAM,CAACsF,IAAI,EAAEwB,aAAa,IAAI;YAC3C,CAAC,CAAC;UACN;QACJ;MACJ,CAAC,CAAC,OAAOlF,KAAK,EAAE;QACZvG,gBAAgB,CAAC,QAAQ,CAAC;QAC1BF,SAAS,CAAC;UAAEoL,OAAO,EAAE;QAAiD,CAAC,CAAC;QACxExK,eAAe,CAAC;UACZiK,OAAO,EAAE,gDAAgD;UACzDQ,IAAI,EAAE,eAAe;UACrBlF,IAAI,EAAE,kBAAkB;UACxBmF,OAAO,EAAE,kBAAkB;UAC3BI,OAAO,EAAEjF,KAAK,CAACoE;QACnB,CAAC,CAAC;MACN,CAAC,SAAS;QACNvK,eAAe,CAAC,KAAK,CAAC;MAC1B;IACJ,CAAC;;IAED;;IAEA;IACA,MAAMsL,aAAa,GAAGC,KAAK,IAAI;MAC3B,IAAI,CAACA,KAAK,CAACC,IAAI,CAAC,CAAC,EAAE;QACfzM,YAAY,CAAC,EAAE,CAAC;QAChBW,SAAS,CAACuJ,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAErK,UAAU,EAAE;QAAG,CAAC,CAAC,CAAC;QAChD;MACJ;;MAEA;MACA,MAAM6M,YAAY,GAAG,4BAA4B;MACjD;MACA,MAAMC,YAAY,GAAG,oBAAoB;MAEzC,IAAID,YAAY,CAACE,IAAI,CAACJ,KAAK,CAAC,EAAE;QAC1BxM,YAAY,CAAC,OAAO,CAAC;QACrBW,SAAS,CAACuJ,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAErK,UAAU,EAAE;QAAG,CAAC,CAAC,CAAC;MACpD,CAAC,MAAM,IAAI8M,YAAY,CAACC,IAAI,CAACJ,KAAK,CAAC,EAAE;QACjCxM,YAAY,CAAC,aAAa,CAAC;QAC3BW,SAAS,CAACuJ,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAErK,UAAU,EAAE;QAAG,CAAC,CAAC,CAAC;MACpD,CAAC,MAAM;QACHG,YAAY,CAAC,EAAE,CAAC;QAChBW,SAAS,CAACuJ,IAAI,KAAK;UACf,GAAGA,IAAI;UACPrK,UAAU,EAAE0G,mDAAE,CACV,qDAAqD,EACrD,kCACJ;QACJ,CAAC,CAAC,CAAC;MACP;IACJ,CAAC;;IAED;IACA,MAAMsG,YAAY,GAAGA,CAAA,KAAM;MACvB,MAAMC,SAAS,GAAG,CAAC,CAAC;MAEpB,IAAInN,aAAa,KAAK,OAAO,EAAE;QAC3B,IAAI,CAACE,UAAU,CAAC4M,IAAI,CAAC,CAAC,EAAE;UACpBK,SAAS,CAACjN,UAAU,GAAG0G,mDAAE,CAAC,oBAAoB,EAAE,kCAAkC,CAAC;QACvF,CAAC,MAAM,IAAI,CAACxG,SAAS,EAAE;UACnB+M,SAAS,CAACjN,UAAU,GAAG0G,mDAAE,CACrB,qDAAqD,EACrD,kCACJ,CAAC;QACL;MACJ,CAAC,MAAM;QACH,IAAI,CAACtG,WAAW,CAACwM,IAAI,CAAC,CAAC,EAAE;UACrBK,SAAS,CAAC7M,WAAW,GAAGsG,mDAAE,CAAC,2BAA2B,EAAE,kCAAkC,CAAC;QAC/F;QACA,IAAI,CAACpG,GAAG,CAACsM,IAAI,CAAC,CAAC,EAAE;UACbK,SAAS,CAAC3M,GAAG,GAAGoG,mDAAE,CAAC,kBAAkB,EAAE,kCAAkC,CAAC;QAC9E,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAACqG,IAAI,CAACzM,GAAG,CAACsM,IAAI,CAAC,CAAC,CAAC,EAAE;UAC3CK,SAAS,CAAC3M,GAAG,GAAGoG,mDAAE,CAAC,sCAAsC,EAAE,kCAAkC,CAAC;QAClG;QACA,IAAI,CAAClG,aAAa,CAACoM,IAAI,CAAC,CAAC,EAAE;UACvBK,SAAS,CAACzM,aAAa,GAAGkG,mDAAE,CAAC,6BAA6B,EAAE,kCAAkC,CAAC;QACnG;MACJ;;MAEA;MACA,MAAMwG,gBAAgB,GAAG5D,UAAU,CAAC5I,aAAa,CAAC;MAClD,IAAI,CAACA,aAAa,IAAIA,aAAa,KAAK,EAAE,IAAIyM,KAAK,CAACD,gBAAgB,CAAC,IAAIA,gBAAgB,IAAI,CAAC,EAAE;QAC5FD,SAAS,CAACvM,aAAa,GAAGgG,mDAAE,CACxB,wCAAwC,EACxC,kCACJ,CAAC;MACL;MAEA5F,SAAS,CAACmM,SAAS,CAAC;MACpB,OAAO7F,MAAM,CAACC,IAAI,CAAC4F,SAAS,CAAC,CAACzF,MAAM,KAAK,CAAC;IAC9C,CAAC;;IAED;IACA4F,KAAK,CAAClO,SAAS,CAAC,MAAM;MAClB,MAAMmO,WAAW,GAAG5D,cAAc,CAAC,MAAM;QACrC,IAAI,CAACuD,YAAY,CAAC,CAAC,EAAE;UACjB,OAAO;YACH/F,IAAI,EAAEyC,aAAa,CAAC4D,KAAK;YACzB3B,OAAO,EAAEjF,mDAAE,CAAC,8CAA8C,EAAE,kCAAkC,CAAC;YAC/F6G,cAAc,EAAE5D,cAAc,CAAC6D;UACnC,CAAC;QACL;;QAEA;QACA,OAAO;UACHvG,IAAI,EAAEyC,aAAa,CAAC+D,OAAO;UAC3BC,IAAI,EAAE;YACFC,iBAAiB,EAAE;cACfC,oBAAoB,EAAE9N,aAAa;cACnC+N,gBAAgB,EAAE/N,aAAa,KAAK,OAAO,GAAGI,SAAS,GAAG,EAAE;cAC5D4N,iBAAiB,EAAEhO,aAAa,KAAK,OAAO,GAAGE,UAAU,GAAG,EAAE;cAC9D+N,kBAAkB,EAAEjO,aAAa,KAAK,aAAa,GAAGM,WAAW,GAAG,EAAE;cACtE4N,SAAS,EAAElO,aAAa,KAAK,aAAa,GAAGQ,GAAG,GAAG,EAAE;cACrD2N,oBAAoB,EAAEnO,aAAa,KAAK,aAAa,GAAGU,aAAa,GAAG,EAAE;cAC1E0N,oBAAoB,EAAExN,aAAa,CAACyN,QAAQ,CAAC;YACjD;UACJ;QACJ,CAAC;MACL,CAAC,CAAC;MAEF,OAAOd,WAAW;IACtB,CAAC,EAAE,CACC5D,cAAc,EACd3J,aAAa,EACbI,SAAS,EACTF,UAAU,EACVI,WAAW,EACXE,GAAG,EACHE,aAAa,EACbE,aAAa,EACbsM,YAAY,CACf,CAAC;;IAEF;IACA,MAAMoB,iBAAiB,GAAG,MAAMC,KAAK,IAAI;MACrC,IAAIA,KAAK,EAAE;QACPA,KAAK,CAACC,cAAc,CAAC,CAAC;QACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;MAC3B;MAEA,IAAI,CAACvB,YAAY,CAAC,CAAC,EAAE;QACjB;MACJ;MAEA5L,eAAe,CAAC,IAAI,CAAC;MACrBJ,gBAAgB,CAAC,YAAY,CAAC;MAC9BF,SAAS,CAAC,CAAC,CAAC,CAAC,EAAC;MACdY,eAAe,CAAC,IAAI,CAAC,EAAC;;MAEtB;MACAE,cAAc,CAAC,IAAI,CAAC;MACpBJ,qBAAqB,CAAC,KAAK,CAAC;MAE5B,IAAI;QACA;QACA,IAAIgN,WAAW,GAAGhP,OAAO,EAAE8I,cAAc,IAAI,CAAC,CAAC;QAC/C,MAAMiC,QAAQ,GAAG,MAAMC,KAAK,CAACjL,QAAQ,CAACkL,QAAQ,IAAI,0BAA0B,EAAE;UAC1EC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACL,cAAc,EAAE;UACpB,CAAC;UACD/F,IAAI,EAAE,IAAIgG,eAAe,CAAC;YACtBC,MAAM,EAAE,gCAAgC;YACxCC,KAAK,EAAEvL,QAAQ,CAACuL,KAAK;YACrB8C,oBAAoB,EAAE9N,aAAa;YACnC+N,gBAAgB,EAAE/N,aAAa,KAAK,OAAO,GAAGI,SAAS,GAAG,EAAE;YAC5D4N,iBAAiB,EAAEhO,aAAa,KAAK,OAAO,GAAGE,UAAU,GAAG,EAAE;YAC9D+N,kBAAkB,EAAEjO,aAAa,KAAK,aAAa,GAAGM,WAAW,GAAG,EAAE;YACtE4N,SAAS,EAAElO,aAAa,KAAK,aAAa,GAAGQ,GAAG,GAAG,EAAE;YACrD2N,oBAAoB,EAAEnO,aAAa,KAAK,aAAa,GAAGU,aAAa,GAAG,EAAE;YAC1E0N,oBAAoB,EAAExN,aAAa,CAACyN,QAAQ,CAAC,CAAC;YAC9CM,kBAAkB,EAAED,WAAW,CAAChG,UAAU,IAAI,EAAE;YAChDkG,iBAAiB,EAAEF,WAAW,CAAC/F,SAAS,IAAI,EAAE;YAC9CkG,aAAa,EAAEH,WAAW,CAACjG,KAAK,IAAI,EAAE;YACtC8C,QAAQ,EAAE5L,OAAO;YACjBmP,cAAc,EAAE;UACpB,CAAC;QACL,CAAC,CAAC;QAEF,MAAMjJ,MAAM,GAAG,MAAM4E,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAEpC,IAAIpF,MAAM,CAACqF,OAAO,EAAE;UAChB9J,sBAAsB,CAAC;YACnBmK,QAAQ,EAAE1F,MAAM,CAACsF,IAAI,CAACI,QAAQ;YAC9BwD,SAAS,EAAElJ,MAAM,CAACsF,IAAI,CAAC4D,SAAS;YAChCC,kBAAkB,EAAEnJ,MAAM,CAACsF,IAAI,CAAC6D,kBAAkB;YAClDvF,QAAQ,EAAEA,QAAQ;YAClBiC,MAAM,EAAEpC,UAAU,CAACqC,OAAO,CAAC,CAAC,CAAC;YAC7B5B,MAAM,EAAE,YAAY;YACpB6B,mBAAmB,EAAE/F,MAAM,CAACsF,IAAI,CAACI,QAAQ;YACzCM,OAAO,EAAE;UACb,CAAC,CAAC;UACF3K,gBAAgB,CAAC,cAAc,CAAC;UAChC4K,yBAAyB,CAACjG,MAAM,CAACsF,IAAI,CAACI,QAAQ,CAAC;QACnD,CAAC,MAAM;UACH,MAAMQ,YAAY,GAAGlG,MAAM,CAACsF,IAAI,EAAEU,OAAO,IAAI,oCAAoC;UACjF,MAAMG,SAAS,GAAGnG,MAAM,CAACsF,IAAI,EAAEc,UAAU,IAAI,eAAe;UAC5D,MAAMC,SAAS,GAAGrG,MAAM,CAACsF,IAAI,EAAEgB,UAAU,IAAI,0BAA0B;;UAEvE;UACAjL,gBAAgB,CAAC,QAAQ,CAAC;UAC1BF,SAAS,CAAC;YAAEoL,OAAO,EAAEL;UAAa,CAAC,CAAC;UACpCnK,eAAe,CAAC;YACZiK,OAAO,EAAEE,YAAY;YACrBM,IAAI,EAAEL,SAAS;YACf7E,IAAI,EAAE+E,SAAS;YACfI,OAAO,EAAE,oBAAoB;YAC7BI,OAAO,EAAE7G,MAAM,CAACsF,IAAI,EAAEwB,aAAa,IAAI,IAAI;YAC3CsC,iBAAiB,EAAEpJ,MAAM,CAACsF,IAAI,EAAE8D,iBAAiB,IAAI;UACzD,CAAC,CAAC;QACN;MACJ,CAAC,CAAC,OAAOxH,KAAK,EAAE;QACZvG,gBAAgB,CAAC,QAAQ,CAAC;QAC1BF,SAAS,CAAC;UAAEoL,OAAO,EAAE;QAA0D,CAAC,CAAC;QACjFxK,eAAe,CAAC;UACZiK,OAAO,EAAE,yDAAyD;UAClEQ,IAAI,EAAE,eAAe;UACrBlF,IAAI,EAAE,kBAAkB;UACxBmF,OAAO,EAAE,oBAAoB;UAC7BI,OAAO,EAAEjF,KAAK,CAACoE;QACnB,CAAC,CAAC;MACN,CAAC,SAAS;QACNvK,eAAe,CAAC,KAAK,CAAC;MAC1B;IACJ,CAAC;;IAED;IACA,MAAM4N,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACtC5N,eAAe,CAAC,IAAI,CAAC;MACrBN,SAAS,CAAC,CAAC,CAAC,CAAC;MACbY,eAAe,CAAC,IAAI,CAAC;MAErB,IAAI;QACA,MAAM8M,WAAW,GAAGhP,OAAO,EAAE8I,cAAc,IAAI,CAAC,CAAC;QACjD,MAAMiC,QAAQ,GAAG,MAAMC,KAAK,CAACjL,QAAQ,CAACkL,QAAQ,IAAI,0BAA0B,EAAE;UAC1EC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACL,cAAc,EAAE;UACpB,CAAC;UACD/F,IAAI,EAAE,IAAIgG,eAAe,CAAC;YACtBC,MAAM,EAAE,+BAA+B;YACvCC,KAAK,EAAEvL,QAAQ,CAACuL,KAAK;YACrBO,QAAQ,EAAEpK,mBAAmB,EAAEoK,QAAQ,IAAI5L,OAAO;YAClDwP,cAAc,EAAET,WAAW,CAACjG,KAAK,IAAI;UACzC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM5C,MAAM,GAAG,MAAM4E,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAEpC,IAAIpF,MAAM,CAACqF,OAAO,EAAE;UAChB;UACA9J,sBAAsB,CAAC,IAAI,CAAC;UAC5BF,gBAAgB,CAAC,MAAM,CAAC;UACxBF,SAAS,CAAC,CAAC,CAAC,CAAC;UACbY,eAAe,CAAC,IAAI,CAAC;;UAErB;UACA3B,gBAAgB,CAAC,OAAO,CAAC;UACzBI,YAAY,CAAC,OAAO,CAAC;UACrBF,aAAa,CAAC,EAAE,CAAC;UACjBI,cAAc,CAAC,EAAE,CAAC;UAClBE,MAAM,CAAC,EAAE,CAAC;UACVE,gBAAgB,CAAC,EAAE,CAAC;UACpBE,gBAAgB,CAACyI,UAAU,CAAC;QAChC,CAAC,MAAM;UACHtI,SAAS,CAAC;YAAEoL,OAAO,EAAEvG,MAAM,CAACsF,IAAI,EAAEU,OAAO,IAAI;UAAoC,CAAC,CAAC;QACvF;MACJ,CAAC,CAAC,OAAOpE,KAAK,EAAE;QACZzG,SAAS,CAAC;UAAEoL,OAAO,EAAE;QAA2D,CAAC,CAAC;MACtF,CAAC,SAAS;QACN9K,eAAe,CAAC,KAAK,CAAC;MAC1B;IACJ,CAAC;;IAED;IACA,MAAMwK,yBAAyB,GAAGnM,OAAO,IAAI;MACzC,MAAMyP,QAAQ,GAAG9E,WAAW,CAAC,YAAY;QACrC,IAAI;UACA,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAACjL,QAAQ,CAACkL,QAAQ,IAAI,0BAA0B,EAAE;YAC1EC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACL,cAAc,EAAE;YACpB,CAAC;YACD/F,IAAI,EAAE,IAAIgG,eAAe,CAAC;cACtBC,MAAM,EAAE,+CAA+C;cACvDC,KAAK,EAAEvL,QAAQ,CAACuL,KAAK;cACrBO,QAAQ,EAAE5L;YACd,CAAC;UACL,CAAC,CAAC;UAEF,MAAMkG,MAAM,GAAG,MAAM4E,QAAQ,CAACQ,IAAI,CAAC,CAAC;UAEpC,IAAIpF,MAAM,CAACqF,OAAO,EAAE;YAChB,MAAMmE,eAAe,GAAGxJ,MAAM,CAACsF,IAAI,CAACmE,gBAAgB,IAAIzJ,MAAM,CAACsF,IAAI,CAACoE,cAAc;YAClF,MAAMtO,aAAa,GAAG4E,MAAM,CAACsF,IAAI,CAACqE,yBAAyB;YAC3D,MAAMC,WAAW,GAAG5J,MAAM,CAACsF,IAAI,CAACuE,YAAY;YAC5C,MAAM/N,YAAY,GAAGkE,MAAM,CAACsF,IAAI,CAACwB,aAAa;YAE9CtJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;cACjC+L,eAAe;cACfpO,aAAa;cACbwO,WAAW;cACX9N;YACJ,CAAC,CAAC;;YAEF;YACA,IAAIA,YAAY,IAAIA,YAAY,CAAC4K,sBAAsB,EAAE;cACrDnJ,aAAa,CAACgM,QAAQ,CAAC;cACvB5N,eAAe,CAAC,IAAI,CAAC;cACrBN,gBAAgB,CAAC,0BAA0B,CAAC;cAE5C,MAAMyO,WAAW,GAAGhO,YAAY,CAACZ,MAAM,IAAI,EAAE;cAC7C,MAAM6O,aAAa,GAAGD,WAAW,CAACE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACjE,OAAO,CAAC,CAACkE,IAAI,CAAC,IAAI,CAAC;cAEpE/O,SAAS,CAAC;gBAAEoL,OAAO,EAAEwD;cAAc,CAAC,CAAC;cACrChO,eAAe,CAAC;gBACZiK,OAAO,EAAE+D,aAAa;gBACtBvD,IAAI,EAAE,0BAA0B;gBAChClF,IAAI,EAAE,uBAAuB;gBAC7BmF,OAAO,EAAE,oBAAoB;gBAC7BG,aAAa,EAAE9K,YAAY,CAAC8K,aAAa;gBACzCF,sBAAsB,EAAE,IAAI;gBAC5BC,YAAY,EAAEmD;cAClB,CAAC,CAAC;cACF;YACJ;;YAEA;YACA,IAAIxO,mBAAmB,EAAE;cACrBC,sBAAsB,CAACmJ,IAAI,KAAK;gBAC5B,GAAGA,IAAI;gBACPR,MAAM,EAAEsF,eAAe,IAAIpO,aAAa,IAAI,YAAY;gBACxDqO,gBAAgB,EAAED,eAAe;gBACjCG,yBAAyB,EAAEvO,aAAa;gBACxCyO,YAAY,EAAED,WAAW;gBACzBV,SAAS,EAAElJ,MAAM,CAACsF,IAAI,CAAC4D,SAAS,IAAIxE,IAAI,CAACwE,SAAS;gBAClDC,kBAAkB,EAAEnJ,MAAM,CAACsF,IAAI,CAAC6D,kBAAkB,IAAIzE,IAAI,CAACyE;cAC/D,CAAC,CAAC,CAAC;YACP;;YAEA;YACA,IACI/N,aAAa,KAAK,WAAW,IAC7BA,aAAa,KAAK,SAAS,IAC3BwO,WAAW,KAAK,WAAW,IAC3BA,WAAW,KAAK,YAAY,EAC9B;cACErM,aAAa,CAACgM,QAAQ,CAAC;cACvB5N,eAAe,CAAC,IAAI,CAAC;cACrBN,gBAAgB,CAAC,SAAS,CAAC;;cAE3B;cACA8O,UAAU,CAAC,MAAM;gBACb,MAAMC,gBAAgB,GAClBpK,MAAM,CAACsF,IAAI,CAAC6D,kBAAkB,IAAI7N,mBAAmB,EAAE6N,kBAAkB;gBAC7E,IAAIiB,gBAAgB,EAAE;kBAClB3J,MAAM,CAAC4J,QAAQ,CAACC,IAAI,GAAGF,gBAAgB;gBAC3C;cACJ,CAAC,EAAE,IAAI,CAAC,EAAC;YACb,CAAC,MAAM,IACHhP,aAAa,KAAK,QAAQ,IAC1BA,aAAa,KAAK,WAAW,IAC7BoO,eAAe,KAAK,QAAQ,IAC5BA,eAAe,KAAK,WAAW,IAC/BA,eAAe,KAAK,UAAU,EAChC;cACEjM,aAAa,CAACgM,QAAQ,CAAC;cACvB5N,eAAe,CAAC,IAAI,CAAC;;cAErB;cACA,IACIG,YAAY,EAAE0K,IAAI,KAAK,gCAAgC,IACvD1K,YAAY,EAAEwF,IAAI,KAAK,uBAAuB,IAC9CtB,MAAM,CAACsF,IAAI,CAACc,UAAU,KAAK,gCAAgC,EAC7D;gBACE/K,gBAAgB,CAAC,0BAA0B,CAAC;gBAC5CF,SAAS,CAAC;kBACNoL,OAAO,EACHzK,YAAY,EAAEkK,OAAO,IACrBhG,MAAM,CAACsF,IAAI,CAACU,OAAO,IACnB;gBACR,CAAC,CAAC;gBACFjK,eAAe,CAAC;kBACZiK,OAAO,EACHlK,YAAY,EAAEkK,OAAO,IACrBhG,MAAM,CAACsF,IAAI,CAACU,OAAO,IACnB,6CAA6C;kBACjDQ,IAAI,EAAE,0BAA0B;kBAChClF,IAAI,EAAE,uBAAuB;kBAC7BmF,OAAO,EAAE,iBAAiB;kBAC1BC,sBAAsB,EAAE,IAAI;kBAC5BC,YAAY,EAAE3G,MAAM,CAACsF,IAAI,EAAEpK,MAAM,IAAIY,YAAY,EAAE6K,YAAY,IAAI,EAAE;kBACrE8C,gBAAgB,EAAED,eAAe;kBACjCe,cAAc,EAAEnP,aAAa;kBAC7ByO,YAAY,EAAED;gBAClB,CAAC,CAAC;cACN,CAAC,MAAM;gBACHvO,gBAAgB,CAAC,QAAQ,CAAC;;gBAE1B;gBACA,IAAI6K,YAAY,GAAG,iCAAiC;gBACpD,IAAIC,SAAS,GAAG,eAAe;gBAC/B,IAAIE,SAAS,GAAG,gBAAgB;gBAEhC,IAAImD,eAAe,KAAK,UAAU,EAAE;kBAChCtD,YAAY,GAAG,2CAA2C;kBAC1DC,SAAS,GAAG,oBAAoB;kBAChCE,SAAS,GAAG,iBAAiB;gBACjC,CAAC,MAAM,IAAImD,eAAe,KAAK,WAAW,EAAE;kBACxCtD,YAAY,GAAG,+BAA+B;kBAC9CC,SAAS,GAAG,qBAAqB;kBACjCE,SAAS,GAAG,iBAAiB;gBACjC,CAAC,MAAM,IAAImD,eAAe,KAAK,QAAQ,EAAE;kBACrCtD,YAAY,GAAG,iCAAiC;kBAChDC,SAAS,GAAG,kBAAkB;kBAC9BE,SAAS,GAAG,iBAAiB;gBACjC,CAAC,MAAM,IAAIjL,aAAa,KAAK,QAAQ,EAAE;kBACnC8K,YAAY,GAAG,2BAA2B;kBAC1CC,SAAS,GAAG,gBAAgB;kBAC5BE,SAAS,GAAG,eAAe;gBAC/B,CAAC,MAAM,IAAIjL,aAAa,KAAK,WAAW,EAAE;kBACtC8K,YAAY,GAAG,uBAAuB;kBACtCC,SAAS,GAAG,mBAAmB;kBAC/BE,SAAS,GAAG,eAAe;gBAC/B;gBAEAlL,SAAS,CAAC;kBAAEoL,OAAO,EAAEL;gBAAa,CAAC,CAAC;gBACpCnK,eAAe,CAAC;kBACZiK,OAAO,EAAEE,YAAY;kBACrBM,IAAI,EAAEL,SAAS;kBACf7E,IAAI,EAAE+E,SAAS;kBACfI,OAAO,EAAE,iBAAiB;kBAC1BgD,gBAAgB,EAAED,eAAe;kBACjCe,cAAc,EAAEnP,aAAa;kBAC7ByO,YAAY,EAAED;gBAClB,CAAC,CAAC;cACN;YACJ;YACA;UACJ;QACJ,CAAC,CAAC,OAAOhI,KAAK,EAAE;UACZpE,OAAO,CAACoE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QAC1D;MACJ,CAAC,EAAE,IAAI,CAAC,EAAC;;MAETjG,eAAe,CAAC4N,QAAQ,CAAC;IAC7B,CAAC;;IAED;IACA,MAAMiB,yBAAyB,GAAGA,CAAA,KAAM;MACpC,IAAI,CAAClP,mBAAmB,EAAE,OAAO,IAAI;MAErC,oBACImM,KAAA,CAAA7I,aAAA;QAAKE,SAAS,EAAC;MAAqF,gBAEhG2I,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0L,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE;QACf;MAAE,gBACFjD,KAAA,CAAA7I,aAAA;QAAKE,SAAS,EAAC;MAAkB,gBAC7B2I,KAAA,CAAA7I,aAAA;QAAKE,SAAS,EAAC;MAAW,GAAC,KAEtB,CAAC,eACN2I,KAAA,CAAA7I,aAAA;QAAK+L,KAAK,EAAC;MAAQ,gBACflD,KAAA,CAAA7I,aAAA;QAAM+L,KAAK,EAAC;MAAiC,gBACzClD,KAAA,CAAA7I,aAAA,2BACI6I,KAAA,CAAA7I,aAAA;QAAM+L,KAAK,EAAC;MAAkC,GAAC,GAAO,CAAC,EACtDrP,mBAAmB,CAACuK,MACpB,CACH,CACL,CACJ,CACJ,CAAC,eAGN4B,KAAA,CAAA7I,aAAA,2BACI6I,KAAA,CAAA7I,aAAA;QAAKE,SAAS,EAAC;MAAsC,gBACjD2I,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAE6L,UAAU,EAAE;QAAI;MAAE,GAAC,UAE5B,CAAC,eAENnD,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEyE,OAAO,EAAE,MAAM;UAAEqH,GAAG,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS;MAAE,gBAC/DrD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHyE,OAAO,EAAE,MAAM;UACfsH,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE,SAAS;UACjBF,GAAG,EAAE;QACT;MAAE,gBACFpD,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEiM,QAAQ,EAAE;QAAW;MAAE,gBACjCvD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHkM,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,eACJhR,aAAa,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS,EACnD;UACFiR,YAAY,EAAE,KAAK;UACnBJ,QAAQ,EAAE;QACd;MAAE,GACD7Q,aAAa,KAAK,OAAO,iBACtBsN,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHkM,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdG,eAAe,EAAE,SAAS;UAC1BD,YAAY,EAAE,KAAK;UACnBJ,QAAQ,EAAE,UAAU;UACpBM,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,KAAK;UACXC,SAAS,EAAE;QACf;MAAE,CAAM,CAEf,CACJ,CAAC,eACN/D,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH6L,UAAU,EAAE,KAAK;UACjBa,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,OAAO;UACnBC,KAAK,EAAE;QACX;MAAE,GAAC,OAED,CACH,CAAC,eACRlE,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHyE,OAAO,EAAE,MAAM;UACfsH,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE,SAAS;UACjBF,GAAG,EAAE;QACT;MAAE,gBACFpD,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEiM,QAAQ,EAAE;QAAW;MAAE,gBACjCvD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHkM,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,eACJhR,aAAa,KAAK,aAAa,GAAG,SAAS,GAAG,SAAS,EACzD;UACFiR,YAAY,EAAE,KAAK;UACnBJ,QAAQ,EAAE;QACd;MAAE,GACD7Q,aAAa,KAAK,aAAa,iBAC5BsN,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHkM,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdG,eAAe,EAAE,SAAS;UAC1BD,YAAY,EAAE,KAAK;UACnBJ,QAAQ,EAAE,UAAU;UACpBM,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,KAAK;UACXC,SAAS,EAAE;QACf;MAAE,CAAM,CAEf,CACJ,CAAC,eACN/D,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH6L,UAAU,EAAE,KAAK;UACjBa,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,OAAO;UACnBC,KAAK,EAAE;QACX;MAAE,GAAC,wBAED,CACH,CACN,CACJ,CACJ,CAAC,eAGNlE,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHoM,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBQ,OAAO,EAAE,gBAAgB;UACzBf,GAAG,EAAE,MAAM;UACXrH,OAAO,EAAE,MAAM;UACfqI,aAAa,EAAE,QAAQ;UACvBf,UAAU,EAAE;QAChB;MAAE,GAED3Q,aAAa,KAAK,OAAO,iBACtBsN,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkM,KAAK,EAAE,MAAM;UAAEzH,OAAO,EAAE,MAAM;UAAEqI,aAAa,EAAE;QAAS;MAAE,gBACpEpE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEyE,OAAO,EAAE,MAAM;UAAEsH,UAAU,EAAE;QAAS;MAAE,gBAClDrD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH6L,UAAU,EAAE,KAAK;UACjBa,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,OAAO;UACnBC,KAAK,EAAE;QACX;MAAE,GAAC,kBAEA,CACN,CAAC,eACNlE,KAAA,CAAA7I,aAAA,CAACuC,8DAAW;QACR6F,KAAK,EAAE3M,UAAU,IAAI,EAAG;QACxByR,QAAQ;QACRC,WAAW,EAAC;MAAc,CAC7B,CACA,CACR,EAGA5R,aAAa,KAAK,aAAa,iBAC5BsN,KAAA,CAAA7I,aAAA,CAAA6I,KAAA,CAAAuE,QAAA,qBAEIvE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkM,KAAK,EAAE,MAAM;UAAEzH,OAAO,EAAE,MAAM;UAAEqI,aAAa,EAAE;QAAS;MAAE,gBACpEpE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEyE,OAAO,EAAE,MAAM;UAAEsH,UAAU,EAAE;QAAS;MAAE,gBAClDrD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH6L,UAAU,EAAE,KAAK;UACjBa,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,OAAO;UACnBC,KAAK,EAAE,SAAS;UAChBnI,OAAO,EAAE,OAAO;UAChByI,YAAY,EAAE;QAClB;MAAE,GAAC,mCAEA,CACN,CAAC,eACNxE,KAAA,CAAA7I,aAAA,CAACuC,8DAAW;QACR6F,KAAK,EAAEvM,WAAW,IAAI,EAAG;QACzBqR,QAAQ;QACRC,WAAW,EAAC;MAAiB,CAChC,CACA,CAAC,eAGNtE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkM,KAAK,EAAE,MAAM;UAAEzH,OAAO,EAAE,MAAM;UAAEqI,aAAa,EAAE;QAAS;MAAE,gBACpEpE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEyE,OAAO,EAAE,MAAM;UAAEsH,UAAU,EAAE;QAAS;MAAE,gBAClDrD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH6L,UAAU,EAAE,KAAK;UACjBa,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,OAAO;UACnBC,KAAK,EAAE,MAAM;UACbnI,OAAO,EAAE,OAAO;UAChByI,YAAY,EAAE;QAClB;MAAE,GAAC,KAEA,CACN,CAAC,eACNxE,KAAA,CAAA7I,aAAA,CAACuC,8DAAW;QACR6F,KAAK,EAAErM,GAAG,IAAI,EAAG;QACjBmR,QAAQ;QACRC,WAAW,EAAC;MAAS,CACxB,CACA,CAAC,eAGNtE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkM,KAAK,EAAE,MAAM;UAAEzH,OAAO,EAAE,MAAM;UAAEqI,aAAa,EAAE;QAAS;MAAE,gBACpEpE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEyE,OAAO,EAAE,MAAM;UAAEsH,UAAU,EAAE;QAAS;MAAE,gBAClDrD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH6L,UAAU,EAAE,KAAK;UACjBa,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,OAAO;UACnBC,KAAK,EAAE,MAAM;UACbnI,OAAO,EAAE,OAAO;UAChByI,YAAY,EAAE;QAClB;MAAE,GAAC,gBAEA,CACN,CAAC,eACNxE,KAAA,CAAA7I,aAAA,CAACuC,8DAAW;QACR6F,KAAK,EAAEnM,aAAa,IAAI,EAAG;QAC3BiR,QAAQ;QACRC,WAAW,EAAC;MAA2B,CAC1C,CACA,CACP,CACL,eAGDtE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkM,KAAK,EAAE,MAAM;UAAEzH,OAAO,EAAE,MAAM;UAAEqI,aAAa,EAAE,QAAQ;UAAEhB,GAAG,EAAE;QAAO;MAAE,gBACjFpD,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEyE,OAAO,EAAE,MAAM;UAAEqI,aAAa,EAAE;QAAS;MAAE,gBACrDpE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEyE,OAAO,EAAE,MAAM;UAAEsH,UAAU,EAAE;QAAS;MAAE,gBAClDrD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH6L,UAAU,EAAE,KAAK;UACjBa,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,OAAO;UACnBC,KAAK,EAAE,MAAM;UACbnI,OAAO,EAAE,OAAO;UAChByI,YAAY,EAAE;QAClB;MAAE,GAAC,kCAEA,CACN,CAAC,eAKNxE,KAAA,CAAA7I,aAAA;QAAKE,SAAS,EAAC;MAA0B,gBACrC2I,KAAA,CAAA7I,aAAA;QAAME,SAAS,EAAC;MAAqB,GAAC,GAAO,CAAC,eAC9C2I,KAAA,CAAA7I,aAAA,CAACuC,8DAAW;QACR6F,KAAK,EAAE,GAAGrD,UAAU,CAAC5I,aAAa,CAAC,IAAI,IAAI,EAAG;QAC9C+Q,QAAQ;MAAA,CACX,CACA,CACJ,CAAC,eACNrE,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH6L,UAAU,EAAE,KAAK;UACjBa,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBC,KAAK,EAAE;QACX;MAAE,GAAC,kHAGF,CACJ,CAAC,eAGNlE,KAAA,CAAA7I,aAAA,CAACsC,yDAAM;QACHnC,KAAK,EAAE;UACHkM,KAAK,EAAE,MAAM;UACbzH,OAAO,EAAE,MAAM;UACf0I,cAAc,EAAE,QAAQ;UACxBpB,UAAU,EAAE,QAAQ;UACpBD,GAAG,EAAE,MAAM;UACXQ,eAAe,EAAE,SAAS;UAC1BD,YAAY,EAAE,KAAK;UACnBe,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,GAAG;UACZrB,MAAM,EAAE;QACZ;MAAE,gBACFtD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH6L,UAAU,EAAE,KAAK;UACjBa,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,QAAQ;UACpBC,KAAK,EAAE;QACX;MAAE,GAAC,qBAED,CACF,CAAC,eAGTlE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkM,KAAK,EAAE;QAAO;MAAE,gBAC1BxD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0M,QAAQ,EAAE,MAAM;UAChBb,UAAU,EAAE,KAAK;UACjBe,KAAK,EAAE,MAAM;UACbM,YAAY,EAAE;QAClB;MAAE,GAAC,8BAEH,CAAC,EAGJ,CAAC,MAAM;QACJ,MAAMI,aAAa,GAAG/Q,mBAAmB,EAAE4I,MAAM,EAAEE,WAAW,CAAC,CAAC,IAAI,SAAS;QAC7E,MAAMoF,eAAe,GACjBlO,mBAAmB,EAAEmO,gBAAgB,EAAErF,WAAW,CAAC,CAAC,IAAI,SAAS;QACrE,MAAMhJ,aAAa,GACfE,mBAAmB,EAAEqO,yBAAyB,EAAEvF,WAAW,CAAC,CAAC,IAAI,SAAS;;QAE9E;QACA,IACIhJ,aAAa,KAAK,WAAW,IAC7BA,aAAa,KAAK,SAAS,IAC3BA,aAAa,KAAK,WAAW,EAC/B;UACE,oBACIqM,KAAA,CAAA7I,aAAA,2BAEI6I,KAAA,CAAA7I,aAAA;YAAKG,KAAK,EAAE;cAAEkN,YAAY,EAAE;YAAO;UAAE,gBACjCxE,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACHyE,OAAO,EAAE,MAAM;cACfsH,UAAU,EAAE,QAAQ;cACpBD,GAAG,EAAE,MAAM;cACXO,YAAY,EAAE,KAAK;cACnBa,YAAY,EAAE;YAClB;UAAE,gBACFxE,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACHkM,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdE,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAE,SAAS;cAC1B7H,OAAO,EAAE,MAAM;cACfsH,UAAU,EAAE,QAAQ;cACpBoB,cAAc,EAAE,QAAQ;cACxBI,UAAU,EAAE;YAChB;UAAE,gBACF7E,KAAA,CAAA7I,aAAA;YACIqM,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,GAAG;YACVqB,OAAO,EAAC,UAAU;YAClBC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAC;UAA4B,gBAClChF,KAAA,CAAA7I,aAAA;YACI8N,CAAC,EAAC,qBAAqB;YACvBC,MAAM,EAAC,SAAS;YAChBC,WAAW,EAAC,GAAG;YACfC,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC;UAAO,CACzB,CACA,CACJ,CAAC,eACNrF,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACH0M,QAAQ,EAAE,MAAM;cAChBE,KAAK,EAAE,SAAS;cAChBf,UAAU,EAAE;YAChB;UAAE,GAAC,kBAED,CACL,CACJ,CAAC,eAGNnD,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACHyE,OAAO,EAAE,MAAM;cACfsH,UAAU,EAAE,YAAY;cACxBD,GAAG,EAAE,MAAM;cACXe,OAAO,EAAE,WAAW;cACpBP,eAAe,EAAE,SAAS;cAC1BD,YAAY,EAAE,KAAK;cACnBa,YAAY,EAAE;YAClB;UAAE,gBACFxE,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACHkM,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdE,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAE,SAAS;cAC1B7H,OAAO,EAAE,MAAM;cACfsH,UAAU,EAAE,QAAQ;cACpBoB,cAAc,EAAE,QAAQ;cACxBI,UAAU,EAAE,CAAC;cACb5B,SAAS,EAAE;YACf;UAAE,gBACFjD,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACH4M,KAAK,EAAE,SAAS;cAChBF,QAAQ,EAAE,MAAM;cAChBb,UAAU,EAAE;YAChB;UAAE,GAAC,GAED,CACL,CAAC,eACNnD,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACHgO,MAAM,EAAE,CAAC;cACTtB,QAAQ,EAAE,MAAM;cAChBE,KAAK,EAAE,SAAS;cAChBD,UAAU,EAAE,KAAK;cACjBd,UAAU,EAAE;YAChB;UAAE,GAAC,gDAC2C,EAAC,GAAG,eAClDnD,KAAA,CAAA7I,aAAA,iBAAQ,OAAa,CACtB,CACF,CACJ,CAAC;QAEd;;QAEA;QACA,IACI,CAAC4K,eAAe,KAAK,YAAY,IAAIA,eAAe,KAAK,UAAU,MAClEpO,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,YAAY,CAAC,EACnE;UACE,oBACIqM,KAAA,CAAA7I,aAAA,2BAEI6I,KAAA,CAAA7I,aAAA;YAAKG,KAAK,EAAE;cAAEkN,YAAY,EAAE;YAAO;UAAE,gBACjCxE,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACHyE,OAAO,EAAE,MAAM;cACfsH,UAAU,EAAE,QAAQ;cACpBD,GAAG,EAAE,MAAM;cACXO,YAAY,EAAE,KAAK;cACnBa,YAAY,EAAE;YAClB;UAAE,gBACFxE,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACHkM,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,mBAAmB;cAC3B6B,SAAS,EAAE,mBAAmB;cAC9B5B,YAAY,EAAE,KAAK;cACnB6B,SAAS,EAAE,yBAAyB;cACpCX,UAAU,EAAE;YAChB;UAAE,CAAM,CAAC,eACb7E,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACH0M,QAAQ,EAAE,MAAM;cAChBE,KAAK,EAAE,SAAS;cAChBf,UAAU,EAAE;YAChB;UAAE,GAAC,kBAED,CACL,CACJ,CACJ,CAAC;QAEd;;QAEA;QACA,IACIxP,aAAa,KAAK,QAAQ,IAC1BA,aAAa,KAAK,WAAW,IAC7BA,aAAa,KAAK,UAAU,IAC5BoO,eAAe,KAAK,QAAQ,IAC5BA,eAAe,KAAK,WAAW,IAC/BA,eAAe,KAAK,UAAU,EAChC;UACE,oBACI/B,KAAA,CAAA7I,aAAA,2BAEI6I,KAAA,CAAA7I,aAAA;YAAKG,KAAK,EAAE;cAAEkN,YAAY,EAAE,MAAM;cAAExB,SAAS,EAAE;YAAS;UAAE,gBACtDhD,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACHkM,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdE,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAE,SAAS;cAC1B7H,OAAO,EAAE,MAAM;cACfsH,UAAU,EAAE,QAAQ;cACpBoB,cAAc,EAAE,QAAQ;cACxBa,MAAM,EAAE;YACZ;UAAE,gBACFtF,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACH4M,KAAK,EAAE,SAAS;cAChBF,QAAQ,EAAE,MAAM;cAChBb,UAAU,EAAE;YAChB;UAAE,GAAC,GAED,CACL,CAAC,eACNnD,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACH0M,QAAQ,EAAE,MAAM;cAChBb,UAAU,EAAE,KAAK;cACjBe,KAAK,EAAE,SAAS;cAChBoB,MAAM,EAAE;YACZ;UAAE,GAAC,gBAEH,CAAC,eACLtF,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACH0M,QAAQ,EAAE,MAAM;cAChBE,KAAK,EAAE,SAAS;cAChBoB,MAAM,EAAE,YAAY;cACpBrB,UAAU,EAAE,KAAK;cACjBd,UAAU,EAAE;YAChB;UAAE,GACD1P,MAAM,CAACqL,OAAO,IACXzK,YAAY,EAAEkK,OAAO,IACrB,wCAAwC,eAC5CyB,KAAA,CAAA7I,aAAA,WAAK,CAAC,qBAEP,CAAC,EAGH9C,YAAY,iBACT2L,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACHkN,YAAY,EAAE,MAAM;cACpBL,OAAO,EAAE,MAAM;cACfP,eAAe,EAAE,SAAS;cAC1BF,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBX,SAAS,EAAE,MAAM;cACjBgB,QAAQ,EAAE;YACd;UAAE,gBACFhE,KAAA,CAAA7I,aAAA;YACIG,KAAK,EAAE;cACHgO,MAAM,EAAE,YAAY;cACpBpB,KAAK,EAAE,SAAS;cAChBF,QAAQ,EAAE,MAAM;cAChBb,UAAU,EAAE;YAChB;UAAE,GAAC,eAEH,CAAC,EAEJ9O,YAAY,CAAC0K,IAAI,iBACdiB,KAAA,CAAA7I,aAAA;YAAKG,KAAK,EAAE;cAAEkN,YAAY,EAAE;YAAM;UAAE,gBAChCxE,KAAA,CAAA7I,aAAA;YAAQG,KAAK,EAAE;cAAE4M,KAAK,EAAE;YAAU;UAAE,GAAC,aAE7B,CAAC,EAAC,GAAG,eACblE,KAAA,CAAA7I,aAAA;YAAMG,KAAK,EAAE;cAAE4M,KAAK,EAAE;YAAO;UAAE,GAC1B7P,YAAY,CAAC0K,IACZ,CACL,CACR,EAEA1K,YAAY,CAACwF,IAAI,iBACdmG,KAAA,CAAA7I,aAAA;YAAKG,KAAK,EAAE;cAAEkN,YAAY,EAAE;YAAM;UAAE,gBAChCxE,KAAA,CAAA7I,aAAA;YAAQG,KAAK,EAAE;cAAE4M,KAAK,EAAE;YAAU;UAAE,GAAC,aAE7B,CAAC,EAAC,GAAG,eACblE,KAAA,CAAA7I,aAAA;YAAMG,KAAK,EAAE;cAAE4M,KAAK,EAAE;YAAO;UAAE,GAC1B7P,YAAY,CAACwF,IACZ,CACL,CACR,EAEAxF,YAAY,CAAC2K,OAAO,iBACjBgB,KAAA,CAAA7I,aAAA;YAAKG,KAAK,EAAE;cAAEkN,YAAY,EAAE;YAAM;UAAE,gBAChCxE,KAAA,CAAA7I,aAAA;YAAQG,KAAK,EAAE;cAAE4M,KAAK,EAAE;YAAU;UAAE,GAAC,UAAgB,CAAC,EAAC,GAAG,eAC1DlE,KAAA,CAAA7I,aAAA;YAAMG,KAAK,EAAE;cAAE4M,KAAK,EAAE;YAAO;UAAE,GAC1B7P,YAAY,CAAC2K,OACZ,CACL,CACR,EAEA3K,YAAY,CAACsN,iBAAiB,iBAC3B3B,KAAA,CAAA7I,aAAA;YAAKG,KAAK,EAAE;cAAE2L,SAAS,EAAE,MAAM;cAAEe,QAAQ,EAAE;YAAO;UAAE,gBAChDhE,KAAA,CAAA7I,aAAA;YAAQG,KAAK,EAAE;cAAE4M,KAAK,EAAE;YAAU;UAAE,GAAC,oBAE7B,CAAC,eACTlE,KAAA,CAAA7I,aAAA;YAAKG,KAAK,EAAE;cAAE2L,SAAS,EAAE,KAAK;cAAEiB,KAAK,EAAE;YAAO;UAAE,GAC3C,OAAO7P,YAAY,CAACsN,iBAAiB,KAAK,QAAQ,GAC7C3H,MAAM,CAACyL,OAAO,CACVpR,YAAY,CAACsN,iBACjB,CAAC,CAACY,GAAG,CAAC,CAAC,CAACmD,KAAK,EAAEvL,KAAK,CAAC,EAAEwL,KAAK,kBACxB3F,KAAA,CAAA7I,aAAA;YACIyO,GAAG,EAAED,KAAM;YACXrO,KAAK,EAAE;cAAEkN,YAAY,EAAE;YAAM;UAAE,gBAC/BxE,KAAA,CAAA7I,aAAA,iBAASuO,KAAK,EAAC,GAAS,CAAC,KAAC,EAACvL,KAC1B,CACR,CAAC,GACF9F,YAAY,CAACsN,iBAClB,CACJ,CAER,CACR,eACD3B,KAAA,CAAA7I,aAAA;YACI0O,OAAO,EAAEA,CAAA,KAAM;cACXjS,gBAAgB,CAAC,MAAM,CAAC;cACxBF,SAAS,CAAC,CAAC,CAAC,CAAC;cACbY,eAAe,CAAC,IAAI,CAAC;cACrBR,sBAAsB,CAAC,IAAI,CAAC;YAChC,CAAE;YACFwD,KAAK,EAAE;cACHkM,KAAK,EAAE,MAAM;cACbW,OAAO,EAAE,WAAW;cACpBP,eAAe,EAAE,SAAS;cAC1BM,KAAK,EAAE,SAAS;cAChBR,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBL,MAAM,EAAE,SAAS;cACjBU,QAAQ,EAAE,MAAM;cAChBb,UAAU,EAAE,KAAK;cACjBc,UAAU,EAAE;YAChB;UAAE,GAAC,WAEC,CACP,CACJ,CAAC;QAEd;;QAEA;QACA,oBACIjE,KAAA,CAAA7I,aAAA,2BAEI6I,KAAA,CAAA7I,aAAA;UAAKG,KAAK,EAAE;YAAEkN,YAAY,EAAE;UAAO;QAAE,gBACjCxE,KAAA,CAAA7I,aAAA;UACIG,KAAK,EAAE;YACHyE,OAAO,EAAE,MAAM;YACfsH,UAAU,EAAE,YAAY;YACxBD,GAAG,EAAE,MAAM;YACXe,OAAO,EAAE,WAAW;YACpBP,eAAe,EAAE,SAAS;YAC1BD,YAAY,EAAE,KAAK;YACnBa,YAAY,EAAE;UAClB;QAAE,gBACFxE,KAAA,CAAA7I,aAAA;UACIG,KAAK,EAAE;YACHkM,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdE,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE,SAAS;YAC1B7H,OAAO,EAAE,MAAM;YACfsH,UAAU,EAAE,QAAQ;YACpBoB,cAAc,EAAE,QAAQ;YACxBI,UAAU,EAAE,CAAC;YACb5B,SAAS,EAAE;UACf;QAAE,gBACFjD,KAAA,CAAA7I,aAAA;UACIG,KAAK,EAAE;YACH4M,KAAK,EAAE,SAAS;YAChBF,QAAQ,EAAE,MAAM;YAChBb,UAAU,EAAE;UAChB;QAAE,GAAC,GAED,CACL,CAAC,eACNnD,KAAA,CAAA7I,aAAA;UACIG,KAAK,EAAE;YACHgO,MAAM,EAAE,CAAC;YACTtB,QAAQ,EAAE,MAAM;YAChBE,KAAK,EAAE,SAAS;YAChBD,UAAU,EAAE,KAAK;YACjBd,UAAU,EAAE;UAChB;QAAE,GAAC,sEAEJ,CACF,CAAC,eACNnD,KAAA,CAAA7I,aAAA;UACIG,KAAK,EAAE;YACHyE,OAAO,EAAE,MAAM;YACfsH,UAAU,EAAE,QAAQ;YACpBD,GAAG,EAAE,MAAM;YACXO,YAAY,EAAE;UAClB;QAAE,gBACF3D,KAAA,CAAA7I,aAAA;UACIG,KAAK,EAAE;YACHkM,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,MAAM,EAAE,mBAAmB;YAC3B6B,SAAS,EAAE,mBAAmB;YAC9B5B,YAAY,EAAE,KAAK;YACnB6B,SAAS,EAAE,yBAAyB;YACpCX,UAAU,EAAE;UAChB;QAAE,CAAM,CAAC,eACb7E,KAAA,CAAA7I,aAAA;UACIG,KAAK,EAAE;YACH0M,QAAQ,EAAE,MAAM;YAChBE,KAAK,EAAE,SAAS;YAChBf,UAAU,EAAE;UAChB;QAAE,GAAC,wBAED,CACL,CACJ,CACJ,CAAC;MAEd,CAAC,EAAE,CACF,CACJ,CACJ,CAAC;IAEd,CAAC;;IAED;IACA,IAAIxP,aAAa,KAAK,cAAc,EAAE;MAClC,OAAOoP,yBAAyB,CAAC,CAAC;IACtC;;IAEA;IACA,IAAIpP,aAAa,KAAK,0BAA0B,EAAE;MAC9C,oBACIqM,KAAA,CAAA7I,aAAA,CAAA6I,KAAA,CAAAuE,QAAA,qBAEIvE,KAAA,CAAA7I,aAAA,2BAEI6I,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0L,SAAS,EAAE,QAAQ;UACnBsC,MAAM,EAAE;QACZ;MAAE,gBACFtF,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0M,QAAQ,EAAE,MAAM;UAChBb,UAAU,EAAE,KAAK;UACjBe,KAAK,EAAE,MAAM;UACbM,YAAY,EAAE;QAClB;MAAE,GAAC,kCAEF,CAAC,eACNxE,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0M,QAAQ,EAAE,MAAM;UAChBE,KAAK,EAAE,SAAS;UAChBoB,MAAM,EAAE;QACZ;MAAE,GAAC,wDAEJ,CACF,CAAC,eAGNtF,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHsM,eAAe,EAAE,SAAS;UAC1BF,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,KAAK;UACnBQ,OAAO,EAAE,MAAM;UACfK,YAAY,EAAE;QAClB;MAAE,gBACFxE,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHyE,OAAO,EAAE,MAAM;UACfsH,UAAU,EAAE,QAAQ;UACpBmB,YAAY,EAAE;QAClB;MAAE,gBACFxE,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0M,QAAQ,EAAE,MAAM;UAChB8B,WAAW,EAAE;QACjB;MAAE,GAAC,cAED,CAAC,eACP9F,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0M,QAAQ,EAAE,MAAM;UAChBb,UAAU,EAAE,KAAK;UACjBe,KAAK,EAAE,SAAS;UAChBoB,MAAM,EAAE;QACZ;MAAE,GAAC,2BAEH,CACH,CAAC,eACNtF,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0M,QAAQ,EAAE,MAAM;UAChBE,KAAK,EAAE,SAAS;UAChBoB,MAAM,EAAE,YAAY;UACpBrB,UAAU,EAAE;QAChB;MAAE,GACDxQ,MAAM,CAACqL,OAAO,IACXzK,YAAY,EAAEkK,OAAO,IACrB,8CACL,CAAC,EAGHlK,YAAY,EAAE6K,YAAY,IAAI7K,YAAY,CAAC6K,YAAY,CAAC9E,MAAM,GAAG,CAAC,iBAC/D4F,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkN,YAAY,EAAE;QAAO;MAAE,gBACjCxE,KAAA,CAAA7I,aAAA;QAAQG,KAAK,EAAE;UAAE4M,KAAK,EAAE,SAAS;UAAEF,QAAQ,EAAE;QAAO;MAAE,GAAC,kBAAwB,CAAC,eAChFhE,KAAA,CAAA7I,aAAA;QAAIG,KAAK,EAAE;UAAEgO,MAAM,EAAE,cAAc;UAAEpB,KAAK,EAAE,MAAM;UAAEF,QAAQ,EAAE;QAAO;MAAE,GAClE3P,YAAY,CAAC6K,YAAY,CAACqD,GAAG,CAAC,CAACpI,KAAK,EAAEwL,KAAK,kBACxC3F,KAAA,CAAA7I,aAAA;QAAIyO,GAAG,EAAED,KAAM;QAACrO,KAAK,EAAE;UAAEkN,YAAY,EAAE;QAAM;MAAE,GAC1CrK,KAAK,CAACoE,OACP,CACP,CACD,CACH,CACR,eAEDyB,KAAA,CAAA7I,aAAA;QAAGG,KAAK,EAAE;UAAE0M,QAAQ,EAAE,MAAM;UAAEE,KAAK,EAAE,MAAM;UAAEoB,MAAM,EAAE;QAAI;MAAE,GAAC,mGAGzD,CACF,CAAC,eAGNtF,KAAA,CAAA7I,aAAA;QACI0O,OAAO,EAAEjE,qBAAsB;QAC/BmE,QAAQ,EAAEhS,YAAa;QACvBuD,KAAK,EAAE;UACHkM,KAAK,EAAE,MAAM;UACbW,OAAO,EAAE,WAAW;UACpBP,eAAe,EAAE7P,YAAY,GAAG,MAAM,GAAG,SAAS;UAClDmQ,KAAK,EAAE,OAAO;UACdR,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBK,QAAQ,EAAE,MAAM;UAChBb,UAAU,EAAE,KAAK;UACjBG,MAAM,EAAEvP,YAAY,GAAG,aAAa,GAAG,SAAS;UAChDiS,UAAU,EAAE,uBAAuB;UACnCxB,YAAY,EAAE;QAClB,CAAE;QACFyB,WAAW,EAAEC,CAAC,IAAI;UACd,IAAI,CAACnS,YAAY,EAAE;YACfmS,CAAC,CAACC,MAAM,CAAC7O,KAAK,CAACsM,eAAe,GAAG,SAAS;UAC9C;QACJ,CAAE;QACFwC,UAAU,EAAEF,CAAC,IAAI;UACb,IAAI,CAACnS,YAAY,EAAE;YACfmS,CAAC,CAACC,MAAM,CAAC7O,KAAK,CAACsM,eAAe,GAAG,SAAS;UAC9C;QACJ;MAAE,GACD7P,YAAY,gBACTiM,KAAA,CAAA7I,aAAA,CAAA6I,KAAA,CAAAuE,QAAA,qBACIvE,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHyE,OAAO,EAAE,cAAc;UACvByH,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,mBAAmB;UAC3B6B,SAAS,EAAE,uBAAuB;UAClC5B,YAAY,EAAE,KAAK;UACnB6B,SAAS,EAAE,yBAAyB;UACpCM,WAAW,EAAE;QACjB;MAAE,CAAO,CAAC,0BAEhB,CAAC,GAEH,8BAEA,CACP,CACP,CAAC;IAEX;;IAEA;IACA,IAAInS,aAAa,KAAK,QAAQ,EAAE;MAC5B,oBACIqM,KAAA,CAAA7I,aAAA,CAAA6I,KAAA,CAAAuE,QAAA,qBAEIvE,KAAA,CAAA7I,aAAA,2BAEI6I,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0L,SAAS,EAAE,QAAQ;UACnBsC,MAAM,EAAE;QACZ;MAAE,gBACFtF,KAAA,CAAA7I,aAAA;QAAKE,SAAS,EAAC;MAAkB,gBAC7B2I,KAAA,CAAA7I,aAAA;QAAKE,SAAS,EAAC;MAAW,GAAC,KAEtB,CAAC,eACN2I,KAAA,CAAA7I,aAAA;QAAK+L,KAAK,EAAC;MAAQ,gBACflD,KAAA,CAAA7I,aAAA;QAAM+L,KAAK,EAAC;MAAiC,gBACzClD,KAAA,CAAA7I,aAAA,2BACI6I,KAAA,CAAA7I,aAAA;QAAM+L,KAAK,EAAC;MAAkC,GAAC,GAAO,CAAC,EACtDlH,UAAU,CAACqC,OAAO,CAAC,CAAC,CACpB,CACH,CACL,CACJ,CACJ,CAAC,eAGN2B,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHoM,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBQ,OAAO,EAAE,gBAAgB;UACzBf,GAAG,EAAE,MAAM;UACXrH,OAAO,EAAE,MAAM;UACfqI,aAAa,EAAE,QAAQ;UACvBf,UAAU,EAAE,QAAQ;UACpBL,SAAS,EAAE;QACf;MAAE,gBACFhD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHkM,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdE,YAAY,EAAE,KAAK;UACnBC,eAAe,EAAE,SAAS;UAC1B7H,OAAO,EAAE,MAAM;UACfsH,UAAU,EAAE,QAAQ;UACpBoB,cAAc,EAAE,QAAQ;UACxBa,MAAM,EAAE;QACZ;MAAE,gBACFtF,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH4M,KAAK,EAAE,SAAS;UAChBF,QAAQ,EAAE,MAAM;UAChBb,UAAU,EAAE;QAChB;MAAE,GAAC,GAED,CACL,CAAC,eACNnD,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0M,QAAQ,EAAE,MAAM;UAChBb,UAAU,EAAE,KAAK;UACjBe,KAAK,EAAE,SAAS;UAChBoB,MAAM,EAAE;QACZ;MAAE,GAAC,gBAEH,CAAC,eACLtF,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH0M,QAAQ,EAAE,MAAM;UAChBE,KAAK,EAAE,SAAS;UAChBoB,MAAM,EAAE,YAAY;UACpBrB,UAAU,EAAE;QAChB;MAAE,GACDxQ,MAAM,CAACqL,OAAO,IAAIzK,YAAY,EAAEkK,OAAO,IAAI,wCAC7C,CAAC,EAGHlK,YAAY,iBACT2L,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHkN,YAAY,EAAE,MAAM;UACpBL,OAAO,EAAE,MAAM;UACfP,eAAe,EAAE,SAAS;UAC1BF,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,KAAK;UACnBX,SAAS,EAAE,MAAM;UACjBgB,QAAQ,EAAE;QACd;MAAE,gBACFhE,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACHgO,MAAM,EAAE,YAAY;UACpBpB,KAAK,EAAE,SAAS;UAChBF,QAAQ,EAAE,MAAM;UAChBb,UAAU,EAAE;QAChB;MAAE,GAAC,eAEH,CAAC,EAEJ9O,YAAY,CAAC0K,IAAI,iBACdiB,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkN,YAAY,EAAE;QAAM;MAAE,gBAChCxE,KAAA,CAAA7I,aAAA;QAAQG,KAAK,EAAE;UAAE4M,KAAK,EAAE;QAAU;MAAE,GAAC,aAAmB,CAAC,EAAC,GAAG,eAC7DlE,KAAA,CAAA7I,aAAA;QAAMG,KAAK,EAAE;UAAE4M,KAAK,EAAE;QAAO;MAAE,GAAE7P,YAAY,CAAC0K,IAAW,CACxD,CACR,EAEA1K,YAAY,CAACwF,IAAI,iBACdmG,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkN,YAAY,EAAE;QAAM;MAAE,gBAChCxE,KAAA,CAAA7I,aAAA;QAAQG,KAAK,EAAE;UAAE4M,KAAK,EAAE;QAAU;MAAE,GAAC,aAAmB,CAAC,EAAC,GAAG,eAC7DlE,KAAA,CAAA7I,aAAA;QAAMG,KAAK,EAAE;UAAE4M,KAAK,EAAE;QAAO;MAAE,GAAE7P,YAAY,CAACwF,IAAW,CACxD,CACR,EAEAxF,YAAY,CAAC2K,OAAO,iBACjBgB,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkN,YAAY,EAAE;QAAM;MAAE,gBAChCxE,KAAA,CAAA7I,aAAA;QAAQG,KAAK,EAAE;UAAE4M,KAAK,EAAE;QAAU;MAAE,GAAC,UAAgB,CAAC,EAAC,GAAG,eAC1DlE,KAAA,CAAA7I,aAAA;QAAMG,KAAK,EAAE;UAAE4M,KAAK,EAAE;QAAO;MAAE,GAAE7P,YAAY,CAAC2K,OAAc,CAC3D,CACR,EAEA,CAAC3K,YAAY,CAAC2N,gBAAgB,IAC3B3N,YAAY,CAACyO,cAAc,IAC3BzO,YAAY,CAAC+N,YAAY,kBACzBpC,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAEkN,YAAY,EAAE;QAAM;MAAE,gBAChCxE,KAAA,CAAA7I,aAAA;QAAQG,KAAK,EAAE;UAAE4M,KAAK,EAAE;QAAU;MAAE,GAAC,SAAe,CAAC,EAAC,GAAG,eACzDlE,KAAA,CAAA7I,aAAA;QAAMG,KAAK,EAAE;UAAE4M,KAAK,EAAE;QAAO;MAAE,GAC1B,CACG7P,YAAY,CAAC2N,gBAAgB,EAC7B3N,YAAY,CAACyO,cAAc,EAC3BzO,YAAY,CAAC+N,YAAY,CAC5B,CACIiE,MAAM,CAACC,OAAO,CAAC,CACf7D,IAAI,CAAC,IAAI,CACZ,CACL,CACR,EAEApO,YAAY,CAAC+K,OAAO,iBACjBY,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAE2L,SAAS,EAAE,MAAM;UAAEe,QAAQ,EAAE;QAAO;MAAE,gBAChDhE,KAAA,CAAA7I,aAAA;QAAQG,KAAK,EAAE;UAAE4M,KAAK,EAAE;QAAU;MAAE,GAAC,oBAA0B,CAAC,eAChElE,KAAA,CAAA7I,aAAA;QACIG,KAAK,EAAE;UACH2L,SAAS,EAAE,KAAK;UAChBkB,OAAO,EAAE,KAAK;UACdP,eAAe,EAAE,SAAS;UAC1BF,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,KAAK;UACnBO,KAAK,EAAE,MAAM;UACbqC,UAAU,EAAE,WAAW;UACvBvC,QAAQ,EAAE,MAAM;UAChBwC,SAAS,EAAE;QACf;MAAE,GACD,OAAOnS,YAAY,CAAC+K,OAAO,KAAK,QAAQ,GACnC/K,YAAY,CAAC+K,OAAO,GACpBqH,IAAI,CAACC,SAAS,CAACrS,YAAY,CAAC+K,OAAO,EAAE,IAAI,EAAE,CAAC,CACjD,CACJ,CACR,EAEA/K,YAAY,CAACsN,iBAAiB,iBAC3B3B,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAE2L,SAAS,EAAE,MAAM;UAAEe,QAAQ,EAAE;QAAO;MAAE,gBAChDhE,KAAA,CAAA7I,aAAA;QAAQG,KAAK,EAAE;UAAE4M,KAAK,EAAE;QAAU;MAAE,GAAC,oBAA0B,CAAC,eAChElE,KAAA,CAAA7I,aAAA;QAAKG,KAAK,EAAE;UAAE2L,SAAS,EAAE,KAAK;UAAEiB,KAAK,EAAE;QAAO;MAAE,GAC3C,OAAO7P,YAAY,CAACsN,iBAAiB,KAAK,QAAQ,GAC7C3H,MAAM,CAACyL,OAAO,CAACpR,YAAY,CAACsN,iBAAiB,CAAC,CAACY,GAAG,CAC9C,CAAC,CAACmD,KAAK,EAAEvL,KAAK,CAAC,EAAEwL,KAAK,kBAClB3F,KAAA,CAAA7I,aAAA;QAAKyO,GAAG,EAAED,KAAM;QAACrO,KAAK,EAAE;UAAEkN,YAAY,EAAE;QAAM;MAAE,gBAC5CxE,KAAA,CAAA7I,aAAA,iBAASuO,KAAK,EAAC,GAAS,CAAC,KAAC,EAACvL,KAC1B,CAEb,CAAC,GACD9F,YAAY,CAACsN,iBAClB,CACJ,CAER,CACR,eACD3B,KAAA,CAAA7I,aAAA;QACI0O,OAAO,EAAEA,CAAA,KAAM;UACXjS,gBAAgB,CAAC,MAAM,CAAC;UACxBF,SAAS,CAAC,CAAC,CAAC,CAAC;UACbY,eAAe,CAAC,IAAI,CAAC;UACrBR,sBAAsB,CAAC,IAAI,CAAC;QAChC,CAAE;QACFwD,KAAK,EAAE;UACHkM,KAAK,EAAE,MAAM;UACbW,OAAO,EAAE,WAAW;UACpBP,eAAe,EAAE,SAAS;UAC1BM,KAAK,EAAE,SAAS;UAChBR,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBL,MAAM,EAAE,SAAS;UACjBU,QAAQ,EAAE,MAAM;UAChBb,UAAU,EAAE,KAAK;UACjBc,UAAU,EAAE;QAChB;MAAE,GAAC,WAEC,CACP,CACJ,CACP,CAAC;IAEX;IAEA,oBACIjE,KAAA,CAAA7I,aAAA,CAAA6I,KAAA,CAAAuE,QAAA,qBAEIvE,KAAA,CAAA7I,aAAA;MAAKE,SAAS,EAAC,qFAAqF;MAACsP,GAAG,EAAErQ;IAAa,gBAEnH0J,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH0L,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE;MACf;IAAE,gBACFjD,KAAA,CAAA7I,aAAA;MAAKE,SAAS,EAAC;IAAkB,gBAC7B2I,KAAA,CAAA7I,aAAA;MAAKE,SAAS,EAAC;IAAW,GAAC,KAEtB,CAAC,eACN2I,KAAA,CAAA7I,aAAA;MAAK+L,KAAK,EAAC;IAAQ,gBACflD,KAAA,CAAA7I,aAAA;MAAM+L,KAAK,EAAC;IAAiC,gBACzClD,KAAA,CAAA7I,aAAA,2BACI6I,KAAA,CAAA7I,aAAA;MAAM+L,KAAK,EAAC;IAAkC,GAAC,GAAO,CAAC,EACtDlH,UAAU,CAACqC,OAAO,CAAC,CAAC,CACpB,CACH,CACL,CACJ,CACJ,CAAC,EAGL,CAACpD,oBAAoB,CAAC,CAAC,iBACpB+E,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QACR6M,OAAO,EAAE,MAAM;QACfP,eAAe,EAAE,SAAS;QAC1BD,YAAY,EAAE,KAAK;QACnB2B,MAAM,EAAE,QAAQ;QAChBtC,SAAS,EAAE;MACf;IAAE,gBACEhD,KAAA,CAAA7I,aAAA;MAAGG,KAAK,EAAE;QAAEgO,MAAM,EAAE,WAAW;QAAEtB,QAAQ,EAAE,MAAM;QAAEE,KAAK,EAAE;MAAU;IAAE,GAAC,0EAEpE,CAAC,eACJlE,KAAA,CAAA7I,aAAA;MAAOG,KAAK,EAAE;QAAE0M,QAAQ,EAAE,MAAM;QAAEE,KAAK,EAAE;MAAU;IAAE,GAAC,6DAE/C,CACN,CACR,EAGA/P,kBAAkB,IAAIhC,QAAQ,CAAC+K,iBAAiB,iBAC7C8C,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkN,YAAY,EAAE,MAAM;QAAExB,SAAS,EAAE;MAAS;IAAE,GAErDzO,WAAW,iBACRyL,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACHkN,YAAY,EAAE,MAAM;QACpBL,OAAO,EAAE,MAAM;QACfP,eAAe,EAAE,SAAS;QAC1BF,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBX,SAAS,EAAE;MACf;IAAE,gBACFhD,KAAA,CAAA7I,aAAA;MAAIG,KAAK,EAAE;QAAEgO,MAAM,EAAE,YAAY;QAAEpB,KAAK,EAAE,SAAS;QAAEF,QAAQ,EAAE;MAAO;IAAE,GACnE1K,mDAAE,CAAC,4BAA4B,EAAE,kCAAkC,CACpE,CAAC,eACL0G,KAAA,CAAA7I,aAAA;MAAGG,KAAK,EAAE;QAAEgO,MAAM,EAAE,YAAY;QAAEpB,KAAK,EAAE,MAAM;QAAEF,QAAQ,EAAE;MAAO;IAAE,GAC/D1K,mDAAE,CACC,wEAAwE,EACxE,kCACJ,CACD,CAAC,eACJ0G,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACHsM,eAAe,EAAE,SAAS;QAC1BO,OAAO,EAAE,MAAM;QACfR,YAAY,EAAE,KAAK;QACnBK,QAAQ,EAAE,MAAM;QAChBE,KAAK,EAAE;MACX;IAAE,gBACFlE,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkN,YAAY,EAAE;MAAM;IAAE,gBAChCxE,KAAA,CAAA7I,aAAA,iBAASmC,mDAAE,CAAC,YAAY,EAAE,kCAAkC,CAAU,CAAC,EAAC,GAAG,EAC1E/E,WAAW,CAAC4J,YAAY,GACnB,MAAM5J,WAAW,CAAC4J,YAAY,CAACyI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAC1C,WACL,CAAC,eACN5G,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkN,YAAY,EAAE;MAAM;IAAE,gBAChCxE,KAAA,CAAA7I,aAAA,iBAASmC,mDAAE,CAAC,iBAAiB,EAAE,kCAAkC,CAAU,CAAC,EAAC,GAAG,EAC/E6C,QAAQ,EAAC,IAAE,EAACD,UAAU,CAAC3H,WAAW,CAACf,cAAc,IAAI,CAAC,CAAC,CAAC6K,OAAO,CAAC,CAAC,CACjE,CAAC,eACN2B,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkN,YAAY,EAAE;MAAM;IAAE,gBAChCxE,KAAA,CAAA7I,aAAA,iBAASmC,mDAAE,CAAC,SAAS,EAAE,kCAAkC,CAAU,CAAC,EAAC,GAAG,eACxE0G,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH4M,KAAK,EAAE3P,WAAW,CAACkI,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;QAClE0G,UAAU,EAAE;MAChB;IAAE,GACD5O,WAAW,CAACkI,MAAM,IAAI,QACrB,CACL,CAAC,EACLlI,WAAW,CAACsS,6BAA6B,iBACtC7G,KAAA,CAAA7I,aAAA,2BACI6I,KAAA,CAAA7I,aAAA,iBACKmC,mDAAE,CAAC,eAAe,EAAE,kCAAkC,CACnD,CAAC,EAAC,GAAG,eACb0G,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH4M,KAAK,EACD3P,WAAW,CAACuS,sBAAsB,GAAG,CAAC,GAChC,SAAS,GACT,SAAS;QACnB3D,UAAU,EAAE;MAChB;IAAE,GACD5O,WAAW,CAACuS,sBAAsB,IAAI,CAAC,EAAC,YACvC,CAAC,EAAC,GAAG,eACX9G,KAAA,CAAA7I,aAAA;MAAMG,KAAK,EAAE;QAAE4M,KAAK,EAAE,SAAS;QAAEF,QAAQ,EAAE;MAAO;IAAE,GAAC,GAChD,EAACzP,WAAW,CAACwS,wBAAwB,IAAI,CAAC,EAAC,GAC5C,EAACxS,WAAW,CAACsS,6BAA6B,IAAI,EAAE,EAAC,QAC/C,CACL,CAER,CACJ,CACR,eAED7G,KAAA,CAAA7I,aAAA;MACI0C,IAAI,EAAC,QAAQ;MACbgM,OAAO,EAAE7H,iCAAkC;MAC3C+H,QAAQ,EAAEhS,YAAa;MACvBuD,KAAK,EAAE;QACHkM,KAAK,EAAE,MAAM;QACbW,OAAO,EAAE,WAAW;QACpBP,eAAe,EAAE7P,YAAY,GAAG,SAAS,GAAG,SAAS;QACrDmQ,KAAK,EAAE,SAAS;QAChBR,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,KAAK;QACnBL,MAAM,EAAEvP,YAAY,GAAG,aAAa,GAAG,SAAS;QAChDiQ,QAAQ,EAAE,MAAM;QAChBb,UAAU,EAAE,KAAK;QACjBqB,YAAY,EAAE;MAClB;IAAE,GACDzQ,YAAY,GACPuF,mDAAE,CAAC,eAAe,EAAE,kCAAkC,CAAC,GACvDA,mDAAE,CAAC,iCAAiC,EAAE,kCAAkC,CAC1E,CAAC,eACT0G,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH0M,QAAQ,EAAE,MAAM;QAChBE,KAAK,EAAE,SAAS;QAChBM,YAAY,EAAE;MAClB;IAAE,GACDlL,mDAAE,CAAC,sCAAsC,EAAE,kCAAkC,CAC7E,CACJ,CACR,EAGG2B,oBAAoB,CAAC,CAAC,iBACnB+E,KAAA,CAAA7I,aAAA,CAAA6I,KAAA,CAAAuE,QAAA,qBAECvE,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkN,YAAY,EAAE;MAAO;IAAE,gBACjCxE,KAAA,CAAA7I,aAAA;MAAKE,SAAS,EAAC;IAAsC,gBACjD2I,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAE6L,UAAU,EAAE;MAAI;IAAE,GAAC,UAE5B,CAAC,eACNnD,KAAA,CAAA7I,aAAA,CAACqC,+DAAY;MACTwN,QAAQ,EAAEtU,aAAc;MACxBuU,OAAO,EAAE,CACL;QAAEvM,KAAK,EAAE,OAAO;QAAE6E,KAAK,EAAE;MAAQ,CAAC,EAClC;QAAE7E,KAAK,EAAE,wBAAwB;QAAE6E,KAAK,EAAE;MAAc,CAAC,CAC3D;MACF2H,QAAQ,EAAGC,CAAC,IAAKxU,gBAAgB,CAACwU,CAAC;IAAE,CACxC,CAEA,CACJ,CAAC,eAGNnH,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACHoM,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBQ,OAAO,EAAE,gBAAgB;QACzBf,GAAG,EAAE,MAAM;QACXrH,OAAO,EAAE,MAAM;QACfqI,aAAa,EAAE,QAAQ;QACvBf,UAAU,EAAE;MAChB;IAAE,GAED3Q,aAAa,KAAK,OAAO,iBACtBsN,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkM,KAAK,EAAE,MAAM;QAAEzH,OAAO,EAAE,MAAM;QAAEqI,aAAa,EAAE;MAAS;IAAE,gBACpEpE,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEyE,OAAO,EAAE,MAAM;QAAEsH,UAAU,EAAE;MAAS;IAAE,gBAClDrD,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH6L,UAAU,EAAE,KAAK;QACjBa,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,OAAO;QACnBC,KAAK,EAAE;MACX;IAAE,GAAC,kBAEA,CACN,CAAC,eACNlE,KAAA,CAAA7I,aAAA,CAACuC,8DAAW;MACR6F,KAAK,EAAE3M,UAAW;MAClBsU,QAAQ,EAAE3H,KAAK,IAAI;QACf1M,aAAa,CAAC0M,KAAK,CAAC;QACpBD,aAAa,CAACC,KAAK,CAAC;MACxB,CAAE;MACF+E,WAAW,EAAC,cAAc;MAC1BjN,SAAS,EAAE5D,MAAM,CAACb,UAAU,GAAG,WAAW,GAAG;IAAG,CACnD,CAAC,EACDa,MAAM,CAACb,UAAU,iBACdoN,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH4M,KAAK,EAAE,SAAS;QAChBF,QAAQ,EAAE,MAAM;QAChBf,SAAS,EAAE,KAAK;QAChBlH,OAAO,EAAE,MAAM;QACfsH,UAAU,EAAE,QAAQ;QACpBD,GAAG,EAAE;MACT;IAAE,GACD3P,MAAM,CAACb,UACP,CAER,CACR,EAGAF,aAAa,KAAK,aAAa,iBAC5BsN,KAAA,CAAA7I,aAAA,CAAA6I,KAAA,CAAAuE,QAAA,qBAEIvE,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkM,KAAK,EAAE,MAAM;QAAEzH,OAAO,EAAE,MAAM;QAAEqI,aAAa,EAAE;MAAS;IAAE,gBACpEpE,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEyE,OAAO,EAAE,MAAM;QAAEsH,UAAU,EAAE;MAAS;IAAE,gBAClDrD,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH6L,UAAU,EAAE,KAAK;QACjBa,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,OAAO;QACnBC,KAAK,EAAE;MACX;IAAE,GAAC,mCAEA,CACN,CAAC,eACNlE,KAAA,CAAA7I,aAAA,CAACuC,8DAAW;MACR6F,KAAK,EAAEvM,WAAY;MACnBkU,QAAQ,EAAEjU,cAAe;MACzBqR,WAAW,EAAC,iBAAiB;MAC7BjN,SAAS,EAAE5D,MAAM,CAACT,WAAW,GAAG,WAAW,GAAG;IAAG,CACpD,CAAC,EACDS,MAAM,CAACT,WAAW,iBACfgN,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH4M,KAAK,EAAE,SAAS;QAChBF,QAAQ,EAAE,MAAM;QAChBf,SAAS,EAAE,KAAK;QAChBlH,OAAO,EAAE,MAAM;QACfsH,UAAU,EAAE,QAAQ;QACpBD,GAAG,EAAE;MACT;IAAE,GACD3P,MAAM,CAACT,WACP,CAER,CAAC,eAGNgN,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkM,KAAK,EAAE,MAAM;QAAEzH,OAAO,EAAE,MAAM;QAAEqI,aAAa,EAAE;MAAS;IAAE,gBACpEpE,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEyE,OAAO,EAAE,MAAM;QAAEsH,UAAU,EAAE;MAAS;IAAE,gBAClDrD,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH6L,UAAU,EAAE,KAAK;QACjBa,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,OAAO;QACnBC,KAAK,EAAE;MACX;IAAE,GAAC,KAEA,CACN,CAAC,eACNlE,KAAA,CAAA7I,aAAA,CAACuC,8DAAW;MACR6F,KAAK,EAAErM,GAAI;MACXgU,QAAQ,EAAE/T,MAAO;MACjBmR,WAAW,EAAC,SAAS;MACrBjN,SAAS,EAAE5D,MAAM,CAACP,GAAG,GAAG,WAAW,GAAG;IAAG,CAC5C,CAAC,EACDO,MAAM,CAACP,GAAG,iBACP8M,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH4M,KAAK,EAAE,SAAS;QAChBF,QAAQ,EAAE,MAAM;QAChBf,SAAS,EAAE,KAAK;QAChBlH,OAAO,EAAE,MAAM;QACfsH,UAAU,EAAE,QAAQ;QACpBD,GAAG,EAAE;MACT;IAAE,GACD3P,MAAM,CAACP,GACP,CAER,CAAC,eAGN8M,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkM,KAAK,EAAE,MAAM;QAAEzH,OAAO,EAAE,MAAM;QAAEqI,aAAa,EAAE;MAAS;IAAE,gBACpEpE,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEyE,OAAO,EAAE,MAAM;QAAEsH,UAAU,EAAE;MAAS;IAAE,gBAClDrD,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH6L,UAAU,EAAE,KAAK;QACjBa,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,OAAO;QACnBC,KAAK,EAAE;MACX;IAAE,GAAC,gBAEA,CACN,CAAC,eACNlE,KAAA,CAAA7I,aAAA,CAACuC,8DAAW;MACR6F,KAAK,EAAEnM,aAAc;MACrB8T,QAAQ,EAAE7T,gBAAiB;MAC3BiR,WAAW,EAAC,2BAA2B;MACvCjN,SAAS,EAAE5D,MAAM,CAACL,aAAa,GAAG,WAAW,GAAG;IAAG,CACtD,CAAC,EACDK,MAAM,CAACL,aAAa,iBACjB4M,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH4M,KAAK,EAAE,SAAS;QAChBF,QAAQ,EAAE,MAAM;QAChBf,SAAS,EAAE,KAAK;QAChBlH,OAAO,EAAE,MAAM;QACfsH,UAAU,EAAE,QAAQ;QACpBD,GAAG,EAAE;MACT;IAAE,GACD3P,MAAM,CAACL,aACP,CAER,CACP,CACL,eAGD4M,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkM,KAAK,EAAE,MAAM;QAAEzH,OAAO,EAAE,MAAM;QAAEqI,aAAa,EAAE,QAAQ;QAAEhB,GAAG,EAAE;MAAO;IAAE,gBACjFpD,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEyE,OAAO,EAAE,MAAM;QAAEqI,aAAa,EAAE;MAAS;IAAE,gBACrDpE,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEyE,OAAO,EAAE,MAAM;QAAEsH,UAAU,EAAE;MAAS;IAAE,gBAClDrD,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH6L,UAAU,EAAE,KAAK;QACjBa,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,OAAO;QACnBC,KAAK,EAAE;MACX;IAAE,GAAC,kCAEA,CACN,CAAC,eACNlE,KAAA,CAAA7I,aAAA;MAAKE,SAAS,EAAC;IAA0B,gBACrC2I,KAAA,CAAA7I,aAAA;MAAME,SAAS,EAAC;IAAqB,GAAC,GAAO,CAAC,eAC9C2I,KAAA,CAAA7I,aAAA,CAACuC,8DAAW;MACRG,IAAI,EAAC,QAAQ;MACb0F,KAAK,EAAEjM,aAAc;MACrB4T,QAAQ,EAAE3H,KAAK,IAAI;QACf;QACA,IAAIA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKrJ,SAAS,EAAE;UACvD3C,gBAAgB,CAAC,EAAE,CAAC;UACpB;QACJ;;QAEA;QACA,MAAM6T,eAAe,GAAG7H,KAAK,CAACwB,QAAQ,CAAC,CAAC,CAACsG,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;;QAE1D;QACA,IAAID,eAAe,KAAK,GAAG,IAAIA,eAAe,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC3H,IAAI,CAACyH,eAAe,CAAC,EAAE;UACjG7T,gBAAgB,CAAC6T,eAAe,CAAC;UACjC;QACJ;;QAEA;QACA,MAAMG,YAAY,GAAGrL,UAAU,CAACkL,eAAe,CAAC;QAChD,IAAI,CAACrH,KAAK,CAACwH,YAAY,CAAC,EAAE;UACtBhU,gBAAgB,CAACgU,YAAY,CAAC;QAClC;MACJ,CAAE;MACFC,GAAG,EAAC,MAAM;MACVC,IAAI,EAAC,MAAM;MACXnD,WAAW,EAAC,MAAM;MAClBjN,SAAS,EAAE5D,MAAM,CAACH,aAAa,GAAG,WAAW,GAAG;IAAG,CACtD,CACA,CAAC,EACLG,MAAM,CAACH,aAAa,iBACjB0M,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH4M,KAAK,EAAE,SAAS;QAChBF,QAAQ,EAAE,MAAM;QAChBf,SAAS,EAAE,KAAK;QAChBlH,OAAO,EAAE,MAAM;QACfsH,UAAU,EAAE,QAAQ;QACpBD,GAAG,EAAE;MACT;IAAE,GACD3P,MAAM,CAACH,aACP,CAER,CAAC,eACN0M,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH6L,UAAU,EAAE,KAAK;QACjBa,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,QAAQ;QACpBC,KAAK,EAAE;MACX;IAAE,GAAC,kHAGF,CACJ,CAAC,eAGNlE,KAAA,CAAA7I,aAAA,CAACsC,yDAAM;MACHnC,KAAK,EAAE;QACHkM,KAAK,EAAE,MAAM;QACbzH,OAAO,EAAE,MAAM;QACf0I,cAAc,EAAE,QAAQ;QACxBpB,UAAU,EAAE,QAAQ;QACpBD,GAAG,EAAE,MAAM;QACXQ,eAAe,EAAE,SAAS;QAC1BD,YAAY,EAAE,KAAK;QACnBe,SAAS,EAAE,MAAM;QACjBpB,MAAM,EAAEvP,YAAY,GAAG,aAAa,GAAG;MAC3C,CAAE;MACF8R,OAAO,EAAE9R,YAAY,GAAGmC,SAAS,GAAG8K;IAAkB,gBACtDhB,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACH6L,UAAU,EAAE,KAAK;QACjBa,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,QAAQ;QACpBC,KAAK,EAAE;MACX;IAAE,GACDnQ,YAAY,IAAIJ,aAAa,KAAK,YAAY,GACzC,eAAe,GACf,qBACJ,CACF,CACP,CACP,CAGL,CACP,CAAC;EAEX,CAAC;;EAED;AACJ;AACA;EACI,MAAM+T,OAAO,GAAG,SAAAA,CAAUC,KAAK,EAAE;IAC7B,MAAMpN,WAAW,GAAGlB,wEAAc,CAAClH,QAAQ,CAACoI,WAAW,IAAI,EAAE,CAAC;IAC9D,oBACIyF,KAAA,CAAA7I,aAAA;MAAKE,SAAS,EAAC;IAAuB,gBAClC2I,KAAA,CAAA7I,aAAA;MACIE,SAAS,EAAC,2BAA2B;MACrCuQ,uBAAuB,EAAE;QAAEC,MAAM,EAAEtN;MAAY;IAAE,CACpD,CAAC,eACFyF,KAAA,CAAA7I,aAAA,CAACwD,SAAS,EAAKgN,KAAQ,CACtB,CAAC;EAEd,CAAC;;EAED;AACJ;AACA;EACI,MAAMG,KAAK,GAAGH,KAAK,IAAI;IACnB,oBACI3H,KAAA,CAAA7I,aAAA;MAAKG,KAAK,EAAE;QAAEkM,KAAK,EAAE;MAAO;IAAE,gBAC1BxD,KAAA,CAAA7I,aAAA;MACIG,KAAK,EAAE;QACHyE,OAAO,EAAE,MAAM;QACf0I,cAAc,EAAE,eAAe;QAC/BpB,UAAU,EAAE,QAAQ;QACpBG,KAAK,EAAE;MACX;IAAE,gBACFxD,KAAA,CAAA7I,aAAA;MAAMG,KAAK,EAAE;QAAE6L,UAAU,EAAE;MAAI;IAAE,GAAEzI,KAAY,CAAC,eAChDsF,KAAA,CAAA7I,aAAA;MACI4Q,GAAG,EAAE,GACD5V,QAAQ,CAAC6V,UAAU,IAAI,uDAAuD,8BACnD;MAC/BC,GAAG,EAAC,YAAY;MAChB3Q,KAAK,EAAE;QACHmM,MAAM,EAAE,MAAM;QACdD,KAAK,EAAE;MACX;IAAE,CACL,CACA,CACJ,CAAC;EAEd,CAAC;;EAED;AACJ;AACA;EACI,MAAM0E,YAAY,GAAG;IACjBC,IAAI,EAAE,eAAe;IACrBzN,KAAK,eAAEsF,KAAA,CAAA7I,aAAA,CAAC2Q,KAAK,MAAE,CAAC;IAChBM,OAAO,eAAEpI,KAAA,CAAA7I,aAAA,CAACuQ,OAAO,MAAE,CAAC;IACpBW,IAAI,eAAErI,KAAA,CAAA7I,aAAA,CAACuQ,OAAO,MAAE,CAAC;IACjBY,cAAc,EAAEA,CAAA,KAAM;MAClB,OAAO,IAAI;IACf,CAAC;IACDC,SAAS,EAAE7N,KAAK;IAChBF,QAAQ,EAAE;MACNgO,QAAQ,EAAErW,QAAQ,CAACqI,QAAQ,IAAI;IACnC,CAAC;IACDiO,KAAK,EAAEtW,QAAQ,CAACsW,KAAK,IAAI;EAC7B,CAAC;;EAED;EACA,IAAI;IACAvP,mFAAqB,CAACgP,YAAY,CAAC;EACvC,CAAC,CAAC,OAAO/N,KAAK,EAAE;IACZpE,OAAO,CAACoE,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;EACnF;AACJ,C", "sources": ["webpack://monoova-payments-for-woocommerce/./assets/js/src/hooks/usePayToPaymentContainer.js", "webpack://monoova-payments-for-woocommerce/./assets/js/src/hooks/usePersistentPaymentDetailsContainer.js", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksData\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksRegistry\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcSettings\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"components\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"data\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"element\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"htmlEntities\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"i18n\"]", "webpack://monoova-payments-for-woocommerce/webpack/bootstrap", "webpack://monoova-payments-for-woocommerce/webpack/runtime/compat get default export", "webpack://monoova-payments-for-woocommerce/webpack/runtime/define property getters", "webpack://monoova-payments-for-woocommerce/webpack/runtime/hasOwnProperty shorthand", "webpack://monoova-payments-for-woocommerce/webpack/runtime/make namespace object", "webpack://monoova-payments-for-woocommerce/./assets/js/src/blocks/monoova-payto-block.js"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from \"@wordpress/element\"\nimport { usePersistentPaymentDetailsContainer } from \"./usePersistentPaymentDetailsContainer\"\n\nexport const usePayToPaymentContainer = ({\n    settings,\n    billing,\n    orderId: existingOrderId,\n    containerId = \"payto-payment-container\",\n    paymentMethodId = \"monoova_payto\",\n    hasRequiredInfo = true,\n}) => {\n    // PayTo-specific state management\n    const [paymentMethod, setPaymentMethod] = useState(\"payid\")\n    const [payidValue, setPayidValue] = useState(\"\")\n    const [payidType, setPayidType] = useState(\"\")\n    const [accountName, setAccountName] = useState(\"\")\n    const [bsb, setBsb] = useState(\"\")\n    const [accountNumber, setAccountNumber] = useState(\"\")\n    const [maximumAmount, setMaximumAmount] = useState(settings.maximum_amount || 1000)\n    const [errors, setErrors] = useState({})\n    const [paymentStatus, setPaymentStatus] = useState(\"form\")\n    const [paymentInstructions, setPaymentInstructions] = useState(null)\n    const [isSubmitting, setIsSubmitting] = useState(false)\n    const [pollInterval, setPollInterval] = useState(null)\n    const [hasExpressCheckout, setHasExpressCheckout] = useState(false)\n    const [errorDetails, setErrorDetails] = useState(null)\n    const [mandateInfo, setMandateInfo] = useState(null)\n    const [countdown, setCountdown] = useState(86400)\n\n    // Use refs to prevent duplicate API calls\n    const isProcessingRef = useRef(false)\n    const pollingIntervalRef = useRef(null)\n\n    // Use persistent container management\n    const {\n        targetRef,\n        containerElement,\n        containerIdActive,\n        isContainerInitialized,\n        setContainerInitialized,\n        setOrderData,\n        getOrderData,\n        clearOrderData,\n        showContainer,\n        hideContainer,\n    } = usePersistentPaymentDetailsContainer(paymentMethodId, containerId)\n\n    // Get persistent data\n    const persistentData = getOrderData()\n    const persistentPaymentData = persistentData?.instructions\n    const persistentOrderId = persistentData?.orderId\n\n    // Check if we should use existing data\n    const shouldUseExistingData = isContainerInitialized && persistentPaymentData\n\n    // Stop polling utility\n    const stopPolling = useCallback(() => {\n        if (pollingIntervalRef.current) {\n            clearInterval(pollingIntervalRef.current)\n            pollingIntervalRef.current = null\n        }\n        if (pollInterval) {\n            clearInterval(pollInterval)\n            setPollInterval(null)\n        }\n    }, [pollInterval])\n\n    // Initialize with persistent data if available\n    useEffect(() => {\n        if (shouldUseExistingData && persistentPaymentData) {\n            console.log(\"PayTo: Restoring persistent payment data:\", persistentPaymentData)\n\n            // Restore form state\n            if (persistentPaymentData.formData) {\n                setPaymentMethod(persistentPaymentData.formData.paymentMethod || \"payid\")\n                setPayidValue(persistentPaymentData.formData.payidValue || \"\")\n                setPayidType(persistentPaymentData.formData.payidType || \"\")\n                setAccountName(persistentPaymentData.formData.accountName || \"\")\n                setBsb(persistentPaymentData.formData.bsb || \"\")\n                setAccountNumber(persistentPaymentData.formData.accountNumber || \"\")\n                setMaximumAmount(persistentPaymentData.formData.maximumAmount || settings.maximum_amount || 1000)\n            }\n\n            // Restore payment state\n            if (persistentPaymentData.paymentInstructions) {\n                setPaymentInstructions(persistentPaymentData.paymentInstructions)\n                setPaymentStatus(persistentPaymentData.paymentStatus || \"instructions\")\n            }\n\n            // Restore express checkout state\n            if (persistentPaymentData.hasExpressCheckout !== undefined) {\n                setHasExpressCheckout(persistentPaymentData.hasExpressCheckout)\n            }\n\n            if (persistentPaymentData.mandateInfo) {\n                setMandateInfo(persistentPaymentData.mandateInfo)\n            }\n        }\n    }, [shouldUseExistingData, persistentPaymentData, settings.maximum_amount])\n\n    // Save current state to persistent storage\n    const saveCurrentState = useCallback(() => {\n        const currentState = {\n            formData: {\n                paymentMethod,\n                payidValue,\n                payidType,\n                accountName,\n                bsb,\n                accountNumber,\n                maximumAmount,\n            },\n            paymentInstructions,\n            paymentStatus,\n            hasExpressCheckout,\n            mandateInfo,\n            errors,\n            errorDetails,\n        }\n\n        setOrderData(existingOrderId, null, currentState)\n    }, [\n        paymentMethod,\n        payidValue,\n        payidType,\n        accountName,\n        bsb,\n        accountNumber,\n        maximumAmount,\n        paymentInstructions,\n        paymentStatus,\n        hasExpressCheckout,\n        mandateInfo,\n        errors,\n        errorDetails,\n        existingOrderId,\n        setOrderData,\n    ])\n\n    // Save state whenever it changes\n    useEffect(() => {\n        if (isContainerInitialized) {\n            saveCurrentState()\n        }\n    }, [saveCurrentState, isContainerInitialized])\n\n    // Reset all states\n    const resetStates = useCallback(() => {\n        setPaymentMethod(\"payid\")\n        setPayidValue(\"\")\n        setPayidType(\"\")\n        setAccountName(\"\")\n        setBsb(\"\")\n        setAccountNumber(\"\")\n        setMaximumAmount(settings.maximum_amount || 1000)\n        setErrors({})\n        setPaymentStatus(\"form\")\n        setPaymentInstructions(null)\n        setIsSubmitting(false)\n        setHasExpressCheckout(false)\n        setErrorDetails(null)\n        setMandateInfo(null)\n        setCountdown(86400)\n        stopPolling()\n\n        if (clearOrderData) {\n            clearOrderData()\n        }\n    }, [settings.maximum_amount, stopPolling, clearOrderData])\n\n    // Cleanup on unmount\n    useEffect(() => {\n        return () => {\n            stopPolling()\n        }\n    }, [stopPolling])\n\n    return {\n        // Container management\n        containerRef: targetRef,\n        containerElement,\n        containerIdActive,\n        isInitialized: isContainerInitialized,\n        setContainerInitialized,\n        showContainer,\n        hideContainer,\n\n        // State management\n        paymentMethod,\n        setPaymentMethod,\n        payidValue,\n        setPayidValue,\n        payidType,\n        setPayidType,\n        accountName,\n        setAccountName,\n        bsb,\n        setBsb,\n        accountNumber,\n        setAccountNumber,\n        maximumAmount,\n        setMaximumAmount,\n        errors,\n        setErrors,\n        paymentStatus,\n        setPaymentStatus,\n        paymentInstructions,\n        setPaymentInstructions,\n        isSubmitting,\n        setIsSubmitting,\n        pollInterval,\n        setPollInterval,\n        hasExpressCheckout,\n        setHasExpressCheckout,\n        errorDetails,\n        setErrorDetails,\n        mandateInfo,\n        setMandateInfo,\n        countdown,\n        setCountdown,\n\n        // Utilities\n        resetStates,\n        stopPolling,\n        saveCurrentState,\n\n        // Persistent data\n        persistentData,\n        shouldUseExistingData,\n    }\n}\n", "import { useEffect, useRef } from '@wordpress/element';\n\n// Global container management for persistent Primer checkout\nclass PrimerCheckoutManager {\n    constructor() {\n        this.containers = new Map();\n        this.activeContainers = new Set();\n        this.isInitialized = false;\n    }\n\n    // Create or get existing container\n    getOrCreateContainer(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        \n        if (!this.containers.has(containerKey)) {\n            // Create container element\n            const container = document.createElement('div');\n            container.id = containerId;\n            container.className = 'primer-checkout-persistent-container';\n            container.style.cssText = `\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            `;\n            \n            // Append to body to keep it persistent\n            document.body.appendChild(container);\n            \n            this.containers.set(containerKey, {\n                element: container,\n                isInitialized: false,\n                isVisible: false,\n                orderId: null, // Store orderId for payment completion\n                clientToken: null // Store clientToken as well\n            });\n        }\n        \n        return this.containers.get(containerKey);\n    }\n\n    // Show container in target element\n    showContainer(paymentMethodId, containerId, targetElement) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        \n        if (containerInfo && containerInfo.element) {\n            // Hide all other containers first\n            this.hideAllContainers();\n            \n            // Move container to target and show it\n            if (targetElement) {\n                targetElement.appendChild(containerInfo.element);\n                containerInfo.element.style.cssText = `\n                    position: relative;\n                    top: auto;\n                    left: auto;\n                    width: 100%;\n                    visibility: visible;\n                    opacity: 1;\n                    pointer-events: auto;\n                    transition: all 0.3s ease;\n                `;\n                containerInfo.isVisible = true;\n                this.activeContainers.add(containerKey);\n            }\n        }\n    }\n\n    // Hide container\n    hideContainer(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        \n        if (containerInfo && containerInfo.element) {\n            // Move back to body and hide\n            document.body.appendChild(containerInfo.element);\n            containerInfo.element.style.cssText = `\n                position: absolute;\n                top: -9999px;\n                left: -9999px;\n                width: 100%;\n                visibility: hidden;\n                opacity: 0;\n                pointer-events: none;\n                transition: all 0.3s ease;\n            `;\n            containerInfo.isVisible = false;\n            this.activeContainers.delete(containerKey);\n        }\n    }\n\n    // Hide all containers\n    hideAllContainers() {\n        this.containers.forEach((containerInfo, containerKey) => {\n            if (containerInfo.isVisible) {\n                this.hideContainer(containerKey.split('_')[0], containerKey.split('_')[1]);\n            }\n        });\n    }\n\n    // Clear order data for a specific container\n    clearOrderData(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n\n        if (containerInfo) {\n            containerInfo.orderId = null;\n            containerInfo.clientToken = null;\n            containerInfo.instructions = null; // Also clear instructions\n            containerInfo.isInitialized = false; // Reset initialization state\n        }\n    }\n\n    // Set initialization status\n    setContainerInitialized(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        if (containerInfo) {\n            containerInfo.isInitialized = true;\n        }\n    }\n\n    // Check if container is initialized\n    isContainerInitialized(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        return containerInfo ? containerInfo.isInitialized : false;\n    }\n\n    // Set order and token data\n    setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions = null) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        if (containerInfo) {\n            containerInfo.orderId = orderId;\n            containerInfo.clientToken = clientToken;\n            containerInfo.instructions = instructions; // Add instructions storage (using for PayID/Bank Transfer)\n        } else {\n            console.warn(`[PrimerCheckoutManager] Container ${containerKey} not found for setting order data`);\n        }\n    }\n\n    // Get order data\n    getOrderData(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        const result = containerInfo ? {\n            orderId: containerInfo.orderId,\n            clientToken: containerInfo.clientToken,\n            instructions: containerInfo.instructions // Include instructions in returned data\n        } : { orderId: null, clientToken: null, instructions: null };\n        \n        return result;\n    }\n\n    // Get container element\n    getContainerElement(paymentMethodId, containerId) {\n        const containerKey = `${paymentMethodId}_${containerId}`;\n        const containerInfo = this.containers.get(containerKey);\n        return containerInfo ? containerInfo.element : null;\n    }\n\n    // Cleanup\n    cleanup() {\n        this.containers.forEach((containerInfo) => {\n            if (containerInfo.element && containerInfo.element.parentNode) {\n                containerInfo.element.parentNode.removeChild(containerInfo.element);\n            }\n        });\n        this.containers.clear();\n        this.activeContainers.clear();\n    }\n}\n\n// Global instance\nconst primerCheckoutManager = new PrimerCheckoutManager();\n\n// Hook for persistent container management\nexport const usePersistentPaymentDetailsContainer = (paymentMethodId, containerId) => {\n    const targetRef = useRef(null);\n    const isActiveRef = useRef(false);\n    \n    // Generate persistent container ID\n    const persistentContainerId = `persistent-${paymentMethodId}-${containerId}`;\n\n    useEffect(() => {\n        // Create or get container\n        const containerInfo = primerCheckoutManager.getOrCreateContainer(paymentMethodId, containerId);\n        \n        // Update the container's ID to match what Primer expects\n        if (containerInfo.element) {\n            containerInfo.element.id = persistentContainerId;\n        }\n        \n        // Show container when component mounts\n        if (targetRef.current && !isActiveRef.current) {\n            primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);\n            isActiveRef.current = true;\n        }\n\n        // Cleanup on unmount\n        return () => {\n            if (isActiveRef.current) {\n                primerCheckoutManager.hideContainer(paymentMethodId, containerId);\n                isActiveRef.current = false;\n            }\n        };\n    }, [paymentMethodId, containerId]);\n\n    return {\n        targetRef,\n        containerElement: primerCheckoutManager.getContainerElement(paymentMethodId, containerId),\n        containerIdActive: persistentContainerId, // Return the active container ID for Primer\n        isContainerInitialized: primerCheckoutManager.isContainerInitialized(paymentMethodId, containerId),\n        setContainerInitialized: () => primerCheckoutManager.setContainerInitialized(paymentMethodId, containerId),\n        // Order data persistence methods\n        setOrderData: (orderId, clientToken, instructions = null) => primerCheckoutManager.setOrderData(paymentMethodId, containerId, orderId, clientToken, instructions),\n        getOrderData: () => primerCheckoutManager.getOrderData(paymentMethodId, containerId),\n        clearOrderData: () => {\n            primerCheckoutManager.clearOrderData(paymentMethodId, containerId);\n        },\n        showContainer: () => {\n            if (targetRef.current && !isActiveRef.current) {\n                primerCheckoutManager.showContainer(paymentMethodId, containerId, targetRef.current);\n                isActiveRef.current = true;\n            }\n        },\n        hideContainer: () => {\n            if (isActiveRef.current) {\n                primerCheckoutManager.hideContainer(paymentMethodId, containerId);\n                isActiveRef.current = false;\n            }\n        }\n    };\n};\n\n// Cleanup function for page unload\nif (typeof window !== 'undefined') {\n    window.addEventListener('beforeunload', () => {\n        primerCheckoutManager.cleanup();\n    });\n}\n\nexport default primerCheckoutManager;\n", "module.exports = wc.wcBlocksData;", "module.exports = wc.wcBlocksRegistry;", "module.exports = wc.wcSettings;", "module.exports = window[\"wp\"][\"components\"];", "module.exports = window[\"wp\"][\"data\"];", "module.exports = window[\"wp\"][\"element\"];", "module.exports = window[\"wp\"][\"htmlEntities\"];", "module.exports = window[\"wp\"][\"i18n\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\n * Monoova PayTo Block for WooCommerce Blocks\n *\n * Uses ES6 imports with defensive programming to handle cases where WooCommerce dependencies\n * may not be available (edit mode, missing plugins, etc.)\n */\n\nimport { registerPaymentMethod } from \"@woocommerce/blocks-registry\"\nimport { getSetting } from \"@woocommerce/settings\"\nimport { CHECKOUT_STORE_KEY } from \"@woocommerce/block-data\"\nimport { decodeEntities } from \"@wordpress/html-entities\"\nimport { __ } from \"@wordpress/i18n\"\nimport { useState, useEffect } from \"@wordpress/element\"\nimport { useSelect } from \"@wordpress/data\"\nimport { RadioControl, Button, TextControl } from '@wordpress/components';\n// Import custom hooks\nimport { usePayToPaymentContainer } from \"../hooks/usePayToPaymentContainer\"\n\n// Add CSS animation for spinner and TextControl styling\nconst spinnerStyles = `\n@keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n}\n\n/* Custom styling for TextControl components */\n.components-text-control__input.has-error {\n    border-color: #ef4444 !important;\n}\n\n.components-text-control__input {\n    font-weight: 500;\n    font-size: 16px;\n    line-height: 1.21em;\n    color: #333;\n    padding: 12px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    background-color: #f8f9fa;\n    min-height: 48px;\n    box-sizing: border-box;\n}\n\n.components-text-control__input:focus {\n    border-color: #2CB5C5;\n    box-shadow: 0 0 0 1px #2CB5C5;\n}\n\n.components-text-control__input[readonly] {\n    background-color: #f8f9fa;\n    color: #333;\n    cursor: default;\n}\n\n/* Input adornment styling for currency prefix */\n.text-control-with-prefix {\n    position: relative;\n    display: inline-block;\n    width: 100%;\n}\n\n.text-control-with-prefix .components-text-control__input {\n    padding-left: 24px !important;\n}\n\n.text-control-prefix {\n    position: absolute;\n    left: 10px;\n    top: 20.5%;\n    font-weight: 500;\n    font-size: 16px;\n    color: #333;\n    pointer-events: none;\n    z-index: 1;\n}\n\n/* Error state for prefixed input */\n.text-control-with-prefix .components-text-control__input.has-error + .text-control-prefix,\n.text-control-with-prefix .has-error .components-text-control__input + .text-control-prefix {\n    color: #ef4444;\n}\n`\n\n// Inject the styles into the document head\nif (typeof document !== \"undefined\") {\n    const styleSheet = document.createElement(\"style\")\n    styleSheet.type = \"text/css\"\n    styleSheet.innerText = spinnerStyles\n    document.head.appendChild(styleSheet)\n}\n\n// If registerPaymentMethod is not available, we can't register the payment method\nif (typeof registerPaymentMethod !== \"function\") {\n    console.warn(\n        \"Monoova PayTo Block: registerPaymentMethod not available. Available globals:\",\n        Object.keys(window.wc || {})\n    )\n} else {\n    // Try to get settings\n    let settings = {}\n    if (typeof getSetting === \"function\") {\n        try {\n            settings = getSetting(\"monoova_payto_data\", {})\n        } catch (error) {\n            console.log(\"Monoova PayTo Block: getSetting failed:\", error)\n        }\n    }\n\n    // Fallback to global variable if getSetting didn't work\n    if (!settings || Object.keys(settings).length === 0) {\n        settings = window.monoova_payto_blocks_params || {}\n        console.log(\"Monoova PayTo Block: Using fallback settings:\", settings)\n    }\n\n    // Set defaults if no settings available\n    if (!settings || Object.keys(settings).length === 0) {\n        console.warn(\"Monoova PayTo Block: No settings found, using defaults\")\n        settings = {\n            title: \"PayTo\",\n            description: \"Set up PayTo directly from your bank using BSB and Account Number or PayID.\",\n            supports: [],\n        }\n    }\n\n    const defaultLabel = __(\"PayTo\", \"monoova-payments-for-woocommerce\")\n    const label = decodeEntities(settings.title) || defaultLabel\n\n    /**\n     * PayTo Payment Form Component\n     */\n    const PayToForm = ({ eventRegistration, emitResponse, billing }) => {\n        // Get current order ID from WooCommerce checkout store\n        const { orderId } = useSelect(select => {\n            const store = select(CHECKOUT_STORE_KEY)\n            return {\n                orderId: store.getOrderId(),\n            }\n        })\n\n        // Helper function to check if required guest information is available\n        const hasRequiredGuestInfo = () => {\n            const billingAddress = billing?.billingAddress;\n            if (!billingAddress) return false;\n\n            // Required fields for guest customers to generate client token\n            return !!(\n                billingAddress.email &&\n                billingAddress.first_name &&\n                billingAddress.last_name &&\n                billingAddress.address_1 &&\n                billingAddress.city &&\n                billingAddress.postcode &&\n                billingAddress.country &&\n                billingAddress.state &&\n                billingAddress.city\n            );\n        };\n\n        // Use the PayTo payment container hook for persistent state management\n        const {\n            containerRef,\n            isInitialized,\n            setContainerInitialized,\n\n            // State management from hook\n            paymentMethod,\n            setPaymentMethod,\n            payidValue,\n            setPayidValue,\n            payidType,\n            setPayidType,\n            accountName,\n            setAccountName,\n            bsb,\n            setBsb,\n            accountNumber,\n            setAccountNumber,\n            maximumAmount,\n            setMaximumAmount,\n            errors,\n            setErrors,\n            paymentStatus,\n            setPaymentStatus,\n            paymentInstructions,\n            setPaymentInstructions,\n            isSubmitting,\n            setIsSubmitting,\n            pollInterval,\n            setPollInterval,\n            hasExpressCheckout,\n            setHasExpressCheckout,\n            errorDetails,\n            setErrorDetails,\n            mandateInfo,\n            setMandateInfo,\n            countdown,\n            setCountdown,\n\n            // Utilities\n            resetStates,\n            stopPolling,\n            saveCurrentState,\n        } = usePayToPaymentContainer({\n            settings,\n            billing,\n            orderId,\n            containerId: \"payto-payment-container\",\n            paymentMethodId: \"monoova_payto\",\n            hasRequiredInfo: hasRequiredGuestInfo(),\n        })\n\n        // Get cart totals from WooCommerce\n        const cartTotals = useSelect(select => {\n            const store = select(\"wc/store/cart\")\n            if (store && store.getCartTotals) {\n                return store.getCartTotals()\n            }\n            return null\n        }, [])\n\n        useEffect(() => {\n            // Selector for the block checkout's \"Place Order\" button\n            const placeOrderButton = document.querySelector(\n                \".wc-block-components-button.wp-element-button.wc-block-components-checkout-place-order-button\"\n            )\n\n            if (placeOrderButton) {\n                // Hide the button when Monoova PayTo is selected\n                placeOrderButton.style.display = \"none\"\n            }\n\n            // Cleanup function: runs when component unmounts (e.g., user selects another payment method)\n            return () => {\n                if (placeOrderButton) {\n                    // Restore the button's default display style\n                    placeOrderButton.style.display = \"\"\n                }\n            }\n        }, [])\n\n        // Get order total amount\n        const orderTotal = cartTotals?.total_price ? parseFloat(cartTotals.total_price) / 100 : 0\n        const currency = cartTotals?.currency_code || settings.currency || \"AUD\"\n\n        const { onPaymentSetup } = eventRegistration\n        const { responseTypes, noticeContexts } = emitResponse\n\n        // Helper function to get status colors\n        const getStatusColor = status => {\n            const statusLower = status?.toLowerCase() || \"\"\n            switch (statusLower) {\n                case \"completed\":\n                case \"success\":\n                case \"authorized\":\n                    return { background: \"#dcfce7\", text: \"#166534\" }\n                case \"processing\":\n                case \"pending\":\n                case \"created\":\n                    return { background: \"#fef3c7\", text: \"#92400e\" }\n                case \"failed\":\n                case \"cancelled\":\n                case \"rejected\":\n                    return { background: \"#fee2e2\", text: \"#991b1b\" }\n                default:\n                    return { background: \"#f3f4f6\", text: \"#374151\" }\n            }\n        }\n\n        // Check for express checkout availability on mount\n        useEffect(() => {\n            console.log(\"PayTo: Component mounted, checking express checkout availability...\")\n            checkExpressCheckoutAvailability()\n        }, [])\n\n        // Cleanup polling on unmount\n        useEffect(() => {\n            return () => {\n                if (pollInterval) {\n                    clearInterval(pollInterval)\n                }\n            }\n        }, [pollInterval])\n\n        // Countdown timer effect\n        useEffect(() => {\n            const timer = setInterval(() => {\n                setCountdown(prev => {\n                    if (prev <= 1) {\n                        clearInterval(timer)\n                        return 0\n                    }\n                    return prev - 1\n                })\n            }, 1000)\n\n            return () => clearInterval(timer)\n        }, [])\n\n        // Check if express checkout is available\n        const checkExpressCheckoutAvailability = async () => {\n            if (!settings.is_user_logged_in) {\n                console.log(\"PayTo: User not logged in, skipping express checkout check\")\n                return\n            }\n\n            try {\n                const response = await fetch(settings.ajax_url || \"/wp-admin/admin-ajax.php\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/x-www-form-urlencoded\",\n                    },\n                    body: new URLSearchParams({\n                        action: \"monoova_check_express_checkout\",\n                        nonce: settings.nonce,\n                    }),\n                })\n\n                const result = await response.json()\n\n                if (result.success && result.data.available) {\n                    setHasExpressCheckout(true)\n                    // Store mandate details for display\n                    if (result.data.mandate_info) {\n                        setMandateInfo(result.data.mandate_info)\n                    }\n                } else {\n                    console.log(\"Express checkout not available or failed:\", result)\n                }\n            } catch (error) {\n                console.error(\"Error checking express checkout availability:\", error)\n            }\n        }\n\n        // Handle checkout with existing mandate\n        const handleCheckoutWithExistingMandate = async () => {\n            setIsSubmitting(true)\n            setPaymentStatus(\"processing\")\n            setErrors({}) // Clear previous errors\n            setErrorDetails(null) // Clear previous error details\n\n            try {\n                const response = await fetch(settings.ajax_url || \"/wp-admin/admin-ajax.php\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/x-www-form-urlencoded\",\n                    },\n                    body: new URLSearchParams({\n                        action: \"monoova_process_payment_with_existing_mandate\",\n                        nonce: settings.nonce,\n                        order_id: orderId,\n                        payment_agreement_uid: mandateInfo?.agreement_id,\n                    }),\n                })\n\n                const result = await response.json()\n\n                if (result.success) {\n                    setPaymentInstructions({\n                        order_id: result.data.order_id,\n                        currency: currency,\n                        amount: orderTotal.toFixed(2),\n                        status: \"Processing\",\n                        agreement_reference: result.data.order_id,\n                        message: \"Payment initiated with existing mandate\",\n                    })\n                    setPaymentStatus(\"instructions\")\n                    startPaymentStatusPolling(result.data.order_id)\n                } else {\n                    const errorMessage = result.data?.message || \"Express checkout failed\"\n                    const errorCode = result.data?.error_code || \"UNKNOWN_ERROR\"\n                    const errorType = result.data?.error_type || \"EXPRESS_CHECKOUT_ERROR\"\n\n                    // Check if this is an agreement limit error\n                    if (errorCode === \"payto_agreement_limit_exceeded\" || errorType === \"AGREEMENT_LIMIT_ERROR\") {\n                        setPaymentStatus(\"agreement_limit_exceeded\")\n                        setErrors({ general: errorMessage })\n                        setErrorDetails({\n                            message: errorMessage,\n                            code: \"AGREEMENT_LIMIT_EXCEEDED\",\n                            type: \"AGREEMENT_LIMIT_ERROR\",\n                            context: \"express_checkout\",\n                            requires_new_agreement: true,\n                            limit_errors: result.data?.errors || [],\n                            agreement_uid: result.data?.agreement_uid,\n                        })\n                    } else {\n                        setPaymentStatus(\"failed\")\n                        setErrors({ general: errorMessage })\n                        setErrorDetails({\n                            message: errorMessage,\n                            code: errorCode,\n                            type: errorType,\n                            context: \"express_checkout\",\n                            details: result.data?.error_details || null,\n                        })\n                    }\n                }\n            } catch (error) {\n                setPaymentStatus(\"failed\")\n                setErrors({ general: \"Network error occurred during express checkout\" })\n                setErrorDetails({\n                    message: \"Network error occurred during express checkout\",\n                    code: \"NETWORK_ERROR\",\n                    type: \"CONNECTION_ERROR\",\n                    context: \"express_checkout\",\n                    details: error.message,\n                })\n            } finally {\n                setIsSubmitting(false)\n            }\n        }\n\n        // Note: We're using a custom \"Accept and Continue\" button instead of the default place order button\n\n        // PayID validation and auto-detection\n        const validatePayID = value => {\n            if (!value.trim()) {\n                setPayidType(\"\")\n                setErrors(prev => ({ ...prev, payidValue: \"\" }))\n                return\n            }\n\n            // Email regex pattern\n            const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n            // Phone regex pattern (Australian format)\n            const phonePattern = /^(\\+61|0)[0-9]{9}$/\n\n            if (emailPattern.test(value)) {\n                setPayidType(\"Email\")\n                setErrors(prev => ({ ...prev, payidValue: \"\" }))\n            } else if (phonePattern.test(value)) {\n                setPayidType(\"PhoneNumber\")\n                setErrors(prev => ({ ...prev, payidValue: \"\" }))\n            } else {\n                setPayidType(\"\")\n                setErrors(prev => ({\n                    ...prev,\n                    payidValue: __(\n                        \"Please enter a valid email address or phone number.\",\n                        \"monoova-payments-for-woocommerce\"\n                    ),\n                }))\n            }\n        }\n\n        // Validation function\n        const validateForm = () => {\n            const newErrors = {}\n\n            if (paymentMethod === \"payid\") {\n                if (!payidValue.trim()) {\n                    newErrors.payidValue = __(\"PayID is required.\", \"monoova-payments-for-woocommerce\")\n                } else if (!payidType) {\n                    newErrors.payidValue = __(\n                        \"Please enter a valid email address or phone number.\",\n                        \"monoova-payments-for-woocommerce\"\n                    )\n                }\n            } else {\n                if (!accountName.trim()) {\n                    newErrors.accountName = __(\"Account name is required.\", \"monoova-payments-for-woocommerce\")\n                }\n                if (!bsb.trim()) {\n                    newErrors.bsb = __(\"BSB is required.\", \"monoova-payments-for-woocommerce\")\n                } else if (!/^\\d{3}-?\\d{3}$/.test(bsb.trim())) {\n                    newErrors.bsb = __(\"Please enter a valid BSB (6 digits).\", \"monoova-payments-for-woocommerce\")\n                }\n                if (!accountNumber.trim()) {\n                    newErrors.accountNumber = __(\"Account number is required.\", \"monoova-payments-for-woocommerce\")\n                }\n            }\n\n            // Validate maximum amount\n            const numericMaxAmount = parseFloat(maximumAmount)\n            if (!maximumAmount || maximumAmount === '' || isNaN(numericMaxAmount) || numericMaxAmount <= 0) {\n                newErrors.maximumAmount = __(\n                    \"Maximum amount must be greater than 0.\",\n                    \"monoova-payments-for-woocommerce\"\n                )\n            }\n\n            setErrors(newErrors)\n            return Object.keys(newErrors).length === 0\n        }\n\n        // Register payment setup handler\n        React.useEffect(() => {\n            const unsubscribe = onPaymentSetup(() => {\n                if (!validateForm()) {\n                    return {\n                        type: responseTypes.ERROR,\n                        message: __(\"Please correct the errors in the PayTo form.\", \"monoova-payments-for-woocommerce\"),\n                        messageContext: noticeContexts.PAYMENTS,\n                    }\n                }\n\n                // Return payment data\n                return {\n                    type: responseTypes.SUCCESS,\n                    meta: {\n                        paymentMethodData: {\n                            payto_payment_method: paymentMethod,\n                            payto_payid_type: paymentMethod === \"payid\" ? payidType : \"\",\n                            payto_payid_value: paymentMethod === \"payid\" ? payidValue : \"\",\n                            payto_account_name: paymentMethod === \"bsb_account\" ? accountName : \"\",\n                            payto_bsb: paymentMethod === \"bsb_account\" ? bsb : \"\",\n                            payto_account_number: paymentMethod === \"bsb_account\" ? accountNumber : \"\",\n                            payto_maximum_amount: maximumAmount.toString(),\n                        },\n                    },\n                }\n            })\n\n            return unsubscribe\n        }, [\n            onPaymentSetup,\n            paymentMethod,\n            payidType,\n            payidValue,\n            accountName,\n            bsb,\n            accountNumber,\n            maximumAmount,\n            validateForm,\n        ])\n\n        // Custom submit handler for PayTo\n        const handlePayToSubmit = async event => {\n            if (event) {\n                event.preventDefault()\n                event.stopPropagation()\n            }\n\n            if (!validateForm()) {\n                return\n            }\n\n            setIsSubmitting(true)\n            setPaymentStatus(\"processing\")\n            setErrors({}) // Clear previous errors\n            setErrorDetails(null) // Clear previous error details\n\n            // Clear mandate info when creating new agreement\n            setMandateInfo(null)\n            setHasExpressCheckout(false)\n\n            try {\n                // Get billing data from props (provided by WooCommerce Blocks)\n                let billingInfo = billing?.billingAddress || {}\n                const response = await fetch(settings.ajax_url || \"/wp-admin/admin-ajax.php\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/x-www-form-urlencoded\",\n                    },\n                    body: new URLSearchParams({\n                        action: \"monoova_create_payto_agreement\",\n                        nonce: settings.nonce,\n                        payto_payment_method: paymentMethod,\n                        payto_payid_type: paymentMethod === \"payid\" ? payidType : \"\",\n                        payto_payid_value: paymentMethod === \"payid\" ? payidValue : \"\",\n                        payto_account_name: paymentMethod === \"bsb_account\" ? accountName : \"\",\n                        payto_bsb: paymentMethod === \"bsb_account\" ? bsb : \"\",\n                        payto_account_number: paymentMethod === \"bsb_account\" ? accountNumber : \"\",\n                        payto_maximum_amount: maximumAmount.toString(),\n                        billing_first_name: billingInfo.first_name || \"\",\n                        billing_last_name: billingInfo.last_name || \"\",\n                        billing_email: billingInfo.email || \"\",\n                        order_id: orderId,\n                        isCheckoutPage: true,\n                    }),\n                })\n\n                const result = await response.json()\n\n                if (result.success) {\n                    setPaymentInstructions({\n                        order_id: result.data.order_id,\n                        order_key: result.data.order_key,\n                        order_received_url: result.data.order_received_url,\n                        currency: currency,\n                        amount: orderTotal.toFixed(2),\n                        status: \"Processing\",\n                        agreement_reference: result.data.order_id,\n                        message: \"Payment agreement created successfully\",\n                    })\n                    setPaymentStatus(\"instructions\")\n                    startPaymentStatusPolling(result.data.order_id)\n                } else {\n                    const errorMessage = result.data?.message || \"Failed to create payment agreement\"\n                    const errorCode = result.data?.error_code || \"UNKNOWN_ERROR\"\n                    const errorType = result.data?.error_type || \"AGREEMENT_CREATION_ERROR\"\n\n                    // Reset payment status to allow user to try again\n                    setPaymentStatus(\"failed\")\n                    setErrors({ general: errorMessage })\n                    setErrorDetails({\n                        message: errorMessage,\n                        code: errorCode,\n                        type: errorType,\n                        context: \"agreement_creation\",\n                        details: result.data?.error_details || null,\n                        validation_errors: result.data?.validation_errors || null,\n                    })\n                }\n            } catch (error) {\n                setPaymentStatus(\"failed\")\n                setErrors({ general: \"Network error occurred while creating payment agreement\" })\n                setErrorDetails({\n                    message: \"Network error occurred while creating payment agreement\",\n                    code: \"NETWORK_ERROR\",\n                    type: \"CONNECTION_ERROR\",\n                    context: \"agreement_creation\",\n                    details: error.message,\n                })\n            } finally {\n                setIsSubmitting(false)\n            }\n        }\n\n        // Reset payment agreement when limits exceeded\n        const resetPaymentAgreement = async () => {\n            setIsSubmitting(true)\n            setErrors({})\n            setErrorDetails(null)\n\n            try {\n                const billingInfo = billing?.billingAddress || {}\n                const response = await fetch(settings.ajax_url || \"/wp-admin/admin-ajax.php\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/x-www-form-urlencoded\",\n                    },\n                    body: new URLSearchParams({\n                        action: \"monoova_payto_reset_agreement\",\n                        nonce: settings.nonce,\n                        order_id: paymentInstructions?.order_id || orderId,\n                        customer_email: billingInfo.email || \"\",\n                    }),\n                })\n\n                const result = await response.json()\n\n                if (result.success) {\n                    // Reset all payment-related state\n                    setPaymentInstructions(null)\n                    setPaymentStatus(\"form\")\n                    setErrors({})\n                    setErrorDetails(null)\n\n                    // Reset form fields to allow new agreement creation\n                    setPaymentMethod(\"payid\")\n                    setPayidType(\"email\")\n                    setPayidValue(\"\")\n                    setAccountName(\"\")\n                    setBsb(\"\")\n                    setAccountNumber(\"\")\n                    setMaximumAmount(orderTotal)\n                } else {\n                    setErrors({ general: result.data?.message || \"Failed to reset payment agreement\" })\n                }\n            } catch (error) {\n                setErrors({ general: \"Network error occurred while resetting payment agreement\" })\n            } finally {\n                setIsSubmitting(false)\n            }\n        }\n\n        // Payment status polling with auto-redirect (5-second interval)\n        const startPaymentStatusPolling = orderId => {\n            const interval = setInterval(async () => {\n                try {\n                    const response = await fetch(settings.ajax_url || \"/wp-admin/admin-ajax.php\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/x-www-form-urlencoded\",\n                        },\n                        body: new URLSearchParams({\n                            action: \"get_payto_agreement_payment_initiation_status\",\n                            nonce: settings.nonce,\n                            order_id: orderId,\n                        }),\n                    })\n\n                    const result = await response.json()\n\n                    if (result.success) {\n                        const agreementStatus = result.data.agreement_status || result.data.mandate_status\n                        const paymentStatus = result.data.payment_initiation_status\n                        const orderStatus = result.data.order_status\n                        const errorDetails = result.data.error_details\n\n                        console.log(\"PayTo polling status:\", {\n                            agreementStatus,\n                            paymentStatus,\n                            orderStatus,\n                            errorDetails,\n                        })\n\n                        // Check for agreement limit errors first\n                        if (errorDetails && errorDetails.requires_new_agreement) {\n                            clearInterval(interval)\n                            setPollInterval(null)\n                            setPaymentStatus(\"agreement_limit_exceeded\")\n\n                            const limitErrors = errorDetails.errors || []\n                            const errorMessages = limitErrors.map(err => err.message).join(\". \")\n\n                            setErrors({ general: errorMessages })\n                            setErrorDetails({\n                                message: errorMessages,\n                                code: \"AGREEMENT_LIMIT_EXCEEDED\",\n                                type: \"AGREEMENT_LIMIT_ERROR\",\n                                context: \"payment_initiation\",\n                                agreement_uid: errorDetails.agreement_uid,\n                                requires_new_agreement: true,\n                                limit_errors: limitErrors,\n                            })\n                            return\n                        }\n\n                        // Update payment instructions with current status\n                        if (paymentInstructions) {\n                            setPaymentInstructions(prev => ({\n                                ...prev,\n                                status: agreementStatus || paymentStatus || \"Processing\",\n                                agreement_status: agreementStatus,\n                                payment_initiation_status: paymentStatus,\n                                order_status: orderStatus,\n                                order_key: result.data.order_key || prev.order_key,\n                                order_received_url: result.data.order_received_url || prev.order_received_url,\n                            }))\n                        }\n\n                        // Check for completed payment\n                        if (\n                            paymentStatus === \"completed\" ||\n                            paymentStatus === \"success\" ||\n                            orderStatus === \"completed\" ||\n                            orderStatus === \"processing\"\n                        ) {\n                            clearInterval(interval)\n                            setPollInterval(null)\n                            setPaymentStatus(\"success\")\n\n                            // Auto-redirect to order received page (like PayID implementation)\n                            setTimeout(() => {\n                                const orderReceivedUrl =\n                                    result.data.order_received_url || paymentInstructions?.order_received_url\n                                if (orderReceivedUrl) {\n                                    window.location.href = orderReceivedUrl\n                                }\n                            }, 2000) // 2 second delay to show success message\n                        } else if (\n                            paymentStatus === \"failed\" ||\n                            paymentStatus === \"cancelled\" ||\n                            agreementStatus === \"failed\" ||\n                            agreementStatus === \"cancelled\" ||\n                            agreementStatus === \"rejected\"\n                        ) {\n                            clearInterval(interval)\n                            setPollInterval(null)\n\n                            // Check if this is an agreement limit error\n                            if (\n                                errorDetails?.code === \"payto_agreement_limit_exceeded\" ||\n                                errorDetails?.type === \"AGREEMENT_LIMIT_ERROR\" ||\n                                result.data.error_code === \"payto_agreement_limit_exceeded\"\n                            ) {\n                                setPaymentStatus(\"agreement_limit_exceeded\")\n                                setErrors({\n                                    general:\n                                        errorDetails?.message ||\n                                        result.data.message ||\n                                        \"Payment agreement limits have been exceeded\",\n                                })\n                                setErrorDetails({\n                                    message:\n                                        errorDetails?.message ||\n                                        result.data.message ||\n                                        \"Payment agreement limits have been exceeded\",\n                                    code: \"AGREEMENT_LIMIT_EXCEEDED\",\n                                    type: \"AGREEMENT_LIMIT_ERROR\",\n                                    context: \"payment_polling\",\n                                    requires_new_agreement: true,\n                                    limit_errors: result.data?.errors || errorDetails?.limit_errors || [],\n                                    agreement_status: agreementStatus,\n                                    payment_status: paymentStatus,\n                                    order_status: orderStatus,\n                                })\n                            } else {\n                                setPaymentStatus(\"failed\")\n\n                                // Set detailed error information based on status\n                                let errorMessage = \"Payment was cancelled or failed\"\n                                let errorCode = \"UNKNOWN_ERROR\"\n                                let errorType = \"PAYMENT_FAILED\"\n\n                                if (agreementStatus === \"rejected\") {\n                                    errorMessage = \"PayTo agreement was rejected by your bank\"\n                                    errorCode = \"AGREEMENT_REJECTED\"\n                                    errorType = \"AGREEMENT_ERROR\"\n                                } else if (agreementStatus === \"cancelled\") {\n                                    errorMessage = \"PayTo agreement was cancelled\"\n                                    errorCode = \"AGREEMENT_CANCELLED\"\n                                    errorType = \"AGREEMENT_ERROR\"\n                                } else if (agreementStatus === \"failed\") {\n                                    errorMessage = \"PayTo agreement creation failed\"\n                                    errorCode = \"AGREEMENT_FAILED\"\n                                    errorType = \"AGREEMENT_ERROR\"\n                                } else if (paymentStatus === \"failed\") {\n                                    errorMessage = \"Payment initiation failed\"\n                                    errorCode = \"PAYMENT_FAILED\"\n                                    errorType = \"PAYMENT_ERROR\"\n                                } else if (paymentStatus === \"cancelled\") {\n                                    errorMessage = \"Payment was cancelled\"\n                                    errorCode = \"PAYMENT_CANCELLED\"\n                                    errorType = \"PAYMENT_ERROR\"\n                                }\n\n                                setErrors({ general: errorMessage })\n                                setErrorDetails({\n                                    message: errorMessage,\n                                    code: errorCode,\n                                    type: errorType,\n                                    context: \"payment_polling\",\n                                    agreement_status: agreementStatus,\n                                    payment_status: paymentStatus,\n                                    order_status: orderStatus,\n                                })\n                            }\n                        }\n                        // For \"pending\", \"created\", \"authorized\" or other statuses, continue polling\n                    }\n                } catch (error) {\n                    console.error(\"Error checking payment status:\", error)\n                }\n            }, 5000) // Poll every 5 seconds\n\n            setPollInterval(interval)\n        }\n\n        // Render payment instructions\n        const renderPaymentInstructions = () => {\n            if (!paymentInstructions) return null\n\n            return (\n                <div className=\"monoova-payto-instructions-wrapper monoova-payid-bank-transfer-instructions-wrapper\">\n                    {/* Payment Amount */}\n                    <div\n                        style={{\n                            textAlign: \"center\",\n                            marginTop: \"24px\",\n                        }}>\n                        <div className=\"monoova-scan-pay\">\n                            <div className=\"pay-label\">\n                                Pay\n                            </div>\n                            <div class=\"amount\">\n                                <span class=\"woocommerce-Price-amount amount\">\n                                    <bdi>\n                                        <span class=\"woocommerce-Price-currencySymbol\">$</span>\n                                        {paymentInstructions.amount}\n                                    </bdi>\n                                </span>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Payment Method Selection */}\n                    <div>\n                        <div className=\"monoova-instruction-method-selection\">\n                            <div style={{ fontWeight: 600 }}>\n                                Pay with\n                            </div>\n                            \n                            <div style={{ display: \"flex\", gap: \"16px\", alignItems: \"center\" }}>\n                                <label\n                                    style={{\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        cursor: \"pointer\",\n                                        gap: \"8px\",\n                                    }}>\n                                    <div style={{ position: \"relative\" }}>\n                                        <div\n                                            style={{\n                                                width: \"24px\",\n                                                height: \"24px\",\n                                                border: `1.5px solid ${\n                                                    paymentMethod === \"payid\" ? \"#2CB5C5\" : \"#ABABAB\"\n                                                }`,\n                                                borderRadius: \"50%\",\n                                                position: \"relative\",\n                                            }}>\n                                            {paymentMethod === \"payid\" && (\n                                                <div\n                                                    style={{\n                                                        width: \"14px\",\n                                                        height: \"14px\",\n                                                        backgroundColor: \"#2CB5C5\",\n                                                        borderRadius: \"50%\",\n                                                        position: \"absolute\",\n                                                        top: \"50%\",\n                                                        left: \"50%\",\n                                                        transform: \"translate(-50%, -50%)\",\n                                                    }}></div>\n                                            )}\n                                        </div>\n                                    </div>\n                                    <span\n                                        style={{\n                                            fontWeight: \"400\",\n                                            fontSize: \"16px\",\n                                            lineHeight: \"1.5em\",\n                                            color: \"#333\",\n                                        }}>\n                                        PayID\n                                    </span>\n                                </label>\n                                <label\n                                    style={{\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        cursor: \"pointer\",\n                                        gap: \"8px\",\n                                    }}>\n                                    <div style={{ position: \"relative\" }}>\n                                        <div\n                                            style={{\n                                                width: \"24px\",\n                                                height: \"24px\",\n                                                border: `1.5px solid ${\n                                                    paymentMethod === \"bsb_account\" ? \"#2CB5C5\" : \"#ABABAB\"\n                                                }`,\n                                                borderRadius: \"50%\",\n                                                position: \"relative\",\n                                            }}>\n                                            {paymentMethod === \"bsb_account\" && (\n                                                <div\n                                                    style={{\n                                                        width: \"14px\",\n                                                        height: \"14px\",\n                                                        backgroundColor: \"#2CB5C5\",\n                                                        borderRadius: \"50%\",\n                                                        position: \"absolute\",\n                                                        top: \"50%\",\n                                                        left: \"50%\",\n                                                        transform: \"translate(-50%, -50%)\",\n                                                    }}></div>\n                                            )}\n                                        </div>\n                                    </div>\n                                    <span\n                                        style={{\n                                            fontWeight: \"400\",\n                                            fontSize: \"16px\",\n                                            lineHeight: \"1.5em\",\n                                            color: \"#333\",\n                                        }}>\n                                        BSB and account number\n                                    </span>\n                                </label>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Form Fields Container */}\n                    <div\n                        style={{\n                            border: \"1px solid #E8E8E8\",\n                            borderRadius: \"16px\",\n                            padding: \"24px 24px 32px\",\n                            gap: \"40px\",\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            alignItems: \"center\",\n                        }}>\n                        {/* PayID Fields */}\n                        {paymentMethod === \"payid\" && (\n                            <div style={{ width: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\n                                <div style={{ display: \"flex\", alignItems: \"center\" }}>\n                                    <label\n                                        style={{\n                                            fontWeight: \"600\",\n                                            fontSize: \"16px\",\n                                            lineHeight: \"1.5em\",\n                                            color: \"#000000\",\n                                        }}>\n                                        Enter your PayID\n                                    </label>\n                                </div>\n                                <TextControl\n                                    value={payidValue || \"\"}\n                                    readOnly\n                                    placeholder=\"0412 345 678\"\n                                />\n                            </div>\n                        )}\n\n                        {/* BSB and Account Fields */}\n                        {paymentMethod === \"bsb_account\" && (\n                            <>\n                                {/* Account Name Field */}\n                                <div style={{ width: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\n                                    <div style={{ display: \"flex\", alignItems: \"center\" }}>\n                                        <label\n                                            style={{\n                                                fontWeight: \"600\",\n                                                fontSize: \"16px\",\n                                                lineHeight: \"1.5em\",\n                                                color: \"#000000\",\n                                                display: \"block\",\n                                                marginBottom: \"4px\",\n                                            }}>\n                                            Name associated with bank account\n                                        </label>\n                                    </div>\n                                    <TextControl\n                                        value={accountName || \"\"}\n                                        readOnly\n                                        placeholder=\"Enter your name\"\n                                    />\n                                </div>\n\n                                {/* BSB Field */}\n                                <div style={{ width: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\n                                    <div style={{ display: \"flex\", alignItems: \"center\" }}>\n                                        <label\n                                            style={{\n                                                fontWeight: \"500\",\n                                                fontSize: \"12px\",\n                                                lineHeight: \"1.5em\",\n                                                color: \"#666\",\n                                                display: \"block\",\n                                                marginBottom: \"4px\",\n                                            }}>\n                                            BSB\n                                        </label>\n                                    </div>\n                                    <TextControl\n                                        value={bsb || \"\"}\n                                        readOnly\n                                        placeholder=\"123-456\"\n                                    />\n                                </div>\n\n                                {/* Account Number Field */}\n                                <div style={{ width: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\n                                    <div style={{ display: \"flex\", alignItems: \"center\" }}>\n                                        <label\n                                            style={{\n                                                fontWeight: \"500\",\n                                                fontSize: \"12px\",\n                                                lineHeight: \"1.5em\",\n                                                color: \"#666\",\n                                                display: \"block\",\n                                                marginBottom: \"4px\",\n                                            }}>\n                                            Account Number\n                                        </label>\n                                    </div>\n                                    <TextControl\n                                        value={accountNumber || \"\"}\n                                        readOnly\n                                        placeholder=\"Enter your account number\"\n                                    />\n                                </div>\n                            </>\n                        )}\n\n                        {/* Maximum Amount Field */}\n                        <div style={{ width: \"100%\", display: \"flex\", flexDirection: \"column\", gap: \"12px\" }}>\n                            <div style={{ display: \"flex\", flexDirection: \"column\" }}>\n                                <div style={{ display: \"flex\", alignItems: \"center\" }}>\n                                    <label\n                                        style={{\n                                            fontWeight: \"600\",\n                                            fontSize: \"12px\",\n                                            lineHeight: \"1.5em\",\n                                            color: \"#666\",\n                                            display: \"block\",\n                                            marginBottom: \"4px\",\n                                        }}>\n                                        Maximum payment agreement amount\n                                    </label>\n                                </div>\n                                {/* <TextControl\n                                    value={`$${maximumAmount || 1000}`}\n                                    readOnly\n                                /> */}\n                                <div className=\"text-control-with-prefix\">\n                                    <span className=\"text-control-prefix\">$</span>\n                                    <TextControl\n                                        value={`${parseFloat(maximumAmount) || 1000}`}\n                                        readOnly\n                                    />\n                                </div>\n                            </div>\n                            <div\n                                style={{\n                                    fontWeight: \"400\",\n                                    fontSize: \"14px\",\n                                    lineHeight: \"1.5\",\n                                    color: \"#555\",\n                                }}>\n                                This is the maximum amount that can be charged under this PayTo agreement. You can\n                                modify this amount if needed.\n                            </div>\n                        </div>\n\n                        {/* Accept and Continue Button (Disabled) */}\n                        <Button\n                            style={{\n                                width: \"100%\",\n                                display: \"flex\",\n                                justifyContent: \"center\",\n                                alignItems: \"center\",\n                                gap: \"10px\",\n                                backgroundColor: \"#2CB5C5\",\n                                borderRadius: \"8px\",\n                                minHeight: \"48px\",\n                                opacity: 0.6,\n                                cursor: \"not-allowed\",\n                            }}>\n                            <span\n                                style={{\n                                    fontWeight: \"700\",\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.21em\",\n                                    color: \"#000000\",\n                                }}>\n                                Accept and Continue\n                            </span>\n                        </Button>\n\n                        {/* Payment Status Section */}\n                        <div style={{ width: \"100%\" }}>\n                            <h4\n                                style={{\n                                    fontSize: \"18px\",\n                                    fontWeight: \"600\",\n                                    color: \"#333\",\n                                    marginBottom: \"16px\",\n                                }}>\n                                Authorise recurring payments\n                            </h4>\n\n                            {/* Current payment status from polling */}\n                            {(() => {\n                                const currentStatus = paymentInstructions?.status?.toLowerCase() || \"pending\"\n                                const agreementStatus =\n                                    paymentInstructions?.agreement_status?.toLowerCase() || \"pending\"\n                                const paymentStatus =\n                                    paymentInstructions?.payment_initiation_status?.toLowerCase() || \"pending\"\n\n                                // 3. Done State - Payment completed/successful (final state)\n                                if (\n                                    paymentStatus === \"completed\" ||\n                                    paymentStatus === \"success\" ||\n                                    paymentStatus === \"processed\"\n                                ) {\n                                    return (\n                                        <div>\n                                            {/* Payment received - completed */}\n                                            <div style={{ marginBottom: \"16px\" }}>\n                                                <div\n                                                    style={{\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"12px\",\n                                                        borderRadius: \"8px\",\n                                                        marginBottom: \"8px\",\n                                                    }}>\n                                                    <div\n                                                        style={{\n                                                            width: \"20px\",\n                                                            height: \"20px\",\n                                                            borderRadius: \"50%\",\n                                                            backgroundColor: \"#2CB5C5\",\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            flexShrink: 0,\n                                                        }}>\n                                                        <svg\n                                                            width=\"12\"\n                                                            height=\"9\"\n                                                            viewBox=\"0 0 12 9\"\n                                                            fill=\"none\"\n                                                            xmlns=\"http://www.w3.org/2000/svg\">\n                                                            <path\n                                                                d=\"M1 4.5L4.5 8L11 1.5\"\n                                                                stroke=\"#ffffff\"\n                                                                strokeWidth=\"2\"\n                                                                strokeLinecap=\"round\"\n                                                                strokeLinejoin=\"round\"\n                                                            />\n                                                        </svg>\n                                                    </div>\n                                                    <span\n                                                        style={{\n                                                            fontSize: \"16px\",\n                                                            color: \"#484848\",\n                                                            fontWeight: \"400\",\n                                                        }}>\n                                                        Payment received\n                                                    </span>\n                                                </div>\n                                            </div>\n\n                                            {/* Redirect notification */}\n                                            <div\n                                                style={{\n                                                    display: \"flex\",\n                                                    alignItems: \"flex-start\",\n                                                    gap: \"12px\",\n                                                    padding: \"14px 10px\",\n                                                    backgroundColor: \"#E6F7FF\",\n                                                    borderRadius: \"8px\",\n                                                    marginBottom: \"16px\",\n                                                }}>\n                                                <div\n                                                    style={{\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        borderRadius: \"50%\",\n                                                        backgroundColor: \"#1890FF\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        flexShrink: 0,\n                                                        marginTop: \"1px\",\n                                                    }}>\n                                                    <span\n                                                        style={{\n                                                            color: \"#ffffff\",\n                                                            fontSize: \"12px\",\n                                                            fontWeight: \"bold\",\n                                                        }}>\n                                                        i\n                                                    </span>\n                                                </div>\n                                                <p\n                                                    style={{\n                                                        margin: 0,\n                                                        fontSize: \"14px\",\n                                                        color: \"#1890FF\",\n                                                        lineHeight: \"1.5\",\n                                                        fontWeight: \"500\",\n                                                    }}>\n                                                    We will redirect you to Order Received page in{\" \"}\n                                                    <strong>5 sec</strong>\n                                                </p>\n                                            </div>\n                                        </div>\n                                    )\n                                }\n\n                                // 2. Initiation State - Agreement authorized, payment being initiated\n                                if (\n                                    (agreementStatus === \"authorized\" || agreementStatus === \"approved\") &&\n                                    (paymentStatus === \"initiated\" || paymentStatus === \"processing\")\n                                ) {\n                                    return (\n                                        <div>\n                                            {/* Initiate payment - active/loading */}\n                                            <div style={{ marginBottom: \"16px\" }}>\n                                                <div\n                                                    style={{\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"12px\",\n                                                        borderRadius: \"8px\",\n                                                        marginBottom: \"8px\",\n                                                    }}>\n                                                    <div\n                                                        style={{\n                                                            width: \"20px\",\n                                                            height: \"20px\",\n                                                            border: \"2px solid #ffffff\",\n                                                            borderTop: \"2px solid #2CB5C5\",\n                                                            borderRadius: \"50%\",\n                                                            animation: \"spin 1s linear infinite\",\n                                                            flexShrink: 0,\n                                                        }}></div>\n                                                    <span\n                                                        style={{\n                                                            fontSize: \"16px\",\n                                                            color: \"#484848\",\n                                                            fontWeight: \"400\",\n                                                        }}>\n                                                        Initiate payment\n                                                    </span>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    )\n                                }\n\n                                // 4. Failed State - Payment failed/cancelled/rejected\n                                if (\n                                    paymentStatus === \"failed\" ||\n                                    paymentStatus === \"cancelled\" ||\n                                    paymentStatus === \"rejected\" ||\n                                    agreementStatus === \"failed\" ||\n                                    agreementStatus === \"cancelled\" ||\n                                    agreementStatus === \"rejected\"\n                                ) {\n                                    return (\n                                        <div>\n                                            {/* Payment failed - error state */}\n                                            <div style={{ marginBottom: \"16px\", textAlign: \"center\" }}>\n                                                <div\n                                                    style={{\n                                                        width: \"22px\",\n                                                        height: \"22px\",\n                                                        borderRadius: \"50%\",\n                                                        backgroundColor: \"#FF0000\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        margin: \"0 auto 16px\",\n                                                    }}>\n                                                    <span\n                                                        style={{\n                                                            color: \"#ffffff\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: \"bold\",\n                                                        }}>\n                                                        i\n                                                    </span>\n                                                </div>\n                                                <h3\n                                                    style={{\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"600\",\n                                                        color: \"#484848\",\n                                                        margin: \"0 0 8px 0\",\n                                                    }}>\n                                                    Payment failed\n                                                </h3>\n                                                <p\n                                                    style={{\n                                                        fontSize: \"16px\",\n                                                        color: \"#484848\",\n                                                        margin: \"0 0 24px 0\",\n                                                        lineHeight: \"1.5\",\n                                                        fontWeight: \"400\",\n                                                    }}>\n                                                    {errors.general ||\n                                                        errorDetails?.message ||\n                                                        \"Something went wrong with the payment.\"}\n                                                    <br />\n                                                    Please try again.\n                                                </p>\n\n                                                {/* Show detailed error information if available */}\n                                                {errorDetails && (\n                                                    <div\n                                                        style={{\n                                                            marginBottom: \"24px\",\n                                                            padding: \"16px\",\n                                                            backgroundColor: \"#FFF2F2\",\n                                                            border: \"1px solid #FFCDD2\",\n                                                            borderRadius: \"8px\",\n                                                            textAlign: \"left\",\n                                                            fontSize: \"14px\",\n                                                        }}>\n                                                        <h4\n                                                            style={{\n                                                                margin: \"0 0 12px 0\",\n                                                                color: \"#D32F2F\",\n                                                                fontSize: \"14px\",\n                                                                fontWeight: \"600\",\n                                                            }}>\n                                                            Error Details\n                                                        </h4>\n\n                                                        {errorDetails.code && (\n                                                            <div style={{ marginBottom: \"8px\" }}>\n                                                                <strong style={{ color: \"#D32F2F\" }}>\n                                                                    Error Code:\n                                                                </strong>{\" \"}\n                                                                <span style={{ color: \"#666\" }}>\n                                                                    {errorDetails.code}\n                                                                </span>\n                                                            </div>\n                                                        )}\n\n                                                        {errorDetails.type && (\n                                                            <div style={{ marginBottom: \"8px\" }}>\n                                                                <strong style={{ color: \"#D32F2F\" }}>\n                                                                    Error Type:\n                                                                </strong>{\" \"}\n                                                                <span style={{ color: \"#666\" }}>\n                                                                    {errorDetails.type}\n                                                                </span>\n                                                            </div>\n                                                        )}\n\n                                                        {errorDetails.context && (\n                                                            <div style={{ marginBottom: \"8px\" }}>\n                                                                <strong style={{ color: \"#D32F2F\" }}>Context:</strong>{\" \"}\n                                                                <span style={{ color: \"#666\" }}>\n                                                                    {errorDetails.context}\n                                                                </span>\n                                                            </div>\n                                                        )}\n\n                                                        {errorDetails.validation_errors && (\n                                                            <div style={{ marginTop: \"12px\", fontSize: \"13px\" }}>\n                                                                <strong style={{ color: \"#D32F2F\" }}>\n                                                                    Validation Errors:\n                                                                </strong>\n                                                                <div style={{ marginTop: \"4px\", color: \"#666\" }}>\n                                                                    {typeof errorDetails.validation_errors === \"object\"\n                                                                        ? Object.entries(\n                                                                              errorDetails.validation_errors\n                                                                          ).map(([field, error], index) => (\n                                                                              <div\n                                                                                  key={index}\n                                                                                  style={{ marginBottom: \"4px\" }}>\n                                                                                  <strong>{field}:</strong> {error}\n                                                                              </div>\n                                                                          ))\n                                                                        : errorDetails.validation_errors}\n                                                                </div>\n                                                            </div>\n                                                        )}\n                                                    </div>\n                                                )}\n                                                <button\n                                                    onClick={() => {\n                                                        setPaymentStatus(\"form\")\n                                                        setErrors({})\n                                                        setErrorDetails(null)\n                                                        setPaymentInstructions(null)\n                                                    }}\n                                                    style={{\n                                                        width: \"100%\",\n                                                        padding: \"12px 20px\",\n                                                        backgroundColor: \"#2CB5C5\",\n                                                        color: \"#000000\",\n                                                        border: \"none\",\n                                                        borderRadius: \"8px\",\n                                                        cursor: \"pointer\",\n                                                        fontSize: \"16px\",\n                                                        fontWeight: \"700\",\n                                                        lineHeight: \"1.21em\",\n                                                    }}>\n                                                    Try Again\n                                                </button>\n                                            </div>\n                                        </div>\n                                    )\n                                }\n\n                                // 1. Waiting State - Default state waiting for agreement approval\n                                return (\n                                    <div>\n                                        {/* PayTo approval - active/waiting */}\n                                        <div style={{ marginBottom: \"16px\" }}>\n                                            <div\n                                                style={{\n                                                    display: \"flex\",\n                                                    alignItems: \"flex-start\",\n                                                    gap: \"12px\",\n                                                    padding: \"14px 10px\",\n                                                    backgroundColor: \"#EDFDFF\",\n                                                    borderRadius: \"8px\",\n                                                    marginBottom: \"8px\",\n                                                }}>\n                                                <div\n                                                    style={{\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        borderRadius: \"50%\",\n                                                        backgroundColor: \"#007A89\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        flexShrink: 0,\n                                                        marginTop: \"1px\",\n                                                    }}>\n                                                    <span\n                                                        style={{\n                                                            color: \"#ffffff\",\n                                                            fontSize: \"12px\",\n                                                            fontWeight: \"bold\",\n                                                        }}>\n                                                        !\n                                                    </span>\n                                                </div>\n                                                <p\n                                                    style={{\n                                                        margin: 0,\n                                                        fontSize: \"14px\",\n                                                        color: \"#007A89\",\n                                                        lineHeight: \"1.5\",\n                                                        fontWeight: \"500\",\n                                                    }}>\n                                                    Approve your recurring payment in your online banking or banking app\n                                                </p>\n                                            </div>\n                                            <div\n                                                style={{\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"12px\",\n                                                    borderRadius: \"8px\",\n                                                }}>\n                                                <div\n                                                    style={{\n                                                        width: \"20px\",\n                                                        height: \"20px\",\n                                                        border: \"2px solid #ffffff\",\n                                                        borderTop: \"2px solid #007A89\",\n                                                        borderRadius: \"50%\",\n                                                        animation: \"spin 1s linear infinite\",\n                                                        flexShrink: 0,\n                                                    }}></div>\n                                                <span\n                                                    style={{\n                                                        fontSize: \"16px\",\n                                                        color: \"#000000\",\n                                                        fontWeight: \"500\",\n                                                    }}>\n                                                    Awaiting your approval\n                                                </span>\n                                            </div>\n                                        </div>\n                                    </div>\n                                )\n                            })()}\n                        </div>\n                    </div>\n                </div>\n            )\n        }\n\n        // Show different views based on payment status\n        if (paymentStatus === \"instructions\") {\n            return renderPaymentInstructions()\n        }\n\n        // Show agreement limit exceeded state with Pay Again option\n        if (paymentStatus === \"agreement_limit_exceeded\") {\n            return (\n                <>\n                    {/* PayTo Section Container */}\n                    <div>\n                        {/* Payment Amount */}\n                        <div\n                            style={{\n                                textAlign: \"center\",\n                                margin: \"24px 0\",\n                            }}>\n                            <div\n                                style={{\n                                    fontSize: \"24px\",\n                                    fontWeight: \"700\",\n                                    color: \"#000\",\n                                    marginBottom: \"8px\",\n                                }}>\n                                Payment Agreement Limit Exceeded\n                            </div>\n                            <p\n                                style={{\n                                    fontSize: \"14px\",\n                                    color: \"#6b7280\",\n                                    margin: \"0\",\n                                }}>\n                                Your current payment agreement has reached its limits.\n                            </p>\n                        </div>\n\n                        {/* Error Message */}\n                        <div\n                            style={{\n                                backgroundColor: \"#FFF2F2\",\n                                border: \"1px solid #FFCDD2\",\n                                borderRadius: \"8px\",\n                                padding: \"16px\",\n                                marginBottom: \"24px\",\n                            }}>\n                            <div\n                                style={{\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    marginBottom: \"12px\",\n                                }}>\n                                <span\n                                    style={{\n                                        fontSize: \"18px\",\n                                        marginRight: \"8px\",\n                                    }}>\n                                    ⚠️\n                                </span>\n                                <h3\n                                    style={{\n                                        fontSize: \"16px\",\n                                        fontWeight: \"600\",\n                                        color: \"#D32F2F\",\n                                        margin: \"0\",\n                                    }}>\n                                    Agreement Limits Exceeded\n                                </h3>\n                            </div>\n                            <p\n                                style={{\n                                    fontSize: \"16px\",\n                                    color: \"#000000\",\n                                    margin: \"0 0 16px 0\",\n                                    lineHeight: \"1.5\",\n                                }}>\n                                {errors.general ||\n                                    errorDetails?.message ||\n                                    \"Payment agreement limits have been exceeded.\"}\n                            </p>\n\n                            {/* Show specific limit errors */}\n                            {errorDetails?.limit_errors && errorDetails.limit_errors.length > 0 && (\n                                <div style={{ marginBottom: \"16px\" }}>\n                                    <strong style={{ color: \"#D32F2F\", fontSize: \"14px\" }}>Specific Issues:</strong>\n                                    <ul style={{ margin: \"8px 0 0 20px\", color: \"#666\", fontSize: \"14px\" }}>\n                                        {errorDetails.limit_errors.map((error, index) => (\n                                            <li key={index} style={{ marginBottom: \"4px\" }}>\n                                                {error.message}\n                                            </li>\n                                        ))}\n                                    </ul>\n                                </div>\n                            )}\n\n                            <p style={{ fontSize: \"14px\", color: \"#666\", margin: \"0\" }}>\n                                To continue with your payment, you'll need to create a new payment agreement with\n                                updated limits.\n                            </p>\n                        </div>\n\n                        {/* Try Again Button */}\n                        <button\n                            onClick={resetPaymentAgreement}\n                            disabled={isSubmitting}\n                            style={{\n                                width: \"100%\",\n                                padding: \"12px 20px\",\n                                backgroundColor: isSubmitting ? \"#ccc\" : \"#2CB5C5\",\n                                color: \"white\",\n                                border: \"none\",\n                                borderRadius: \"8px\",\n                                fontSize: \"16px\",\n                                fontWeight: \"600\",\n                                cursor: isSubmitting ? \"not-allowed\" : \"pointer\",\n                                transition: \"background-color 0.2s\",\n                                marginBottom: \"16px\",\n                            }}\n                            onMouseOver={e => {\n                                if (!isSubmitting) {\n                                    e.target.style.backgroundColor = \"#1a9aa8\"\n                                }\n                            }}\n                            onMouseOut={e => {\n                                if (!isSubmitting) {\n                                    e.target.style.backgroundColor = \"#2CB5C5\"\n                                }\n                            }}>\n                            {isSubmitting ? (\n                                <>\n                                    <span\n                                        style={{\n                                            display: \"inline-block\",\n                                            width: \"16px\",\n                                            height: \"16px\",\n                                            border: \"2px solid #ffffff\",\n                                            borderTop: \"2px solid transparent\",\n                                            borderRadius: \"50%\",\n                                            animation: \"spin 1s linear infinite\",\n                                            marginRight: \"8px\",\n                                        }}></span>\n                                    Resetting Agreement...\n                                </>\n                            ) : (\n                                \"Try Again with New Agreement\"\n                            )}\n                        </button>\n                    </div>\n                </>\n            )\n        }\n\n        // Show failure state in main form\n        if (paymentStatus === \"failed\") {\n            return (\n                <>\n                    {/* PayTo Section Container */}\n                    <div>\n                        {/* Payment Amount */}\n                        <div\n                            style={{\n                                textAlign: \"center\",\n                                margin: \"24px 0\",\n                            }}>\n                            <div className=\"monoova-scan-pay\">\n                                <div className=\"pay-label\">\n                                    Pay\n                                </div>\n                                <div class=\"amount\">\n                                    <span class=\"woocommerce-Price-amount amount\">\n                                        <bdi>\n                                            <span class=\"woocommerce-Price-currencySymbol\">$</span>\n                                            {orderTotal.toFixed(2)}\n                                        </bdi>\n                                    </span>\n                                </div>\n                            </div>\n                        </div>\n\n                        {/* Failure State */}\n                        <div\n                            style={{\n                                border: \"1px solid #E8E8E8\",\n                                borderRadius: \"16px\",\n                                padding: \"24px 24px 32px\",\n                                gap: \"16px\",\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                alignItems: \"center\",\n                                textAlign: \"center\",\n                            }}>\n                            <div\n                                style={{\n                                    width: \"45px\",\n                                    height: \"45px\",\n                                    borderRadius: \"50%\",\n                                    backgroundColor: \"#FF0000\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    margin: \"0 auto 16px\",\n                                }}>\n                                <span\n                                    style={{\n                                        color: \"#ffffff\",\n                                        fontSize: \"24px\",\n                                        fontWeight: \"bold\",\n                                    }}>\n                                    i\n                                </span>\n                            </div>\n                            <h3\n                                style={{\n                                    fontSize: \"24px\",\n                                    fontWeight: \"600\",\n                                    color: \"#000000\",\n                                    margin: \"0 0 8px 0\",\n                                }}>\n                                Payment failed\n                            </h3>\n                            <p\n                                style={{\n                                    fontSize: \"16px\",\n                                    color: \"#000000\",\n                                    margin: \"0 0 24px 0\",\n                                    lineHeight: \"1.5\",\n                                }}>\n                                {errors.general || errorDetails?.message || \"Something went wrong with the payment.\"}\n                            </p>\n\n                            {/* Show detailed error information if available */}\n                            {errorDetails && (\n                                <div\n                                    style={{\n                                        marginBottom: \"24px\",\n                                        padding: \"16px\",\n                                        backgroundColor: \"#FFF2F2\",\n                                        border: \"1px solid #FFCDD2\",\n                                        borderRadius: \"8px\",\n                                        textAlign: \"left\",\n                                        fontSize: \"14px\",\n                                    }}>\n                                    <h4\n                                        style={{\n                                            margin: \"0 0 12px 0\",\n                                            color: \"#D32F2F\",\n                                            fontSize: \"14px\",\n                                            fontWeight: \"600\",\n                                        }}>\n                                        Error Details\n                                    </h4>\n\n                                    {errorDetails.code && (\n                                        <div style={{ marginBottom: \"8px\" }}>\n                                            <strong style={{ color: \"#D32F2F\" }}>Error Code:</strong>{\" \"}\n                                            <span style={{ color: \"#666\" }}>{errorDetails.code}</span>\n                                        </div>\n                                    )}\n\n                                    {errorDetails.type && (\n                                        <div style={{ marginBottom: \"8px\" }}>\n                                            <strong style={{ color: \"#D32F2F\" }}>Error Type:</strong>{\" \"}\n                                            <span style={{ color: \"#666\" }}>{errorDetails.type}</span>\n                                        </div>\n                                    )}\n\n                                    {errorDetails.context && (\n                                        <div style={{ marginBottom: \"8px\" }}>\n                                            <strong style={{ color: \"#D32F2F\" }}>Context:</strong>{\" \"}\n                                            <span style={{ color: \"#666\" }}>{errorDetails.context}</span>\n                                        </div>\n                                    )}\n\n                                    {(errorDetails.agreement_status ||\n                                        errorDetails.payment_status ||\n                                        errorDetails.order_status) && (\n                                        <div style={{ marginBottom: \"8px\" }}>\n                                            <strong style={{ color: \"#D32F2F\" }}>Status:</strong>{\" \"}\n                                            <span style={{ color: \"#666\" }}>\n                                                {[\n                                                    errorDetails.agreement_status,\n                                                    errorDetails.payment_status,\n                                                    errorDetails.order_status,\n                                                ]\n                                                    .filter(Boolean)\n                                                    .join(\", \")}\n                                            </span>\n                                        </div>\n                                    )}\n\n                                    {errorDetails.details && (\n                                        <div style={{ marginTop: \"12px\", fontSize: \"13px\" }}>\n                                            <strong style={{ color: \"#D32F2F\" }}>Technical Details:</strong>\n                                            <div\n                                                style={{\n                                                    marginTop: \"4px\",\n                                                    padding: \"8px\",\n                                                    backgroundColor: \"#FFFFFF\",\n                                                    border: \"1px solid #E0E0E0\",\n                                                    borderRadius: \"4px\",\n                                                    color: \"#666\",\n                                                    fontFamily: \"monospace\",\n                                                    fontSize: \"12px\",\n                                                    wordBreak: \"break-word\",\n                                                }}>\n                                                {typeof errorDetails.details === \"string\"\n                                                    ? errorDetails.details\n                                                    : JSON.stringify(errorDetails.details, null, 2)}\n                                            </div>\n                                        </div>\n                                    )}\n\n                                    {errorDetails.validation_errors && (\n                                        <div style={{ marginTop: \"12px\", fontSize: \"13px\" }}>\n                                            <strong style={{ color: \"#D32F2F\" }}>Validation Errors:</strong>\n                                            <div style={{ marginTop: \"4px\", color: \"#666\" }}>\n                                                {typeof errorDetails.validation_errors === \"object\"\n                                                    ? Object.entries(errorDetails.validation_errors).map(\n                                                          ([field, error], index) => (\n                                                              <div key={index} style={{ marginBottom: \"4px\" }}>\n                                                                  <strong>{field}:</strong> {error}\n                                                              </div>\n                                                          )\n                                                      )\n                                                    : errorDetails.validation_errors}\n                                            </div>\n                                        </div>\n                                    )}\n                                </div>\n                            )}\n                            <button\n                                onClick={() => {\n                                    setPaymentStatus(\"form\")\n                                    setErrors({})\n                                    setErrorDetails(null)\n                                    setPaymentInstructions(null)\n                                }}\n                                style={{\n                                    width: \"100%\",\n                                    padding: \"12px 20px\",\n                                    backgroundColor: \"#2CB5C5\",\n                                    color: \"#000000\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    cursor: \"pointer\",\n                                    fontSize: \"16px\",\n                                    fontWeight: \"700\",\n                                    lineHeight: \"1.21em\",\n                                }}>\n                                Try Again\n                            </button>\n                        </div>\n                    </div>\n                </>\n            )\n        }\n\n        return (\n            <>\n                {/* PayTo Section Container */}\n                <div className=\"monoova-payto-instructions-wrapper monoova-payid-bank-transfer-instructions-wrapper\" ref={containerRef}>\n                    {/* Payment Amount */}\n                    <div\n                        style={{\n                            textAlign: \"center\",\n                            marginTop: \"24px\",\n                        }}>\n                        <div className=\"monoova-scan-pay\">\n                            <div className=\"pay-label\">\n                                Pay\n                            </div>\n                            <div class=\"amount\">\n                                <span class=\"woocommerce-Price-amount amount\">\n                                    <bdi>\n                                        <span class=\"woocommerce-Price-currencySymbol\">$</span>\n                                        {orderTotal.toFixed(2)}\n                                    </bdi>\n                                </span>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Guest info validation message */}\n                    {!hasRequiredGuestInfo() && (\n                        <div style={{\n                            padding: \"16px\",\n                            backgroundColor: \"#f3f4f6\",\n                            borderRadius: \"8px\",\n                            margin: \"16px 0\",\n                            textAlign: \"center\"\n                        }}>\n                            <p style={{ margin: \"0 0 8px 0\", fontSize: \"14px\", color: \"#374151\" }}>\n                                Please complete your billing information to initialize the payment form.\n                            </p>\n                            <small style={{ fontSize: \"12px\", color: \"#6b7280\" }}>\n                                Required: Email, Name, Address, City, Postcode, and Country\n                            </small>\n                        </div>\n                    )}\n\n                    {/* Express Checkout Button */}\n                    {hasExpressCheckout && settings.is_user_logged_in && (\n                        <div style={{ marginBottom: \"20px\", textAlign: \"center\" }}>\n                            {/* Display mandate information if available */}\n                            {mandateInfo && (\n                                <div\n                                    style={{\n                                        marginBottom: \"16px\",\n                                        padding: \"16px\",\n                                        backgroundColor: \"#f0f8ff\",\n                                        border: \"1px solid #0073aa\",\n                                        borderRadius: \"8px\",\n                                        textAlign: \"left\",\n                                    }}>\n                                    <h4 style={{ margin: \"0 0 12px 0\", color: \"#0073aa\", fontSize: \"16px\" }}>\n                                        {__(\"Express Checkout Available\", \"monoova-payments-for-woocommerce\")}\n                                    </h4>\n                                    <p style={{ margin: \"0 0 12px 0\", color: \"#666\", fontSize: \"14px\" }}>\n                                        {__(\n                                            \"You have an active PayTo mandate that can be used for faster checkout.\",\n                                            \"monoova-payments-for-woocommerce\"\n                                        )}\n                                    </p>\n                                    <div\n                                        style={{\n                                            backgroundColor: \"#ffffff\",\n                                            padding: \"12px\",\n                                            borderRadius: \"4px\",\n                                            fontSize: \"13px\",\n                                            color: \"#333\",\n                                        }}>\n                                        <div style={{ marginBottom: \"6px\" }}>\n                                            <strong>{__(\"Agreement:\", \"monoova-payments-for-woocommerce\")}</strong>{\" \"}\n                                            {mandateInfo.agreement_id\n                                                ? `...${mandateInfo.agreement_id.slice(-8)}`\n                                                : \"Available\"}\n                                        </div>\n                                        <div style={{ marginBottom: \"6px\" }}>\n                                            <strong>{__(\"Maximum Amount:\", \"monoova-payments-for-woocommerce\")}</strong>{\" \"}\n                                            {currency} ${parseFloat(mandateInfo.maximum_amount || 0).toFixed(2)}\n                                        </div>\n                                        <div style={{ marginBottom: \"6px\" }}>\n                                            <strong>{__(\"Status:\", \"monoova-payments-for-woocommerce\")}</strong>{\" \"}\n                                            <span\n                                                style={{\n                                                    color: mandateInfo.status === \"authorized\" ? \"#10b981\" : \"#f59e0b\",\n                                                    fontWeight: \"500\",\n                                                }}>\n                                                {mandateInfo.status || \"Active\"}\n                                            </span>\n                                        </div>\n                                        {mandateInfo.num_of_transactions_permitted && (\n                                            <div>\n                                                <strong>\n                                                    {__(\"Transactions:\", \"monoova-payments-for-woocommerce\")}\n                                                </strong>{\" \"}\n                                                <span\n                                                    style={{\n                                                        color:\n                                                            mandateInfo.remaining_transactions > 0\n                                                                ? \"#10b981\"\n                                                                : \"#ef4444\",\n                                                        fontWeight: \"500\",\n                                                    }}>\n                                                    {mandateInfo.remaining_transactions || 0} remaining\n                                                </span>{\" \"}\n                                                <span style={{ color: \"#6b7280\", fontSize: \"12px\" }}>\n                                                    ({mandateInfo.num_of_paid_transactions || 0}/\n                                                    {mandateInfo.num_of_transactions_permitted || 10} used)\n                                                </span>\n                                            </div>\n                                        )}\n                                    </div>\n                                </div>\n                            )}\n\n                            <button\n                                type=\"button\"\n                                onClick={handleCheckoutWithExistingMandate}\n                                disabled={isSubmitting}\n                                style={{\n                                    width: \"100%\",\n                                    padding: \"12px 20px\",\n                                    backgroundColor: isSubmitting ? \"#9ca3af\" : \"#10b981\",\n                                    color: \"#ffffff\",\n                                    border: \"none\",\n                                    borderRadius: \"8px\",\n                                    cursor: isSubmitting ? \"not-allowed\" : \"pointer\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"600\",\n                                    marginBottom: \"12px\",\n                                }}>\n                                {isSubmitting\n                                    ? __(\"Processing...\", \"monoova-payments-for-woocommerce\")\n                                    : __(\"Pay with existing PayTo mandate\", \"monoova-payments-for-woocommerce\")}\n                            </button>\n                            <div\n                                style={{\n                                    fontSize: \"12px\",\n                                    color: \"#6b7280\",\n                                    marginBottom: \"16px\",\n                                }}>\n                                {__(\"or set up a new payment method below\", \"monoova-payments-for-woocommerce\")}\n                            </div>\n                        </div>\n                    )}\n\n                    {\n                        hasRequiredGuestInfo()\n                        && <>\n                            {/* Payment Method Selection */}\n                            <div style={{ marginBottom: \"24px\" }}>\n                                <div className=\"monoova-instruction-method-selection\">\n                                    <div style={{ fontWeight: 600 }}>\n                                        Pay with\n                                    </div>\n                                    <RadioControl\n                                        selected={paymentMethod}\n                                        options={[\n                                            { label: \"PayID\", value: 'payid' },\n                                            { label: \"BSB and account number\", value: 'bsb_account' },\n                                        ]}\n                                        onChange={(v) => setPaymentMethod(v)}\n                                    />\n                                    \n                                </div>\n                            </div>\n\n                            {/* Form Fields Container */}\n                            <div\n                                style={{\n                                    border: \"1px solid #E8E8E8\",\n                                    borderRadius: \"16px\",\n                                    padding: \"24px 24px 32px\",\n                                    gap: \"16px\",\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    alignItems: \"center\",\n                                }}>\n                                {/* PayID Fields */}\n                                {paymentMethod === \"payid\" && (\n                                    <div style={{ width: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\n                                        <div style={{ display: \"flex\", alignItems: \"center\" }}>\n                                            <label\n                                                style={{\n                                                    fontWeight: \"600\",\n                                                    fontSize: \"16px\",\n                                                    lineHeight: \"1.5em\",\n                                                    color: \"#000000\",\n                                                }}>\n                                                Enter your PayID\n                                            </label>\n                                        </div>\n                                        <TextControl\n                                            value={payidValue}\n                                            onChange={value => {\n                                                setPayidValue(value)\n                                                validatePayID(value)\n                                            }}\n                                            placeholder=\"0412 345 678\"\n                                            className={errors.payidValue ? \"has-error\" : \"\"}\n                                        />\n                                        {errors.payidValue && (\n                                            <div\n                                                style={{\n                                                    color: \"#ef4444\",\n                                                    fontSize: \"12px\",\n                                                    marginTop: \"6px\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\",\n                                                }}>\n                                                {errors.payidValue}\n                                            </div>\n                                        )}\n                                    </div>\n                                )}\n\n                                {/* BSB and Account Fields */}\n                                {paymentMethod === \"bsb_account\" && (\n                                    <>\n                                        {/* Account Name Field */}\n                                        <div style={{ width: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\n                                            <div style={{ display: \"flex\", alignItems: \"center\" }}>\n                                                <label\n                                                    style={{\n                                                        fontWeight: \"600\",\n                                                        fontSize: \"16px\",\n                                                        lineHeight: \"1.5em\",\n                                                        color: \"#000000\"\n                                                    }}>\n                                                    Name associated with bank account\n                                                </label>\n                                            </div>\n                                            <TextControl\n                                                value={accountName}\n                                                onChange={setAccountName}\n                                                placeholder=\"Enter your name\"\n                                                className={errors.accountName ? \"has-error\" : \"\"}\n                                            />\n                                            {errors.accountName && (\n                                                <div\n                                                    style={{\n                                                        color: \"#ef4444\",\n                                                        fontSize: \"12px\",\n                                                        marginTop: \"6px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"4px\",\n                                                    }}>\n                                                    {errors.accountName}\n                                                </div>\n                                            )}\n                                        </div>\n\n                                        {/* BSB Field */}\n                                        <div style={{ width: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\n                                            <div style={{ display: \"flex\", alignItems: \"center\" }}>\n                                                <label\n                                                    style={{\n                                                        fontWeight: \"600\",\n                                                        fontSize: \"16px\",\n                                                        lineHeight: \"1.5em\",\n                                                        color: \"#000000\",\n                                                    }}>\n                                                    BSB\n                                                </label>\n                                            </div>\n                                            <TextControl\n                                                value={bsb}\n                                                onChange={setBsb}\n                                                placeholder=\"123-456\"\n                                                className={errors.bsb ? \"has-error\" : \"\"}\n                                            />\n                                            {errors.bsb && (\n                                                <div\n                                                    style={{\n                                                        color: \"#ef4444\",\n                                                        fontSize: \"12px\",\n                                                        marginTop: \"6px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"4px\",\n                                                    }}>\n                                                    {errors.bsb}\n                                                </div>\n                                            )}\n                                        </div>\n\n                                        {/* Account Number Field */}\n                                        <div style={{ width: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\n                                            <div style={{ display: \"flex\", alignItems: \"center\" }}>\n                                                <label\n                                                    style={{\n                                                        fontWeight: \"600\",\n                                                        fontSize: \"16px\",\n                                                        lineHeight: \"1.5em\",\n                                                        color: \"#000000\",\n                                                    }}>\n                                                    Account Number\n                                                </label>\n                                            </div>\n                                            <TextControl\n                                                value={accountNumber}\n                                                onChange={setAccountNumber}\n                                                placeholder=\"Enter your account number\"\n                                                className={errors.accountNumber ? \"has-error\" : \"\"}\n                                            />\n                                            {errors.accountNumber && (\n                                                <div\n                                                    style={{\n                                                        color: \"#ef4444\",\n                                                        fontSize: \"12px\",\n                                                        marginTop: \"6px\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"4px\",\n                                                    }}>\n                                                    {errors.accountNumber}\n                                                </div>\n                                            )}\n                                        </div>\n                                    </>\n                                )}\n\n                                {/* Maximum Amount Field */}\n                                <div style={{ width: \"100%\", display: \"flex\", flexDirection: \"column\", gap: \"12px\" }}>\n                                    <div style={{ display: \"flex\", flexDirection: \"column\" }}>\n                                        <div style={{ display: \"flex\", alignItems: \"center\" }}>\n                                            <label\n                                                style={{\n                                                    fontWeight: \"600\",\n                                                    fontSize: \"16px\",\n                                                    lineHeight: \"1.5em\",\n                                                    color: \"#000000\",\n                                                }}>\n                                                Maximum payment agreement amount\n                                            </label>\n                                        </div>\n                                        <div className=\"text-control-with-prefix\">\n                                            <span className=\"text-control-prefix\">$</span>\n                                            <TextControl\n                                                type=\"number\"\n                                                value={maximumAmount}\n                                                onChange={value => {\n                                                    // Handle empty input\n                                                    if (value === '' || value === null || value === undefined) {\n                                                        setMaximumAmount('')\n                                                        return\n                                                    }\n\n                                                    // Replace comma with dot for international decimal support\n                                                    const normalizedValue = value.toString().replace(',', '.')\n\n                                                    // Allow partial decimal inputs like \"100.\" or \".5\"\n                                                    if (normalizedValue === '.' || normalizedValue.endsWith('.') || /^\\d*\\.?\\d*$/.test(normalizedValue)) {\n                                                        setMaximumAmount(normalizedValue)\n                                                        return\n                                                    }\n\n                                                    // Parse valid numbers\n                                                    const numericValue = parseFloat(normalizedValue)\n                                                    if (!isNaN(numericValue)) {\n                                                        setMaximumAmount(numericValue)\n                                                    }\n                                                }}\n                                                min=\"0.01\"\n                                                step=\"0.01\"\n                                                placeholder=\"1000\"\n                                                className={errors.maximumAmount ? \"has-error\" : \"\"}\n                                            />\n                                        </div>\n                                        {errors.maximumAmount && (\n                                            <div\n                                                style={{\n                                                    color: \"#ef4444\",\n                                                    fontSize: \"12px\",\n                                                    marginTop: \"6px\",\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: \"4px\",\n                                                }}>\n                                                {errors.maximumAmount}\n                                            </div>\n                                        )}\n                                    </div>\n                                    <div\n                                        style={{\n                                            fontWeight: \"500\",\n                                            fontSize: \"14px\",\n                                            lineHeight: \"1.29em\",\n                                            color: \"#909090\",\n                                        }}>\n                                        This is the maximum amount that can be charged under this PayTo agreement. You can\n                                        modify this amount if needed.\n                                    </div>\n                                </div>\n\n                                {/* Accept and Continue Button */}\n                                <Button\n                                    style={{\n                                        width: \"100%\",\n                                        display: \"flex\",\n                                        justifyContent: \"center\",\n                                        alignItems: \"center\",\n                                        gap: \"10px\",\n                                        backgroundColor: \"#2CB5C5\",\n                                        borderRadius: \"8px\",\n                                        minHeight: \"48px\",\n                                        cursor: isSubmitting ? \"not-allowed\" : \"pointer\",\n                                    }}\n                                    onClick={isSubmitting ? undefined : handlePayToSubmit}>\n                                    <span\n                                        style={{\n                                            fontWeight: \"700\",\n                                            fontSize: \"16px\",\n                                            lineHeight: \"1.21em\",\n                                            color: \"#000000\",\n                                        }}>\n                                        {isSubmitting || paymentStatus === \"processing\"\n                                            ? \"Processing...\"\n                                            : \"Accept and Continue\"}\n                                    </span>\n                                </Button>\n                            </div>\n                        </>\n                    }\n                    \n                </div>\n            </>\n        )\n    }\n\n    /**\n     * Content component for PayTo\n     */\n    const Content = function (props) {\n        const description = decodeEntities(settings.description || \"\");\n        return (\n            <div className=\"monoova-payto-content\">\n                <div\n                    className=\"monoova-payto-description\"\n                    dangerouslySetInnerHTML={{ __html: description }}\n                />\n                <PayToForm {...props} />\n            </div>\n        )\n    }\n\n    /**\n     * Label component with PayTo icon and description\n     */\n    const Label = props => {\n        return (\n            <div style={{ width: \"100%\" }}>\n                <div\n                    style={{\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        width: \"100%\",\n                    }}>\n                    <span style={{ fontWeight: 600 }}>{label}</span>\n                    <img\n                        src={`${\n                            settings.plugin_url || \"/wp-content/plugins/monoova-payments-for-woocommerce/\"\n                        }assets/images/payto-logo.svg`}\n                        alt=\"PayTo logo\"\n                        style={{\n                            height: \"24px\",\n                            width: \"auto\",\n                        }}\n                    />\n                </div>\n            </div>\n        )\n    }\n\n    /**\n     * Monoova PayTo payment method config object\n     */\n    const MonoovaPayTo = {\n        name: \"monoova_payto\",\n        label: <Label />,\n        content: <Content />,\n        edit: <Content />,\n        canMakePayment: () => {\n            return true\n        },\n        ariaLabel: label,\n        supports: {\n            features: settings.supports || [],\n        },\n        icons: settings.icons || null,\n    }\n\n    // Register the payment method\n    try {\n        registerPaymentMethod(MonoovaPayTo)\n    } catch (error) {\n        console.error(\"Monoova PayTo Block: Failed to register payment method:\", error)\n    }\n}\n"], "names": ["useState", "useEffect", "useCallback", "useRef", "usePersistentPaymentDetailsContainer", "usePayToPaymentContainer", "settings", "billing", "orderId", "existingOrderId", "containerId", "paymentMethodId", "hasRequiredInfo", "paymentMethod", "setPaymentMethod", "payidValue", "setPayidValue", "payidType", "setPayidType", "accountName", "setAccountName", "bsb", "setBsb", "accountNumber", "setAccountNumber", "maximumAmount", "setMaximumAmount", "maximum_amount", "errors", "setErrors", "paymentStatus", "setPaymentStatus", "paymentInstructions", "setPaymentInstructions", "isSubmitting", "setIsSubmitting", "pollInterval", "setPollInterval", "hasExpressCheckout", "setHasExpressCheckout", "errorDetails", "setErrorDetails", "mandateInfo", "setMandateInfo", "countdown", "setCountdown", "isProcessingRef", "pollingIntervalRef", "targetRef", "containerElement", "containerIdActive", "isContainerInitialized", "setContainerInitialized", "setOrderData", "getOrderData", "clearOrderData", "showContainer", "<PERSON><PERSON><PERSON><PERSON>", "persistentData", "persistentPaymentData", "instructions", "persistentOrderId", "shouldUseExistingData", "stopPolling", "current", "clearInterval", "console", "log", "formData", "undefined", "saveCurrentState", "currentState", "resetStates", "containerRef", "isInitialized", "PrimerCheckoutManager", "constructor", "containers", "Map", "activeContainers", "Set", "getOrCreateContainer", "containerKey", "has", "container", "document", "createElement", "id", "className", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "set", "element", "isVisible", "clientToken", "get", "targetElement", "containerInfo", "hideAllContainers", "add", "delete", "for<PERSON>ach", "split", "warn", "result", "getContainerElement", "cleanup", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "clear", "primerCheckoutManager", "isActiveRef", "persistentContainerId", "window", "addEventListener", "registerPaymentMethod", "getSetting", "CHECKOUT_STORE_KEY", "decodeEntities", "__", "useSelect", "RadioControl", "<PERSON><PERSON>", "TextControl", "spinnerStyles", "styleSheet", "type", "innerText", "head", "Object", "keys", "wc", "error", "length", "monoova_payto_blocks_params", "title", "description", "supports", "defaultLabel", "label", "PayToForm", "eventRegistration", "emitResponse", "select", "store", "getOrderId", "hasRequiredGuestInfo", "billing<PERSON><PERSON>ress", "email", "first_name", "last_name", "address_1", "city", "postcode", "country", "state", "cartTotals", "getCartTotals", "placeOrderButton", "querySelector", "display", "orderTotal", "total_price", "parseFloat", "currency", "currency_code", "onPaymentSetup", "responseTypes", "noticeContexts", "getStatusColor", "status", "statusLower", "toLowerCase", "background", "text", "checkExpressCheckoutAvailability", "timer", "setInterval", "prev", "is_user_logged_in", "response", "fetch", "ajax_url", "method", "headers", "URLSearchParams", "action", "nonce", "json", "success", "data", "available", "mandate_info", "handleCheckoutWithExistingMandate", "order_id", "payment_agreement_uid", "agreement_id", "amount", "toFixed", "agreement_reference", "message", "startPaymentStatusPolling", "errorMessage", "errorCode", "error_code", "errorType", "error_type", "general", "code", "context", "requires_new_agreement", "limit_errors", "agreement_uid", "details", "error_details", "validatePayID", "value", "trim", "emailPattern", "phonePattern", "test", "validateForm", "newErrors", "numericMaxAmount", "isNaN", "React", "unsubscribe", "ERROR", "messageContext", "PAYMENTS", "SUCCESS", "meta", "paymentMethodData", "payto_payment_method", "payto_payid_type", "payto_payid_value", "payto_account_name", "payto_bsb", "payto_account_number", "payto_maximum_amount", "toString", "handlePayToSubmit", "event", "preventDefault", "stopPropagation", "billingInfo", "billing_first_name", "billing_last_name", "billing_email", "isCheckoutPage", "order_key", "order_received_url", "validation_errors", "resetPaymentAgreement", "customer_email", "interval", "agreementStatus", "agreement_status", "mandate_status", "payment_initiation_status", "orderStatus", "order_status", "limitErrors", "errorMessages", "map", "err", "join", "setTimeout", "orderReceivedUrl", "location", "href", "payment_status", "renderPaymentInstructions", "textAlign", "marginTop", "class", "fontWeight", "gap", "alignItems", "cursor", "position", "width", "height", "border", "borderRadius", "backgroundColor", "top", "left", "transform", "fontSize", "lineHeight", "color", "padding", "flexDirection", "readOnly", "placeholder", "Fragment", "marginBottom", "justifyContent", "minHeight", "opacity", "currentStatus", "flexShrink", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "margin", "borderTop", "animation", "entries", "field", "index", "key", "onClick", "marginRight", "disabled", "transition", "onMouseOver", "e", "target", "onMouseOut", "filter", "Boolean", "fontFamily", "wordBreak", "JSON", "stringify", "ref", "slice", "num_of_transactions_permitted", "remaining_transactions", "num_of_paid_transactions", "selected", "options", "onChange", "v", "normalizedValue", "replace", "endsWith", "numericValue", "min", "step", "Content", "props", "dangerouslySetInnerHTML", "__html", "Label", "src", "plugin_url", "alt", "MonoovaPayTo", "name", "content", "edit", "canMakePayment", "aria<PERSON><PERSON><PERSON>", "features", "icons"], "sourceRoot": ""}