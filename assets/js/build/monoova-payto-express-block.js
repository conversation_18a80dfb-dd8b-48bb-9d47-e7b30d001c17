/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./assets/js/src/blocks/NavigationCountdown.js":
/*!*****************************************************!*\
  !*** ./assets/js/src/blocks/NavigationCountdown.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NavigationCountdown: () => (/* binding */ NavigationCountdown)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);


/**
 * Navigation Countdown Component
 * 
 * Shows a countdown timer and redirects to a URL when it reaches 0
 * 
 * @param {Object} props
 * @param {number} props.initialSeconds - Starting countdown time in seconds
 * @param {string} props.redirectUrl - URL to redirect to when countdown reaches 0
 * @param {string} props.message - Message template with {countdown} placeholder
 */
const NavigationCountdown = ({
  initialSeconds = 5,
  redirectUrl,
  message = 'Redirecting in {countdown} seconds...'
}) => {
  const [countdown, setCountdown] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useState)(initialSeconds);
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    if (countdown <= 0) {
      if (redirectUrl) {
        window.location.href = redirectUrl;
      }
      return;
    }
    const timer = setTimeout(() => {
      setCountdown(prev => prev - 1);
    }, 1000);
    return () => clearTimeout(timer);
  }, [countdown, redirectUrl]);
  const formattedMessage = message.replace('{countdown}', countdown.toString());
  return /*#__PURE__*/React.createElement("div", {
    className: "monoova-navigation-countdown",
    style: {
      marginTop: '15px',
      padding: '10px',
      backgroundColor: '#f8f9fa',
      border: '1px solid #dee2e6',
      borderRadius: '4px',
      textAlign: 'center',
      fontSize: '14px',
      color: '#6c757d'
    }
  }, formattedMessage);
};

/***/ }),

/***/ "@woocommerce/block-data":
/*!**************************************!*\
  !*** external ["wc","wcBlocksData"] ***!
  \**************************************/
/***/ ((module) => {

module.exports = wc.wcBlocksData;

/***/ }),

/***/ "@woocommerce/blocks-registry":
/*!******************************************!*\
  !*** external ["wc","wcBlocksRegistry"] ***!
  \******************************************/
/***/ ((module) => {

module.exports = wc.wcBlocksRegistry;

/***/ }),

/***/ "@woocommerce/settings":
/*!************************************!*\
  !*** external ["wc","wcSettings"] ***!
  \************************************/
/***/ ((module) => {

module.exports = wc.wcSettings;

/***/ }),

/***/ "@wordpress/data":
/*!******************************!*\
  !*** external ["wp","data"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["data"];

/***/ }),

/***/ "@wordpress/element":
/*!*********************************!*\
  !*** external ["wp","element"] ***!
  \*********************************/
/***/ ((module) => {

module.exports = window["wp"]["element"];

/***/ }),

/***/ "@wordpress/html-entities":
/*!**************************************!*\
  !*** external ["wp","htmlEntities"] ***!
  \**************************************/
/***/ ((module) => {

module.exports = window["wp"]["htmlEntities"];

/***/ }),

/***/ "@wordpress/i18n":
/*!******************************!*\
  !*** external ["wp","i18n"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["i18n"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!*************************************************************!*\
  !*** ./assets/js/src/blocks/monoova-payto-express-block.js ***!
  \*************************************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @woocommerce/blocks-registry */ "@woocommerce/blocks-registry");
/* harmony import */ var _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/html-entities */ "@wordpress/html-entities");
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _woocommerce_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @woocommerce/settings */ "@woocommerce/settings");
/* harmony import */ var _woocommerce_settings__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_settings__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @wordpress/data */ "@wordpress/data");
/* harmony import */ var _wordpress_data__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_wordpress_data__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @woocommerce/block-data */ "@woocommerce/block-data");
/* harmony import */ var _woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _NavigationCountdown__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NavigationCountdown */ "./assets/js/src/blocks/NavigationCountdown.js");
/**
 * External dependencies
 */









/**
 * Defensive check for WooCommerce availability
 */
console.log("PayTo Express: Script loaded, checking dependencies");
if (typeof _woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__.registerExpressPaymentMethod === "undefined") {
  console.warn("WooCommerce Blocks registerExpressPaymentMethod is not available. This may be due to edit mode or missing dependencies.");
} else if (!window.wp || !window.wp.element) {
  console.error("WordPress element library is not available");
} else {
  if (typeof _wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement === "undefined" || typeof _wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState === "undefined" || typeof _wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useCallback === "undefined") {
    console.error("Required React hooks are not available");

    // Try fallback to window.wp.element
    const wpElement = window.wp.element;
    if (wpElement && wpElement.createElement && wpElement.useState && wpElement.useCallback) {
      console.log("PayTo Express: Using fallback wp.element");
      const wpCreateElement = wpElement.createElement;
      const wpUseState = wpElement.useState;
      const wpUseCallback = wpElement.useCallback;
      const wpUseEffect = wpElement.useEffect;

      // Proceed with main logic using fallback functions
      initializePayToExpressCheckout(wpCreateElement, wpUseState, wpUseCallback, wpUseEffect);
    } else {
      console.error("Fallback wp.element also not available");
    }
  } else {
    console.log("PayTo Express: All dependencies available, proceeding with initialization");
    // Proceed with main logic using imported functions
    initializePayToExpressCheckout();
  }
}

/**
 * Initialize PayTo Express Checkout with provided React functions
 */
function initializePayToExpressCheckout() {
  console.log("PayTo Express: Starting initialization");

  // Try to get settings from WooCommerce settings registry first, then fallback to global variable
  let settings = {};
  try {
    // Check if getSetting is available before using it
    if (typeof _woocommerce_settings__WEBPACK_IMPORTED_MODULE_2__.getSetting !== "undefined") {
      settings = (0,_woocommerce_settings__WEBPACK_IMPORTED_MODULE_2__.getSetting)("monoova_payto_express_data", {});
      console.log("PayTo Express: Got settings from getSetting:", settings);
    }
  } catch (error) {
    console.warn("getSetting failed, trying fallback:", error);
  }

  // If settings are empty or getSetting failed, try window global fallback
  if (!settings || Object.keys(settings).length === 0) {
    settings = window.monoova_payto_express_blocks_params || {};
    console.log("PayTo Express: Got settings from window global:", settings);
  }

  // If still no settings, provide safe defaults
  if (!settings || Object.keys(settings).length === 0) {
    console.warn("PayTo Express settings not found, using defaults");
    settings = {
      title: "PayTo Express Checkout",
      description: "Pay quickly and securely with your existing PayTo mandate.",
      is_available: false,
      testmode: true,
      ajax_url: "/wp-admin/admin-ajax.php",
      check_mandate_nonce: "",
      express_payment_nonce: "",
      user_logged_in: false,
      // Add default
      i18n: {
        express_pay_button: "Pay with PayTo",
        login_required: "Please log in to use PayTo express checkout.",
        no_mandate: "No active PayTo mandate found.",
        mandate_available: "Use existing PayTo mandate",
        processing: "Processing payment...",
        error: "Payment failed. Please try again."
      }
    };
  }
  console.log("PayTo Express: Final settings:", settings);
  const defaultTitle = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayTo Express Checkout", "monoova-payments-for-woocommerce");

  /**
   * PayTo Express Content Component
   */
  const PayToExpressContent = ({
    onClose,
    onError,
    onSubmit,
    eventRegistration,
    emitResponse
  }) => {
    const [isLoading, setIsLoading] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)(false);
    const [mandateData, setMandateData] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)(null);
    const [isLoggedIn, setIsLoggedIn] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)(false);
    const [shouldUseMandateCheck, setShouldUseMandateCheck] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)(true);
    const [mandateLimitExceeded, setMandateLimitExceeded] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)(false);
    const [showRedirect, setShowRedirect] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useState)(false);

    // Get order ID from WooCommerce checkout store
    const {
      orderId
    } = (0,_wordpress_data__WEBPACK_IMPORTED_MODULE_5__.useSelect)(select => {
      const store = select(_woocommerce_block_data__WEBPACK_IMPORTED_MODULE_6__.CHECKOUT_STORE_KEY);
      return {
        orderId: store.getOrderId()
      };
    });

    // Check login status and mandate availability on mount
    (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useEffect)(() => {
      checkLoginAndMandate();
    }, []);
    const checkLoginAndMandate = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useCallback)(async () => {
      setIsLoading(true);

      // First check if we're logged in according to PHP settings
      const isUserLoggedIn = settings.user_logged_in;
      console.log("User logged in from settings:", isUserLoggedIn);
      console.log("Full settings:", settings);
      if (!isUserLoggedIn) {
        setIsLoggedIn(false);
        setMandateData(null);
        setIsLoading(false);
        return;
      }

      // User is logged in, now check for mandate via AJAX
      try {
        const response = await fetch(settings.ajax_url, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded"
          },
          body: new URLSearchParams({
            action: settings.ajax_check_mandate_action || "monoova_payto_check_mandate",
            nonce: settings.check_mandate_nonce
          })
        });
        const result = await response.json();
        console.log("Mandate check result:", result);
        if (result.success) {
          setIsLoggedIn(true);
          if (result.data && result.data.mandate) {
            setMandateData(result.data.mandate);
            setShouldUseMandateCheck(result.data.should_use_mandate !== false);
            setMandateLimitExceeded(result.data.mandate_limit_exceeded === true);
            console.log("Mandate found:", result.data.mandate);
            console.log("Should use mandate:", result.data.should_use_mandate);
            console.log("Mandate limit exceeded:", result.data.mandate_limit_exceeded);

            // If mandate limit is exceeded, show redirect message
            if (result.data.mandate_limit_exceeded === true) {
              setShowRedirect(true);
            }
          } else {
            console.log("No mandate data in response");
            setMandateData(null);
            setShouldUseMandateCheck(false);
            setMandateLimitExceeded(false);
          }
        } else {
          console.log("Mandate check failed:", result.data?.message || "Unknown error");
          // User is logged in but mandate check failed
          setIsLoggedIn(true);
          setMandateData(null);
          setShouldUseMandateCheck(false);
          setMandateLimitExceeded(false);
        }
      } catch (error) {
        console.error("Error checking mandate:", error);
        // If AJAX fails, still show logged in state but no mandate
        setIsLoggedIn(true);
        setMandateData(null);
        setShouldUseMandateCheck(false);
        setMandateLimitExceeded(false);
      }
      setIsLoading(false);
    }, []);
    const handleExpressPayment = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.useCallback)(async event => {
      console.log("PayTo Express: Button clicked, starting payment process");
      if (!mandateData) {
        onError(settings.i18n.no_mandate);
        return;
      }
      console.log("PayTo Express: Starting payment process", {
        mandateData,
        settings: {
          ajax_url: settings.ajax_url,
          express_payment_nonce: settings.express_payment_nonce
        }
      });
      setIsLoading(true);
      try {
        // For express checkout, we need to initiate the payment immediately
        // This will create an order from the cart and process payment with existing mandate
        const requestBody = new URLSearchParams({
          action: settings.ajax_express_payment_action || "monoova_payto_express_payment",
          nonce: settings.express_payment_nonce,
          payment_agreement_uid: mandateData.payment_agreement_uid
        });
        console.log("PayTo Express: Sending request", {
          url: settings.ajax_url,
          body: requestBody.toString()
        });
        const response = await fetch(settings.ajax_url, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded"
          },
          body: requestBody
        });
        const result = await response.json();
        console.log("PayTo Express: Response received", result);
        if (result.success) {
          // Express checkout successful - redirect to order received page
          const redirectUrl = result.data.order_received_url || result.data.redirect_url;
          console.log("PayTo Express: Redirecting to", redirectUrl);
          if (redirectUrl) {
            window.location.href = redirectUrl;
          } else {
            // Fallback: close express checkout and show success
            console.log("PayTo Express: No redirect URL, closing modal");
            onClose();
          }
        } else {
          console.error("PayTo Express: Payment failed", result.data);
          const errorMessage = result.data?.message || settings.i18n.error;
          onError(errorMessage);
        }
      } catch (error) {
        console.error("PayTo Express: Network error", error);
        onError(settings.i18n.error);
      }
      setIsLoading(false);
    }, [mandateData, onSubmit, onError, emitResponse, orderId]);

    // If loading, show loading state
    if (isLoading) {
      return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("div", {
        className: "monoova-payto-express-loading",
        style: {
          padding: "20px",
          textAlign: "center"
        }
      }, settings.i18n.processing);
    }

    // If not logged in, show login message
    if (!isLoggedIn) {
      return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("div", {
        className: "monoova-payto-express-login-required",
        style: {
          padding: "20px",
          textAlign: "center",
          color: "#666"
        }
      }, settings.i18n.login_required);
    }

    // If no mandate available, show message
    if (!mandateData) {
      return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("div", {
        className: "monoova-payto-express-no-mandate",
        style: {
          padding: "20px",
          textAlign: "center",
          color: "#666"
        }
      }, settings.i18n.no_mandate);
    }

    // If mandate limit is exceeded, show redirect message with countdown
    if (mandateLimitExceeded || showRedirect) {
      const checkoutUrl = window.location.origin + "/checkout/";
      return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("div", {
        className: "monoova-payto-express-limit-exceeded",
        style: {
          padding: "20px",
          textAlign: "center",
          backgroundColor: "#fff3cd",
          border: "1px solid #ffeaa7",
          borderRadius: "5px",
          color: "#856404"
        }
      }, [(0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("div", {
        key: "message",
        style: {
          marginBottom: "15px",
          fontSize: "14px"
        }
      }, "Your current payment agreement has reached its limits. We will redirect you to the checkout page so that you can create another one."), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)(_NavigationCountdown__WEBPACK_IMPORTED_MODULE_7__.NavigationCountdown, {
        key: "countdown",
        initialSeconds: 5,
        redirectUrl: checkoutUrl,
        message: "Redirecting to checkout in {countdown} seconds..."
      })]);
    }

    // If mandate should not be used (but not limit exceeded), don't show express checkout
    if (!shouldUseMandateCheck) {
      return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("div", {
        className: "monoova-payto-express-mandate-invalid",
        style: {
          padding: "20px",
          textAlign: "center",
          color: "#666"
        }
      }, "Your payment agreement cannot be used for this order. Please proceed to checkout.");
    }

    // Show express checkout option
    return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("div", {
      className: "monoova-payto-express-checkout"
    }, [(0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("div", {
      key: "mandate-info",
      className: "mandate-info",
      style: {
        marginBottom: "15px",
        padding: "10px",
        backgroundColor: "#f0f8ff",
        border: "1px solid #0073aa",
        borderRadius: "5px"
      }
    }, [(0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("h4", {
      key: "title",
      style: {
        margin: "0 0 10px 0",
        color: "#0073aa"
      }
    }, settings.i18n.mandate_available), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("p", {
      key: "details",
      style: {
        margin: "0",
        fontSize: "14px"
      }
    }, [(0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("strong", {
      key: "label"
    }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Maximum Amount: ", "monoova-payments-for-woocommerce")), `$${mandateData.maximum_amount || "0.00"}`])]), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("button", {
      key: "express-button",
      type: "button",
      className: "wp-element-button wc-block-components-checkout-return-to-cart-button monoova-payto-express-btn",
      style: {
        width: "100%",
        padding: "12px",
        backgroundColor: settings.button_style?.color === "primary" ? "#0073aa" : "#666666",
        color: "white",
        border: "none",
        borderRadius: `${settings.button_style?.borderRadius || 5}px`,
        fontSize: "16px",
        fontWeight: "bold",
        cursor: isLoading ? "not-allowed" : "pointer",
        opacity: isLoading ? 0.6 : 1,
        height: `${settings.button_style?.height || 48}px`
      },
      onClick: handleExpressPayment,
      disabled: isLoading
    }, isLoading ? settings.i18n.processing : settings.i18n.express_pay_button)]);
  };

  /**
   * Can make payment check
   * Only show PayTo express checkout on cart page, hide on checkout page
   */
  const canMakePayment = ({
    cart,
    cartTotals,
    cartNeedsShipping,
    shippingAddress,
    billingAddress,
    selectedShippingMethods,
    paymentRequirements
  }) => {
    // Check if we're on the checkout page
    const isCheckoutPage = document.body.classList.contains("woocommerce-checkout") || window.location.pathname.includes("/checkout/") || document.querySelector(".woocommerce-checkout") !== null || document.querySelector(".wp-block-woocommerce-checkout") !== null;

    // Check if we're on the cart page
    const isCartPage = document.body.classList.contains("woocommerce-cart") || window.location.pathname.includes("/cart/") || document.querySelector(".woocommerce-cart") !== null || document.querySelector(".wp-block-woocommerce-cart") !== null;

    // Additional check: ensure PayTo is available and user is logged in
    const isPayToAvailable = settings.is_available !== false;
    const isUserLoggedIn = settings.user_logged_in;
    console.log("PayTo Express: canMakePayment check", {
      isCheckoutPage,
      isCartPage,
      isPayToAvailable,
      isUserLoggedIn,
      settings,
      cart,
      cartTotals,
      cartNeedsShipping,
      shippingAddress,
      billingAddress,
      selectedShippingMethods,
      paymentRequirements
    });

    // Only show express checkout on cart page, not on checkout page, and only if PayTo is available
    const canShow = isCartPage && !isCheckoutPage && isPayToAvailable && isUserLoggedIn;
    return canShow;
  };

  /**
   * Express Payment Configuration
   */
  const expressPaymentConfig = {
    name: "monoova_payto_express",
    label: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("span", {}, [
    // Add logo if available
    settings.logo_url && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("img", {
      key: "logo",
      src: settings.logo_url,
      alt: "PayTo",
      style: {
        height: "20px",
        marginRight: "8px",
        verticalAlign: "middle"
      }
    }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("span", {
      key: "title"
    }, settings.title || defaultTitle)]),
    content: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)(PayToExpressContent),
    edit: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_4__.createElement)("div", {}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayTo Express Checkout", "monoova-payments-for-woocommerce")),
    canMakePayment: canMakePayment,
    paymentMethodId: "monoova_payto_express",
    // Use unique ID for express checkout
    supports: {
      features: ["products"]
    }
  };

  // Register the express payment method
  try {
    console.log("PayTo Express: Attempting to register with config:", expressPaymentConfig);

    // Check if another express payment method with same name exists
    console.log("PayTo Express: Checking existing registrations...");
    (0,_woocommerce_blocks_registry__WEBPACK_IMPORTED_MODULE_0__.registerExpressPaymentMethod)(expressPaymentConfig);
    console.log("PayTo Express Checkout registered successfully");

    // Verify registration worked by checking if method is available
    setTimeout(() => {
      const expressCheckoutArea = document.querySelector(".wc-block-components-express-payment");
      console.log("PayTo Express: Express checkout area found:", expressCheckoutArea);
    }, 2000);
  } catch (error) {
    console.error("Failed to register PayTo Express Checkout:", error);
  }
}
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (initializePayToExpressCheckout);
})();

/******/ })()
;
//# sourceMappingURL=monoova-payto-express-block.js.map