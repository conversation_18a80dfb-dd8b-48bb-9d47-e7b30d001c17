(()=>{"use strict";const e=wc.wcBlocksRegistry,o=(window.wp.htmlEntities,wc.wcSettings),t=window.wp.i18n,a=window.wp.element,n=window.wp.data,s=wc.wcBlocksData,r=({initialSeconds:e=5,redirectUrl:o,message:t="Redirecting in {countdown} seconds..."})=>{const[n,s]=(0,a.useState)(e);(0,a.useEffect)((()=>{if(n<=0)return void(o&&(window.location.href=o));const e=setTimeout((()=>{s((e=>e-1))}),1e3);return()=>clearTimeout(e)}),[n,o]);const r=t.replace("{countdown}",n.toString());return React.createElement("div",{className:"monoova-navigation-countdown",style:{marginTop:"15px",padding:"10px",backgroundColor:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"4px",textAlign:"center",fontSize:"14px",color:"#6c757d"}},r)};if(console.log("PayTo Express: Script loaded, checking dependencies"),void 0===e.registerExpressPaymentMethod)console.warn("WooCommerce Blocks registerExpressPaymentMethod is not available. This may be due to edit mode or missing dependencies.");else if(window.wp&&window.wp.element)if(void 0===a.createElement||void 0===a.useState||void 0===a.useCallback){console.error("Required React hooks are not available");const e=window.wp.element;e&&e.createElement&&e.useState&&e.useCallback?(console.log("PayTo Express: Using fallback wp.element"),e.createElement,e.useState,e.useCallback,e.useEffect,c()):console.error("Fallback wp.element also not available")}else console.log("PayTo Express: All dependencies available, proceeding with initialization"),c();else console.error("WordPress element library is not available");function c(){console.log("PayTo Express: Starting initialization");let c={};try{void 0!==o.getSetting&&(c=(0,o.getSetting)("monoova_payto_express_data",{}),console.log("PayTo Express: Got settings from getSetting:",c))}catch(e){console.warn("getSetting failed, trying fallback:",e)}c&&0!==Object.keys(c).length||(c=window.monoova_payto_express_blocks_params||{},console.log("PayTo Express: Got settings from window global:",c)),c&&0!==Object.keys(c).length||(console.warn("PayTo Express settings not found, using defaults"),c={title:"PayTo Express Checkout",description:"Pay quickly and securely with your existing PayTo mandate.",is_available:!1,testmode:!0,ajax_url:"/wp-admin/admin-ajax.php",check_mandate_nonce:"",express_payment_nonce:"",user_logged_in:!1,i18n:{express_pay_button:"Pay with PayTo",login_required:"Please log in to use PayTo express checkout.",no_mandate:"No active PayTo mandate found.",mandate_available:"Use existing PayTo mandate",processing:"Processing payment...",error:"Payment failed. Please try again."}}),console.log("PayTo Express: Final settings:",c);const i=(0,t.__)("PayTo Express Checkout","monoova-payments-for-woocommerce"),l={name:"monoova_payto_express",label:(0,a.createElement)("span",{},[c.logo_url&&(0,a.createElement)("img",{key:"logo",src:c.logo_url,alt:"PayTo",style:{height:"20px",marginRight:"8px",verticalAlign:"middle"}}),(0,a.createElement)("span",{key:"title"},c.title||i)]),content:(0,a.createElement)((({onClose:e,onError:o,onSubmit:i,eventRegistration:l,emitResponse:d})=>{const[m,p]=(0,a.useState)(!1),[u,g]=(0,a.useState)(null),[y,x]=(0,a.useState)(!1),[_,w]=(0,a.useState)(!0),[h,k]=(0,a.useState)(!1),[b,f]=(0,a.useState)(!1),{orderId:E}=(0,n.useSelect)((e=>({orderId:e(s.CHECKOUT_STORE_KEY).getOrderId()})));(0,a.useEffect)((()=>{P()}),[]);const P=(0,a.useCallback)((async()=>{p(!0);const e=c.user_logged_in;if(console.log("User logged in from settings:",e),console.log("Full settings:",c),!e)return x(!1),g(null),void p(!1);try{const e=await fetch(c.ajax_url,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:c.ajax_check_mandate_action||"monoova_payto_check_mandate",nonce:c.check_mandate_nonce})}),o=await e.json();console.log("Mandate check result:",o),o.success?(x(!0),o.data&&o.data.mandate?(g(o.data.mandate),w(!1!==o.data.should_use_mandate),k(!0===o.data.mandate_limit_exceeded),console.log("Mandate found:",o.data.mandate),console.log("Should use mandate:",o.data.should_use_mandate),console.log("Mandate limit exceeded:",o.data.mandate_limit_exceeded),!0===o.data.mandate_limit_exceeded&&f(!0)):(console.log("No mandate data in response"),g(null),w(!1),k(!1))):(console.log("Mandate check failed:",o.data?.message||"Unknown error"),x(!0),g(null),w(!1),k(!1))}catch(e){console.error("Error checking mandate:",e),x(!0),g(null),w(!1),k(!1)}p(!1)}),[]),v=(0,a.useCallback)((async t=>{if(console.log("PayTo Express: Button clicked, starting payment process"),u){console.log("PayTo Express: Starting payment process",{mandateData:u,settings:{ajax_url:c.ajax_url,express_payment_nonce:c.express_payment_nonce}}),p(!0);try{const t=new URLSearchParams({action:c.ajax_express_payment_action||"monoova_payto_express_payment",nonce:c.express_payment_nonce,payment_agreement_uid:u.payment_agreement_uid});console.log("PayTo Express: Sending request",{url:c.ajax_url,body:t.toString()});const a=await fetch(c.ajax_url,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t}),n=await a.json();if(console.log("PayTo Express: Response received",n),n.success){const o=n.data.order_received_url||n.data.redirect_url;console.log("PayTo Express: Redirecting to",o),o?window.location.href=o:(console.log("PayTo Express: No redirect URL, closing modal"),e())}else{console.error("PayTo Express: Payment failed",n.data);const e=n.data?.message||c.i18n.error;o(e)}}catch(e){console.error("PayTo Express: Network error",e),o(c.i18n.error)}p(!1)}else o(c.i18n.no_mandate)}),[u,i,o,d,E]);if(m)return(0,a.createElement)("div",{className:"monoova-payto-express-loading",style:{padding:"20px",textAlign:"center"}},c.i18n.processing);if(!y)return(0,a.createElement)("div",{className:"monoova-payto-express-login-required",style:{padding:"20px",textAlign:"center",color:"#666"}},c.i18n.login_required);if(!u)return(0,a.createElement)("div",{className:"monoova-payto-express-no-mandate",style:{padding:"20px",textAlign:"center",color:"#666"}},c.i18n.no_mandate);if(h||b){const e=window.location.origin+"/checkout/";return(0,a.createElement)("div",{className:"monoova-payto-express-limit-exceeded",style:{padding:"20px",textAlign:"center",backgroundColor:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"5px",color:"#856404"}},[(0,a.createElement)("div",{key:"message",style:{marginBottom:"15px",fontSize:"14px"}},"Your current payment agreement has reached its limits. We will redirect you to the checkout page so that you can create another one."),(0,a.createElement)(r,{key:"countdown",initialSeconds:5,redirectUrl:e,message:"Redirecting to checkout in {countdown} seconds..."})])}return _?(0,a.createElement)("div",{className:"monoova-payto-express-checkout"},[(0,a.createElement)("div",{key:"mandate-info",className:"mandate-info",style:{marginBottom:"15px",padding:"10px",backgroundColor:"#f0f8ff",border:"1px solid #0073aa",borderRadius:"5px"}},[(0,a.createElement)("h4",{key:"title",style:{margin:"0 0 10px 0",color:"#0073aa"}},c.i18n.mandate_available),(0,a.createElement)("p",{key:"details",style:{margin:"0",fontSize:"14px"}},[(0,a.createElement)("strong",{key:"label"},(0,t.__)("Maximum Amount: ","monoova-payments-for-woocommerce")),`$${u.maximum_amount||"0.00"}`])]),(0,a.createElement)("button",{key:"express-button",type:"button",className:"wp-element-button wc-block-components-checkout-return-to-cart-button monoova-payto-express-btn",style:{width:"100%",padding:"12px",backgroundColor:"primary"===c.button_style?.color?"#0073aa":"#666666",color:"white",border:"none",borderRadius:`${c.button_style?.borderRadius||5}px`,fontSize:"16px",fontWeight:"bold",cursor:m?"not-allowed":"pointer",opacity:m?.6:1,height:`${c.button_style?.height||48}px`},onClick:v,disabled:m},m?c.i18n.processing:c.i18n.express_pay_button)]):(0,a.createElement)("div",{className:"monoova-payto-express-mandate-invalid",style:{padding:"20px",textAlign:"center",color:"#666"}},"Your payment agreement cannot be used for this order. Please proceed to checkout.")})),edit:(0,a.createElement)("div",{},(0,t.__)("PayTo Express Checkout","monoova-payments-for-woocommerce")),canMakePayment:({cart:e,cartTotals:o,cartNeedsShipping:t,shippingAddress:a,billingAddress:n,selectedShippingMethods:s,paymentRequirements:r})=>{const i=document.body.classList.contains("woocommerce-checkout")||window.location.pathname.includes("/checkout/")||null!==document.querySelector(".woocommerce-checkout")||null!==document.querySelector(".wp-block-woocommerce-checkout"),l=document.body.classList.contains("woocommerce-cart")||window.location.pathname.includes("/cart/")||null!==document.querySelector(".woocommerce-cart")||null!==document.querySelector(".wp-block-woocommerce-cart"),d=!1!==c.is_available,m=c.user_logged_in;return console.log("PayTo Express: canMakePayment check",{isCheckoutPage:i,isCartPage:l,isPayToAvailable:d,isUserLoggedIn:m,settings:c,cart:e,cartTotals:o,cartNeedsShipping:t,shippingAddress:a,billingAddress:n,selectedShippingMethods:s,paymentRequirements:r}),l&&!i&&d&&m},paymentMethodId:"monoova_payto_express",supports:{features:["products"]}};try{console.log("PayTo Express: Attempting to register with config:",l),console.log("PayTo Express: Checking existing registrations..."),(0,e.registerExpressPaymentMethod)(l),console.log("PayTo Express Checkout registered successfully"),setTimeout((()=>{const e=document.querySelector(".wc-block-components-express-payment");console.log("PayTo Express: Express checkout area found:",e)}),2e3)}catch(e){console.error("Failed to register PayTo Express Checkout:",e)}}})();