{"version": 3, "file": "monoova-payto-express-block.js", "mappings": ";;;;;;;;;;;;;;;;AAAyD;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAME,mBAAmB,GAAGA,CAAC;EAChCC,cAAc,GAAG,CAAC;EAClBC,WAAW;EACXC,OAAO,GAAG;AACd,CAAC,KAAK;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,4DAAQ,CAACG,cAAc,CAAC;EAE1DF,6DAAS,CAAC,MAAM;IACZ,IAAIK,SAAS,IAAI,CAAC,EAAE;MAChB,IAAIF,WAAW,EAAE;QACbI,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGN,WAAW;MACtC;MACA;IACJ;IAEA,MAAMO,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC3BL,YAAY,CAACM,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,YAAY,CAACH,KAAK,CAAC;EACpC,CAAC,EAAE,CAACL,SAAS,EAAEF,WAAW,CAAC,CAAC;EAE5B,MAAMW,gBAAgB,GAAGV,OAAO,CAACW,OAAO,CAAC,aAAa,EAAEV,SAAS,CAACW,QAAQ,CAAC,CAAC,CAAC;EAE7E,oBACIC,KAAA,CAAAC,aAAA;IAAKC,SAAS,EAAC,8BAA8B;IAACC,KAAK,EAAE;MACjDC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,MAAM;MAChBC,KAAK,EAAE;IACX;EAAE,GACGd,gBACA,CAAC;AAEd,CAAC;;;;;;;;;;AClDD;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNA;AACA;AACA;AAC2E;AAClB;AACP;AACd;AACgD;AACzC;AACiB;AACD;;AAE3D;AACA;AACA;AACAsB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;AAElE,IAAI,OAAOR,sFAA4B,KAAK,WAAW,EAAE;EACrDO,OAAO,CAACE,IAAI,CACR,yHACJ,CAAC;AACL,CAAC,MAAM,IAAI,CAAC/B,MAAM,CAACgC,EAAE,IAAI,CAAChC,MAAM,CAACgC,EAAE,CAACC,OAAO,EAAE;EACzCJ,OAAO,CAACK,KAAK,CAAC,4CAA4C,CAAC;AAC/D,CAAC,MAAM;EACH,IAAI,OAAOvB,6DAAa,KAAK,WAAW,IAAI,OAAOnB,wDAAQ,KAAK,WAAW,IAAI,OAAOkC,2DAAW,KAAK,WAAW,EAAE;IAC/GG,OAAO,CAACK,KAAK,CAAC,wCAAwC,CAAC;;IAEvD;IACA,MAAMC,SAAS,GAAGnC,MAAM,CAACgC,EAAE,CAACC,OAAO;IACnC,IAAIE,SAAS,IAAIA,SAAS,CAACxB,aAAa,IAAIwB,SAAS,CAAC3C,QAAQ,IAAI2C,SAAS,CAACT,WAAW,EAAE;MACrFG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,MAAMM,eAAe,GAAGD,SAAS,CAACxB,aAAa;MAC/C,MAAM0B,UAAU,GAAGF,SAAS,CAAC3C,QAAQ;MACrC,MAAM8C,aAAa,GAAGH,SAAS,CAACT,WAAW;MAC3C,MAAMa,WAAW,GAAGJ,SAAS,CAAC1C,SAAS;;MAEvC;MACA+C,8BAA8B,CAACJ,eAAe,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,CAAC;IAC3F,CAAC,MAAM;MACHV,OAAO,CAACK,KAAK,CAAC,wCAAwC,CAAC;IAC3D;EACJ,CAAC,MAAM;IACHL,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;IACxF;IACAU,8BAA8B,CAAC,CAAC;EACpC;AACJ;;AAEA;AACA;AACA;AACA,SAASA,8BAA8BA,CAAA,EAAG;EACtCX,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;EAErD;EACA,IAAIW,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAI;IACA;IACA,IAAI,OAAOjB,6DAAU,KAAK,WAAW,EAAE;MACnCiB,QAAQ,GAAGjB,iEAAU,CAAC,4BAA4B,EAAE,CAAC,CAAC,CAAC;MACvDK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEW,QAAQ,CAAC;IACzE;EACJ,CAAC,CAAC,OAAOP,KAAK,EAAE;IACZL,OAAO,CAACE,IAAI,CAAC,qCAAqC,EAAEG,KAAK,CAAC;EAC9D;;EAEA;EACA,IAAI,CAACO,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;IACjDH,QAAQ,GAAGzC,MAAM,CAAC6C,mCAAmC,IAAI,CAAC,CAAC;IAC3DhB,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEW,QAAQ,CAAC;EAC5E;;EAEA;EACA,IAAI,CAACA,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;IACjDf,OAAO,CAACE,IAAI,CAAC,kDAAkD,CAAC;IAChEU,QAAQ,GAAG;MACPK,KAAK,EAAE,wBAAwB;MAC/BC,WAAW,EAAE,4DAA4D;MACzEC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,0BAA0B;MACpCC,mBAAmB,EAAE,EAAE;MACvBC,qBAAqB,EAAE,EAAE;MACzBC,cAAc,EAAE,KAAK;MAAE;MACvBC,IAAI,EAAE;QACFC,kBAAkB,EAAE,gBAAgB;QACpCC,cAAc,EAAE,8CAA8C;QAC9DC,UAAU,EAAE,gCAAgC;QAC5CC,iBAAiB,EAAE,4BAA4B;QAC/CC,UAAU,EAAE,uBAAuB;QACnCzB,KAAK,EAAE;MACX;IACJ,CAAC;EACL;EAEAL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEW,QAAQ,CAAC;EAEvD,MAAMmB,YAAY,GAAGnC,mDAAE,CAAC,wBAAwB,EAAE,kCAAkC,CAAC;;EAErF;AACJ;AACA;EACI,MAAMoC,mBAAmB,GAAGA,CAAC;IAAEC,OAAO;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,iBAAiB;IAAEC;EAAa,CAAC,KAAK;IAC7F,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5E,4DAAQ,CAAC,KAAK,CAAC;IACjD,MAAM,CAAC6E,WAAW,EAAEC,cAAc,CAAC,GAAG9E,4DAAQ,CAAC,IAAI,CAAC;IACpD,MAAM,CAAC+E,UAAU,EAAEC,aAAa,CAAC,GAAGhF,4DAAQ,CAAC,KAAK,CAAC;IACnD,MAAM,CAACiF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlF,4DAAQ,CAAC,IAAI,CAAC;IACxE,MAAM,CAACmF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpF,4DAAQ,CAAC,KAAK,CAAC;IACvE,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,4DAAQ,CAAC,KAAK,CAAC;;IAEvD;IACA,MAAM;MAAEuF;IAAQ,CAAC,GAAGpD,0DAAS,CAACqD,MAAM,IAAI;MACpC,MAAMC,KAAK,GAAGD,MAAM,CAACpD,uEAAkB,CAAC;MACxC,OAAO;QACHmD,OAAO,EAAEE,KAAK,CAACC,UAAU,CAAC;MAC9B,CAAC;IACL,CAAC,CAAC;;IAEF;IACAzF,6DAAS,CAAC,MAAM;MACZ0F,oBAAoB,CAAC,CAAC;IAC1B,CAAC,EAAE,EAAE,CAAC;IAEN,MAAMA,oBAAoB,GAAGzD,+DAAW,CAAC,YAAY;MACjD0C,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMgB,cAAc,GAAG3C,QAAQ,CAACY,cAAc;MAE9CxB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEsD,cAAc,CAAC;MAC5DvD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEW,QAAQ,CAAC;MAEvC,IAAI,CAAC2C,cAAc,EAAE;QACjBZ,aAAa,CAAC,KAAK,CAAC;QACpBF,cAAc,CAAC,IAAI,CAAC;QACpBF,YAAY,CAAC,KAAK,CAAC;QACnB;MACJ;;MAEA;MACA,IAAI;QACA,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC7C,QAAQ,CAACS,QAAQ,EAAE;UAC5CqC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACL,cAAc,EAAE;UACpB,CAAC;UACDC,IAAI,EAAE,IAAIC,eAAe,CAAC;YACtBC,MAAM,EAAElD,QAAQ,CAACmD,yBAAyB,IAAI,6BAA6B;YAC3EC,KAAK,EAAEpD,QAAQ,CAACU;UACpB,CAAC;QACL,CAAC,CAAC;QAEF,MAAM2C,MAAM,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;QACpClE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgE,MAAM,CAAC;QAE5C,IAAIA,MAAM,CAACE,OAAO,EAAE;UAChBxB,aAAa,CAAC,IAAI,CAAC;UACnB,IAAIsB,MAAM,CAACG,IAAI,IAAIH,MAAM,CAACG,IAAI,CAACC,OAAO,EAAE;YACpC5B,cAAc,CAACwB,MAAM,CAACG,IAAI,CAACC,OAAO,CAAC;YACnCxB,wBAAwB,CAACoB,MAAM,CAACG,IAAI,CAACE,kBAAkB,KAAK,KAAK,CAAC;YAClEvB,uBAAuB,CAACkB,MAAM,CAACG,IAAI,CAACG,sBAAsB,KAAK,IAAI,CAAC;YAEpEvE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgE,MAAM,CAACG,IAAI,CAACC,OAAO,CAAC;YAClDrE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgE,MAAM,CAACG,IAAI,CAACE,kBAAkB,CAAC;YAClEtE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgE,MAAM,CAACG,IAAI,CAACG,sBAAsB,CAAC;;YAE1E;YACA,IAAIN,MAAM,CAACG,IAAI,CAACG,sBAAsB,KAAK,IAAI,EAAE;cAC7CtB,eAAe,CAAC,IAAI,CAAC;YACzB;UACJ,CAAC,MAAM;YACHjD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;YAC1CwC,cAAc,CAAC,IAAI,CAAC;YACpBI,wBAAwB,CAAC,KAAK,CAAC;YAC/BE,uBAAuB,CAAC,KAAK,CAAC;UAClC;QACJ,CAAC,MAAM;UACH/C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgE,MAAM,CAACG,IAAI,EAAEpG,OAAO,IAAI,eAAe,CAAC;UAC7E;UACA2E,aAAa,CAAC,IAAI,CAAC;UACnBF,cAAc,CAAC,IAAI,CAAC;UACpBI,wBAAwB,CAAC,KAAK,CAAC;UAC/BE,uBAAuB,CAAC,KAAK,CAAC;QAClC;MACJ,CAAC,CAAC,OAAO1C,KAAK,EAAE;QACZL,OAAO,CAACK,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C;QACAsC,aAAa,CAAC,IAAI,CAAC;QACnBF,cAAc,CAAC,IAAI,CAAC;QACpBI,wBAAwB,CAAC,KAAK,CAAC;QAC/BE,uBAAuB,CAAC,KAAK,CAAC;MAClC;MAEAR,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC,EAAE,EAAE,CAAC;IAEN,MAAMiC,oBAAoB,GAAG3E,+DAAW,CACpC,MAAM4E,KAAK,IAAI;MACXzE,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MAEtE,IAAI,CAACuC,WAAW,EAAE;QACdN,OAAO,CAACtB,QAAQ,CAACa,IAAI,CAACG,UAAU,CAAC;QACjC;MACJ;MAEA5B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;QACnDuC,WAAW;QACX5B,QAAQ,EAAE;UACNS,QAAQ,EAAET,QAAQ,CAACS,QAAQ;UAC3BE,qBAAqB,EAAEX,QAAQ,CAACW;QACpC;MACJ,CAAC,CAAC;MAEFgB,YAAY,CAAC,IAAI,CAAC;MAElB,IAAI;QACA;QACA;QACA,MAAMmC,WAAW,GAAG,IAAIb,eAAe,CAAC;UACpCC,MAAM,EAAElD,QAAQ,CAAC+D,2BAA2B,IAAI,+BAA+B;UAC/EX,KAAK,EAAEpD,QAAQ,CAACW,qBAAqB;UACrCqD,qBAAqB,EAAEpC,WAAW,CAACoC;QACvC,CAAC,CAAC;QAEF5E,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC1C4E,GAAG,EAAEjE,QAAQ,CAACS,QAAQ;UACtBuC,IAAI,EAAEc,WAAW,CAAC9F,QAAQ,CAAC;QAC/B,CAAC,CAAC;QAEF,MAAM4E,QAAQ,GAAG,MAAMC,KAAK,CAAC7C,QAAQ,CAACS,QAAQ,EAAE;UAC5CqC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACL,cAAc,EAAE;UACpB,CAAC;UACDC,IAAI,EAAEc;QACV,CAAC,CAAC;QAEF,MAAMT,MAAM,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;QACpClE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEgE,MAAM,CAAC;QAEvD,IAAIA,MAAM,CAACE,OAAO,EAAE;UAChB;UACA,MAAMpG,WAAW,GAAGkG,MAAM,CAACG,IAAI,CAACU,kBAAkB,IAAIb,MAAM,CAACG,IAAI,CAACW,YAAY;UAC9E/E,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAElC,WAAW,CAAC;UACzD,IAAIA,WAAW,EAAE;YACbI,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGN,WAAW;UACtC,CAAC,MAAM;YACH;YACAiC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;YAC5DgC,OAAO,CAAC,CAAC;UACb;QACJ,CAAC,MAAM;UACHjC,OAAO,CAACK,KAAK,CAAC,+BAA+B,EAAE4D,MAAM,CAACG,IAAI,CAAC;UAC3D,MAAMY,YAAY,GAAGf,MAAM,CAACG,IAAI,EAAEpG,OAAO,IAAI4C,QAAQ,CAACa,IAAI,CAACpB,KAAK;UAChE6B,OAAO,CAAC8C,YAAY,CAAC;QACzB;MACJ,CAAC,CAAC,OAAO3E,KAAK,EAAE;QACZL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD6B,OAAO,CAACtB,QAAQ,CAACa,IAAI,CAACpB,KAAK,CAAC;MAChC;MAEAkC,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC,EACD,CAACC,WAAW,EAAEL,QAAQ,EAAED,OAAO,EAAEG,YAAY,EAAEa,OAAO,CAC1D,CAAC;;IAED;IACA,IAAIZ,SAAS,EAAE;MACX,OAAOxD,iEAAa,CAChB,KAAK,EACL;QACIC,SAAS,EAAE,+BAA+B;QAC1CC,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEI,SAAS,EAAE;QAAS;MAClD,CAAC,EACDsB,QAAQ,CAACa,IAAI,CAACK,UAClB,CAAC;IACL;;IAEA;IACA,IAAI,CAACY,UAAU,EAAE;MACb,OAAO5D,iEAAa,CAChB,KAAK,EACL;QACIC,SAAS,EAAE,sCAAsC;QACjDC,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEI,SAAS,EAAE,QAAQ;UAAEE,KAAK,EAAE;QAAO;MACjE,CAAC,EACDoB,QAAQ,CAACa,IAAI,CAACE,cAClB,CAAC;IACL;;IAEA;IACA,IAAI,CAACa,WAAW,EAAE;MACd,OAAO1D,iEAAa,CAChB,KAAK,EACL;QACIC,SAAS,EAAE,kCAAkC;QAC7CC,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEI,SAAS,EAAE,QAAQ;UAAEE,KAAK,EAAE;QAAO;MACjE,CAAC,EACDoB,QAAQ,CAACa,IAAI,CAACG,UAClB,CAAC;IACL;;IAEA;IACA,IAAIkB,oBAAoB,IAAIE,YAAY,EAAE;MACtC,MAAMiC,WAAW,GAAG9G,MAAM,CAACC,QAAQ,CAAC8G,MAAM,GAAG,YAAY;MAEzD,OAAOpG,iEAAa,CAChB,KAAK,EACL;QACIC,SAAS,EAAE,sCAAsC;QACjDC,KAAK,EAAE;UACHE,OAAO,EAAE,MAAM;UACfI,SAAS,EAAE,QAAQ;UACnBH,eAAe,EAAE,SAAS;UAC1BC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,KAAK;UACnBG,KAAK,EAAE;QACX;MACJ,CAAC,EACD,CACIV,iEAAa,CACT,KAAK,EACL;QACIqG,GAAG,EAAE,SAAS;QACdnG,KAAK,EAAE;UAAEoG,YAAY,EAAE,MAAM;UAAE7F,QAAQ,EAAE;QAAO;MACpD,CAAC,EACD,sIACJ,CAAC,EACDT,iEAAa,CAACjB,qEAAmB,EAAE;QAC/BsH,GAAG,EAAE,WAAW;QAChBrH,cAAc,EAAE,CAAC;QACjBC,WAAW,EAAEkH,WAAW;QACxBjH,OAAO,EAAE;MACb,CAAC,CAAC,CAEV,CAAC;IACL;;IAEA;IACA,IAAI,CAAC4E,qBAAqB,EAAE;MACxB,OAAO9D,iEAAa,CAChB,KAAK,EACL;QACIC,SAAS,EAAE,uCAAuC;QAClDC,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEI,SAAS,EAAE,QAAQ;UAAEE,KAAK,EAAE;QAAO;MACjE,CAAC,EACD,mFACJ,CAAC;IACL;;IAEA;IACA,OAAOV,iEAAa,CAChB,KAAK,EACL;MACIC,SAAS,EAAE;IACf,CAAC,EACD,CACID,iEAAa,CACT,KAAK,EACL;MACIqG,GAAG,EAAE,cAAc;MACnBpG,SAAS,EAAE,cAAc;MACzBC,KAAK,EAAE;QACHoG,YAAY,EAAE,MAAM;QACpBlG,OAAO,EAAE,MAAM;QACfC,eAAe,EAAE,SAAS;QAC1BC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE;MAClB;IACJ,CAAC,EACD,CACIP,iEAAa,CACT,IAAI,EACJ;MACIqG,GAAG,EAAE,OAAO;MACZnG,KAAK,EAAE;QAAEqG,MAAM,EAAE,YAAY;QAAE7F,KAAK,EAAE;MAAU;IACpD,CAAC,EACDoB,QAAQ,CAACa,IAAI,CAACI,iBAClB,CAAC,EACD/C,iEAAa,CACT,GAAG,EACH;MACIqG,GAAG,EAAE,SAAS;MACdnG,KAAK,EAAE;QAAEqG,MAAM,EAAE,GAAG;QAAE9F,QAAQ,EAAE;MAAO;IAC3C,CAAC,EACD,CACIT,iEAAa,CACT,QAAQ,EACR;MAAEqG,GAAG,EAAE;IAAQ,CAAC,EAChBvF,mDAAE,CAAC,kBAAkB,EAAE,kCAAkC,CAC7D,CAAC,EACD,IAAI4C,WAAW,CAAC8C,cAAc,IAAI,MAAM,EAAE,CAElD,CAAC,CAET,CAAC,EACDxG,iEAAa,CACT,QAAQ,EACR;MACIqG,GAAG,EAAE,gBAAgB;MACrBI,IAAI,EAAE,QAAQ;MACdxG,SAAS,EACL,gGAAgG;MACpGC,KAAK,EAAE;QACHwG,KAAK,EAAE,MAAM;QACbtG,OAAO,EAAE,MAAM;QACfC,eAAe,EAAEyB,QAAQ,CAAC6E,YAAY,EAAEjG,KAAK,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;QACnFA,KAAK,EAAE,OAAO;QACdJ,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,GAAGuB,QAAQ,CAAC6E,YAAY,EAAEpG,YAAY,IAAI,CAAC,IAAI;QAC7DE,QAAQ,EAAE,MAAM;QAChBmG,UAAU,EAAE,MAAM;QAClBC,MAAM,EAAErD,SAAS,GAAG,aAAa,GAAG,SAAS;QAC7CsD,OAAO,EAAEtD,SAAS,GAAG,GAAG,GAAG,CAAC;QAC5BuD,MAAM,EAAE,GAAGjF,QAAQ,CAAC6E,YAAY,EAAEI,MAAM,IAAI,EAAE;MAClD,CAAC;MACDC,OAAO,EAAEtB,oBAAoB;MAC7BuB,QAAQ,EAAEzD;IACd,CAAC,EACDA,SAAS,GAAG1B,QAAQ,CAACa,IAAI,CAACK,UAAU,GAAGlB,QAAQ,CAACa,IAAI,CAACC,kBACzD,CAAC,CAET,CAAC;EACL,CAAC;;EAED;AACJ;AACA;AACA;EACI,MAAMsE,cAAc,GAAGA,CAAC;IACpBC,IAAI;IACJC,UAAU;IACVC,iBAAiB;IACjBC,eAAe;IACfC,cAAc;IACdC,uBAAuB;IACvBC;EACJ,CAAC,KAAK;IACF;IACA,MAAMC,cAAc,GAChBC,QAAQ,CAAC7C,IAAI,CAAC8C,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IACxDxI,MAAM,CAACC,QAAQ,CAACwI,QAAQ,CAACC,QAAQ,CAAC,YAAY,CAAC,IAC/CJ,QAAQ,CAACK,aAAa,CAAC,uBAAuB,CAAC,KAAK,IAAI,IACxDL,QAAQ,CAACK,aAAa,CAAC,gCAAgC,CAAC,KAAK,IAAI;;IAErE;IACA,MAAMC,UAAU,GACZN,QAAQ,CAAC7C,IAAI,CAAC8C,SAAS,CAACC,QAAQ,CAAC,kBAAkB,CAAC,IACpDxI,MAAM,CAACC,QAAQ,CAACwI,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAC3CJ,QAAQ,CAACK,aAAa,CAAC,mBAAmB,CAAC,KAAK,IAAI,IACpDL,QAAQ,CAACK,aAAa,CAAC,4BAA4B,CAAC,KAAK,IAAI;;IAEjE;IACA,MAAME,gBAAgB,GAAGpG,QAAQ,CAACO,YAAY,KAAK,KAAK;IACxD,MAAMoC,cAAc,GAAG3C,QAAQ,CAACY,cAAc;IAE9CxB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;MAC/CuG,cAAc;MACdO,UAAU;MACVC,gBAAgB;MAChBzD,cAAc;MACd3C,QAAQ;MACRqF,IAAI;MACJC,UAAU;MACVC,iBAAiB;MACjBC,eAAe;MACfC,cAAc;MACdC,uBAAuB;MACvBC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAMU,OAAO,GAAGF,UAAU,IAAI,CAACP,cAAc,IAAIQ,gBAAgB,IAAIzD,cAAc;IACnF,OAAO0D,OAAO;EAClB,CAAC;;EAED;AACJ;AACA;EACI,MAAMC,oBAAoB,GAAG;IACzBC,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAEtI,iEAAa,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;IAC7B;IACA8B,QAAQ,CAACyG,QAAQ,IACbvI,iEAAa,CAAC,KAAK,EAAE;MACjBqG,GAAG,EAAE,MAAM;MACXmC,GAAG,EAAE1G,QAAQ,CAACyG,QAAQ;MACtBE,GAAG,EAAE,OAAO;MACZvI,KAAK,EAAE;QAAE6G,MAAM,EAAE,MAAM;QAAE2B,WAAW,EAAE,KAAK;QAAEC,aAAa,EAAE;MAAS;IACzE,CAAC,CAAC,EACN3I,iEAAa,CAAC,MAAM,EAAE;MAAEqG,GAAG,EAAE;IAAQ,CAAC,EAAEvE,QAAQ,CAACK,KAAK,IAAIc,YAAY,CAAC,CAC1E,CAAC;IACF2F,OAAO,EAAE5I,iEAAa,CAACkD,mBAAmB,CAAC;IAC3C2F,IAAI,EAAE7I,iEAAa,CAAC,KAAK,EAAE,CAAC,CAAC,EAAEc,mDAAE,CAAC,wBAAwB,EAAE,kCAAkC,CAAC,CAAC;IAChGoG,cAAc,EAAEA,cAAc;IAC9B4B,eAAe,EAAE,uBAAuB;IAAE;IAC1CC,QAAQ,EAAE;MACNC,QAAQ,EAAE,CAAC,UAAU;IACzB;EACJ,CAAC;;EAED;EACA,IAAI;IACA9H,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEiH,oBAAoB,CAAC;;IAEvF;IACAlH,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhER,0FAA4B,CAACyH,oBAAoB,CAAC;IAClDlH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;;IAE7D;IACA1B,UAAU,CAAC,MAAM;MACb,MAAMwJ,mBAAmB,GAAGtB,QAAQ,CAACK,aAAa,CAAC,sCAAsC,CAAC;MAC1F9G,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE8H,mBAAmB,CAAC;IACnF,CAAC,EAAE,IAAI,CAAC;EACZ,CAAC,CAAC,OAAO1H,KAAK,EAAE;IACZL,OAAO,CAACK,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;EACtE;AACJ;AAEA,iEAAeM,8BAA8B,E", "sources": ["webpack://monoova-payments-for-woocommerce/./assets/js/src/blocks/NavigationCountdown.js", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksData\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcBlocksRegistry\"]", "webpack://monoova-payments-for-woocommerce/external var [\"wc\",\"wcSettings\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"data\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"element\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"htmlEntities\"]", "webpack://monoova-payments-for-woocommerce/external window [\"wp\",\"i18n\"]", "webpack://monoova-payments-for-woocommerce/webpack/bootstrap", "webpack://monoova-payments-for-woocommerce/webpack/runtime/compat get default export", "webpack://monoova-payments-for-woocommerce/webpack/runtime/define property getters", "webpack://monoova-payments-for-woocommerce/webpack/runtime/hasOwnProperty shorthand", "webpack://monoova-payments-for-woocommerce/webpack/runtime/make namespace object", "webpack://monoova-payments-for-woocommerce/./assets/js/src/blocks/monoova-payto-express-block.js"], "sourcesContent": ["import { useState, useEffect } from '@wordpress/element';\n\n/**\n * Navigation Countdown Component\n * \n * Shows a countdown timer and redirects to a URL when it reaches 0\n * \n * @param {Object} props\n * @param {number} props.initialSeconds - Starting countdown time in seconds\n * @param {string} props.redirectUrl - URL to redirect to when countdown reaches 0\n * @param {string} props.message - Message template with {countdown} placeholder\n */\nexport const NavigationCountdown = ({ \n    initialSeconds = 5, \n    redirectUrl, \n    message = 'Redirecting in {countdown} seconds...' \n}) => {\n    const [countdown, setCountdown] = useState(initialSeconds);\n\n    useEffect(() => {\n        if (countdown <= 0) {\n            if (redirectUrl) {\n                window.location.href = redirectUrl;\n            }\n            return;\n        }\n\n        const timer = setTimeout(() => {\n            setCountdown(prev => prev - 1);\n        }, 1000);\n\n        return () => clearTimeout(timer);\n    }, [countdown, redirectUrl]);\n\n    const formattedMessage = message.replace('{countdown}', countdown.toString());\n\n    return (\n        <div className=\"monoova-navigation-countdown\" style={{ \n            marginTop: '15px', \n            padding: '10px',\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #dee2e6',\n            borderRadius: '4px',\n            textAlign: 'center',\n            fontSize: '14px',\n            color: '#6c757d'\n        }}>\n            {formattedMessage}\n        </div>\n    );\n};\n", "module.exports = wc.wcBlocksData;", "module.exports = wc.wcBlocksRegistry;", "module.exports = wc.wcSettings;", "module.exports = window[\"wp\"][\"data\"];", "module.exports = window[\"wp\"][\"element\"];", "module.exports = window[\"wp\"][\"htmlEntities\"];", "module.exports = window[\"wp\"][\"i18n\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\n * External dependencies\n */\nimport { registerExpressPaymentMethod } from \"@woocommerce/blocks-registry\"\nimport { decodeEntities } from \"@wordpress/html-entities\"\nimport { getSetting } from \"@woocommerce/settings\"\nimport { __ } from \"@wordpress/i18n\"\nimport { createElement, useState, useCallback, useEffect } from \"@wordpress/element\"\nimport { useSelect } from \"@wordpress/data\"\nimport { CHECKOUT_STORE_KEY } from \"@woocommerce/block-data\"\nimport { NavigationCountdown } from \"./NavigationCountdown\"\n\n/**\n * Defensive check for WooCommerce availability\n */\nconsole.log(\"PayTo Express: Script loaded, checking dependencies\")\n\nif (typeof registerExpressPaymentMethod === \"undefined\") {\n    console.warn(\n        \"WooCommerce Blocks registerExpressPaymentMethod is not available. This may be due to edit mode or missing dependencies.\"\n    )\n} else if (!window.wp || !window.wp.element) {\n    console.error(\"WordPress element library is not available\")\n} else {\n    if (typeof createElement === \"undefined\" || typeof useState === \"undefined\" || typeof useCallback === \"undefined\") {\n        console.error(\"Required React hooks are not available\")\n\n        // Try fallback to window.wp.element\n        const wpElement = window.wp.element\n        if (wpElement && wpElement.createElement && wpElement.useState && wpElement.useCallback) {\n            console.log(\"PayTo Express: Using fallback wp.element\")\n            const wpCreateElement = wpElement.createElement\n            const wpUseState = wpElement.useState\n            const wpUseCallback = wpElement.useCallback\n            const wpUseEffect = wpElement.useEffect\n\n            // Proceed with main logic using fallback functions\n            initializePayToExpressCheckout(wpCreateElement, wpUseState, wpUseCallback, wpUseEffect)\n        } else {\n            console.error(\"Fallback wp.element also not available\")\n        }\n    } else {\n        console.log(\"PayTo Express: All dependencies available, proceeding with initialization\")\n        // Proceed with main logic using imported functions\n        initializePayToExpressCheckout()\n    }\n}\n\n/**\n * Initialize PayTo Express Checkout with provided React functions\n */\nfunction initializePayToExpressCheckout() {\n    console.log(\"PayTo Express: Starting initialization\")\n\n    // Try to get settings from WooCommerce settings registry first, then fallback to global variable\n    let settings = {}\n    try {\n        // Check if getSetting is available before using it\n        if (typeof getSetting !== \"undefined\") {\n            settings = getSetting(\"monoova_payto_express_data\", {})\n            console.log(\"PayTo Express: Got settings from getSetting:\", settings)\n        }\n    } catch (error) {\n        console.warn(\"getSetting failed, trying fallback:\", error)\n    }\n\n    // If settings are empty or getSetting failed, try window global fallback\n    if (!settings || Object.keys(settings).length === 0) {\n        settings = window.monoova_payto_express_blocks_params || {}\n        console.log(\"PayTo Express: Got settings from window global:\", settings)\n    }\n\n    // If still no settings, provide safe defaults\n    if (!settings || Object.keys(settings).length === 0) {\n        console.warn(\"PayTo Express settings not found, using defaults\")\n        settings = {\n            title: \"PayTo Express Checkout\",\n            description: \"Pay quickly and securely with your existing PayTo mandate.\",\n            is_available: false,\n            testmode: true,\n            ajax_url: \"/wp-admin/admin-ajax.php\",\n            check_mandate_nonce: \"\",\n            express_payment_nonce: \"\",\n            user_logged_in: false, // Add default\n            i18n: {\n                express_pay_button: \"Pay with PayTo\",\n                login_required: \"Please log in to use PayTo express checkout.\",\n                no_mandate: \"No active PayTo mandate found.\",\n                mandate_available: \"Use existing PayTo mandate\",\n                processing: \"Processing payment...\",\n                error: \"Payment failed. Please try again.\",\n            },\n        }\n    }\n\n    console.log(\"PayTo Express: Final settings:\", settings)\n\n    const defaultTitle = __(\"PayTo Express Checkout\", \"monoova-payments-for-woocommerce\")\n\n    /**\n     * PayTo Express Content Component\n     */\n    const PayToExpressContent = ({ onClose, onError, onSubmit, eventRegistration, emitResponse }) => {\n        const [isLoading, setIsLoading] = useState(false)\n        const [mandateData, setMandateData] = useState(null)\n        const [isLoggedIn, setIsLoggedIn] = useState(false)\n        const [shouldUseMandateCheck, setShouldUseMandateCheck] = useState(true)\n        const [mandateLimitExceeded, setMandateLimitExceeded] = useState(false)\n        const [showRedirect, setShowRedirect] = useState(false)\n\n        // Get order ID from WooCommerce checkout store\n        const { orderId } = useSelect(select => {\n            const store = select(CHECKOUT_STORE_KEY)\n            return {\n                orderId: store.getOrderId(),\n            }\n        })\n\n        // Check login status and mandate availability on mount\n        useEffect(() => {\n            checkLoginAndMandate()\n        }, [])\n\n        const checkLoginAndMandate = useCallback(async () => {\n            setIsLoading(true)\n\n            // First check if we're logged in according to PHP settings\n            const isUserLoggedIn = settings.user_logged_in\n\n            console.log(\"User logged in from settings:\", isUserLoggedIn)\n            console.log(\"Full settings:\", settings)\n\n            if (!isUserLoggedIn) {\n                setIsLoggedIn(false)\n                setMandateData(null)\n                setIsLoading(false)\n                return\n            }\n\n            // User is logged in, now check for mandate via AJAX\n            try {\n                const response = await fetch(settings.ajax_url, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/x-www-form-urlencoded\",\n                    },\n                    body: new URLSearchParams({\n                        action: settings.ajax_check_mandate_action || \"monoova_payto_check_mandate\",\n                        nonce: settings.check_mandate_nonce,\n                    }),\n                })\n\n                const result = await response.json()\n                console.log(\"Mandate check result:\", result)\n\n                if (result.success) {\n                    setIsLoggedIn(true)\n                    if (result.data && result.data.mandate) {\n                        setMandateData(result.data.mandate)\n                        setShouldUseMandateCheck(result.data.should_use_mandate !== false)\n                        setMandateLimitExceeded(result.data.mandate_limit_exceeded === true)\n\n                        console.log(\"Mandate found:\", result.data.mandate)\n                        console.log(\"Should use mandate:\", result.data.should_use_mandate)\n                        console.log(\"Mandate limit exceeded:\", result.data.mandate_limit_exceeded)\n\n                        // If mandate limit is exceeded, show redirect message\n                        if (result.data.mandate_limit_exceeded === true) {\n                            setShowRedirect(true)\n                        }\n                    } else {\n                        console.log(\"No mandate data in response\")\n                        setMandateData(null)\n                        setShouldUseMandateCheck(false)\n                        setMandateLimitExceeded(false)\n                    }\n                } else {\n                    console.log(\"Mandate check failed:\", result.data?.message || \"Unknown error\")\n                    // User is logged in but mandate check failed\n                    setIsLoggedIn(true)\n                    setMandateData(null)\n                    setShouldUseMandateCheck(false)\n                    setMandateLimitExceeded(false)\n                }\n            } catch (error) {\n                console.error(\"Error checking mandate:\", error)\n                // If AJAX fails, still show logged in state but no mandate\n                setIsLoggedIn(true)\n                setMandateData(null)\n                setShouldUseMandateCheck(false)\n                setMandateLimitExceeded(false)\n            }\n\n            setIsLoading(false)\n        }, [])\n\n        const handleExpressPayment = useCallback(\n            async event => {\n                console.log(\"PayTo Express: Button clicked, starting payment process\")\n\n                if (!mandateData) {\n                    onError(settings.i18n.no_mandate)\n                    return\n                }\n\n                console.log(\"PayTo Express: Starting payment process\", {\n                    mandateData,\n                    settings: {\n                        ajax_url: settings.ajax_url,\n                        express_payment_nonce: settings.express_payment_nonce,\n                    },\n                })\n\n                setIsLoading(true)\n\n                try {\n                    // For express checkout, we need to initiate the payment immediately\n                    // This will create an order from the cart and process payment with existing mandate\n                    const requestBody = new URLSearchParams({\n                        action: settings.ajax_express_payment_action || \"monoova_payto_express_payment\",\n                        nonce: settings.express_payment_nonce,\n                        payment_agreement_uid: mandateData.payment_agreement_uid,\n                    })\n\n                    console.log(\"PayTo Express: Sending request\", {\n                        url: settings.ajax_url,\n                        body: requestBody.toString(),\n                    })\n\n                    const response = await fetch(settings.ajax_url, {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/x-www-form-urlencoded\",\n                        },\n                        body: requestBody,\n                    })\n\n                    const result = await response.json()\n                    console.log(\"PayTo Express: Response received\", result)\n\n                    if (result.success) {\n                        // Express checkout successful - redirect to order received page\n                        const redirectUrl = result.data.order_received_url || result.data.redirect_url\n                        console.log(\"PayTo Express: Redirecting to\", redirectUrl)\n                        if (redirectUrl) {\n                            window.location.href = redirectUrl\n                        } else {\n                            // Fallback: close express checkout and show success\n                            console.log(\"PayTo Express: No redirect URL, closing modal\")\n                            onClose()\n                        }\n                    } else {\n                        console.error(\"PayTo Express: Payment failed\", result.data)\n                        const errorMessage = result.data?.message || settings.i18n.error\n                        onError(errorMessage)\n                    }\n                } catch (error) {\n                    console.error(\"PayTo Express: Network error\", error)\n                    onError(settings.i18n.error)\n                }\n\n                setIsLoading(false)\n            },\n            [mandateData, onSubmit, onError, emitResponse, orderId]\n        )\n\n        // If loading, show loading state\n        if (isLoading) {\n            return createElement(\n                \"div\",\n                {\n                    className: \"monoova-payto-express-loading\",\n                    style: { padding: \"20px\", textAlign: \"center\" },\n                },\n                settings.i18n.processing\n            )\n        }\n\n        // If not logged in, show login message\n        if (!isLoggedIn) {\n            return createElement(\n                \"div\",\n                {\n                    className: \"monoova-payto-express-login-required\",\n                    style: { padding: \"20px\", textAlign: \"center\", color: \"#666\" },\n                },\n                settings.i18n.login_required\n            )\n        }\n\n        // If no mandate available, show message\n        if (!mandateData) {\n            return createElement(\n                \"div\",\n                {\n                    className: \"monoova-payto-express-no-mandate\",\n                    style: { padding: \"20px\", textAlign: \"center\", color: \"#666\" },\n                },\n                settings.i18n.no_mandate\n            )\n        }\n\n        // If mandate limit is exceeded, show redirect message with countdown\n        if (mandateLimitExceeded || showRedirect) {\n            const checkoutUrl = window.location.origin + \"/checkout/\"\n\n            return createElement(\n                \"div\",\n                {\n                    className: \"monoova-payto-express-limit-exceeded\",\n                    style: {\n                        padding: \"20px\",\n                        textAlign: \"center\",\n                        backgroundColor: \"#fff3cd\",\n                        border: \"1px solid #ffeaa7\",\n                        borderRadius: \"5px\",\n                        color: \"#856404\",\n                    },\n                },\n                [\n                    createElement(\n                        \"div\",\n                        {\n                            key: \"message\",\n                            style: { marginBottom: \"15px\", fontSize: \"14px\" },\n                        },\n                        \"Your current payment agreement has reached its limits. We will redirect you to the checkout page so that you can create another one.\"\n                    ),\n                    createElement(NavigationCountdown, {\n                        key: \"countdown\",\n                        initialSeconds: 5,\n                        redirectUrl: checkoutUrl,\n                        message: \"Redirecting to checkout in {countdown} seconds...\",\n                    }),\n                ]\n            )\n        }\n\n        // If mandate should not be used (but not limit exceeded), don't show express checkout\n        if (!shouldUseMandateCheck) {\n            return createElement(\n                \"div\",\n                {\n                    className: \"monoova-payto-express-mandate-invalid\",\n                    style: { padding: \"20px\", textAlign: \"center\", color: \"#666\" },\n                },\n                \"Your payment agreement cannot be used for this order. Please proceed to checkout.\"\n            )\n        }\n\n        // Show express checkout option\n        return createElement(\n            \"div\",\n            {\n                className: \"monoova-payto-express-checkout\",\n            },\n            [\n                createElement(\n                    \"div\",\n                    {\n                        key: \"mandate-info\",\n                        className: \"mandate-info\",\n                        style: {\n                            marginBottom: \"15px\",\n                            padding: \"10px\",\n                            backgroundColor: \"#f0f8ff\",\n                            border: \"1px solid #0073aa\",\n                            borderRadius: \"5px\",\n                        },\n                    },\n                    [\n                        createElement(\n                            \"h4\",\n                            {\n                                key: \"title\",\n                                style: { margin: \"0 0 10px 0\", color: \"#0073aa\" },\n                            },\n                            settings.i18n.mandate_available\n                        ),\n                        createElement(\n                            \"p\",\n                            {\n                                key: \"details\",\n                                style: { margin: \"0\", fontSize: \"14px\" },\n                            },\n                            [\n                                createElement(\n                                    \"strong\",\n                                    { key: \"label\" },\n                                    __(\"Maximum Amount: \", \"monoova-payments-for-woocommerce\")\n                                ),\n                                `$${mandateData.maximum_amount || \"0.00\"}`,\n                            ]\n                        ),\n                    ]\n                ),\n                createElement(\n                    \"button\",\n                    {\n                        key: \"express-button\",\n                        type: \"button\",\n                        className:\n                            \"wp-element-button wc-block-components-checkout-return-to-cart-button monoova-payto-express-btn\",\n                        style: {\n                            width: \"100%\",\n                            padding: \"12px\",\n                            backgroundColor: settings.button_style?.color === \"primary\" ? \"#0073aa\" : \"#666666\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: `${settings.button_style?.borderRadius || 5}px`,\n                            fontSize: \"16px\",\n                            fontWeight: \"bold\",\n                            cursor: isLoading ? \"not-allowed\" : \"pointer\",\n                            opacity: isLoading ? 0.6 : 1,\n                            height: `${settings.button_style?.height || 48}px`,\n                        },\n                        onClick: handleExpressPayment,\n                        disabled: isLoading,\n                    },\n                    isLoading ? settings.i18n.processing : settings.i18n.express_pay_button\n                ),\n            ]\n        )\n    }\n\n    /**\n     * Can make payment check\n     * Only show PayTo express checkout on cart page, hide on checkout page\n     */\n    const canMakePayment = ({\n        cart,\n        cartTotals,\n        cartNeedsShipping,\n        shippingAddress,\n        billingAddress,\n        selectedShippingMethods,\n        paymentRequirements,\n    }) => {\n        // Check if we're on the checkout page\n        const isCheckoutPage =\n            document.body.classList.contains(\"woocommerce-checkout\") ||\n            window.location.pathname.includes(\"/checkout/\") ||\n            document.querySelector(\".woocommerce-checkout\") !== null ||\n            document.querySelector(\".wp-block-woocommerce-checkout\") !== null\n\n        // Check if we're on the cart page\n        const isCartPage =\n            document.body.classList.contains(\"woocommerce-cart\") ||\n            window.location.pathname.includes(\"/cart/\") ||\n            document.querySelector(\".woocommerce-cart\") !== null ||\n            document.querySelector(\".wp-block-woocommerce-cart\") !== null\n\n        // Additional check: ensure PayTo is available and user is logged in\n        const isPayToAvailable = settings.is_available !== false\n        const isUserLoggedIn = settings.user_logged_in\n\n        console.log(\"PayTo Express: canMakePayment check\", {\n            isCheckoutPage,\n            isCartPage,\n            isPayToAvailable,\n            isUserLoggedIn,\n            settings,\n            cart,\n            cartTotals,\n            cartNeedsShipping,\n            shippingAddress,\n            billingAddress,\n            selectedShippingMethods,\n            paymentRequirements,\n        })\n\n        // Only show express checkout on cart page, not on checkout page, and only if PayTo is available\n        const canShow = isCartPage && !isCheckoutPage && isPayToAvailable && isUserLoggedIn\n        return canShow\n    }\n\n    /**\n     * Express Payment Configuration\n     */\n    const expressPaymentConfig = {\n        name: \"monoova_payto_express\",\n        label: createElement(\"span\", {}, [\n            // Add logo if available\n            settings.logo_url &&\n                createElement(\"img\", {\n                    key: \"logo\",\n                    src: settings.logo_url,\n                    alt: \"PayTo\",\n                    style: { height: \"20px\", marginRight: \"8px\", verticalAlign: \"middle\" },\n                }),\n            createElement(\"span\", { key: \"title\" }, settings.title || defaultTitle),\n        ]),\n        content: createElement(PayToExpressContent),\n        edit: createElement(\"div\", {}, __(\"PayTo Express Checkout\", \"monoova-payments-for-woocommerce\")),\n        canMakePayment: canMakePayment,\n        paymentMethodId: \"monoova_payto_express\", // Use unique ID for express checkout\n        supports: {\n            features: [\"products\"],\n        },\n    }\n\n    // Register the express payment method\n    try {\n        console.log(\"PayTo Express: Attempting to register with config:\", expressPaymentConfig)\n\n        // Check if another express payment method with same name exists\n        console.log(\"PayTo Express: Checking existing registrations...\")\n\n        registerExpressPaymentMethod(expressPaymentConfig)\n        console.log(\"PayTo Express Checkout registered successfully\")\n\n        // Verify registration worked by checking if method is available\n        setTimeout(() => {\n            const expressCheckoutArea = document.querySelector(\".wc-block-components-express-payment\")\n            console.log(\"PayTo Express: Express checkout area found:\", expressCheckoutArea)\n        }, 2000)\n    } catch (error) {\n        console.error(\"Failed to register PayTo Express Checkout:\", error)\n    }\n}\n\nexport default initializePayToExpressCheckout\n"], "names": ["useState", "useEffect", "NavigationCountdown", "initialSeconds", "redirectUrl", "message", "countdown", "setCountdown", "window", "location", "href", "timer", "setTimeout", "prev", "clearTimeout", "formattedMessage", "replace", "toString", "React", "createElement", "className", "style", "marginTop", "padding", "backgroundColor", "border", "borderRadius", "textAlign", "fontSize", "color", "registerExpressPaymentMethod", "decodeEntities", "getSetting", "__", "useCallback", "useSelect", "CHECKOUT_STORE_KEY", "console", "log", "warn", "wp", "element", "error", "wpElement", "wpCreateElement", "wpUseState", "wpUseCallback", "wpUseEffect", "initializePayToExpressCheckout", "settings", "Object", "keys", "length", "monoova_payto_express_blocks_params", "title", "description", "is_available", "testmode", "ajax_url", "check_mandate_nonce", "express_payment_nonce", "user_logged_in", "i18n", "express_pay_button", "login_required", "no_mandate", "mandate_available", "processing", "defaultTitle", "PayToExpressContent", "onClose", "onError", "onSubmit", "eventRegistration", "emitResponse", "isLoading", "setIsLoading", "mandateData", "setMandateData", "isLoggedIn", "setIsLoggedIn", "shouldUseMandateCheck", "setShouldUseMandateCheck", "mandateLimitExceeded", "setMandateLimitExceeded", "showRedirect", "setShowRedirect", "orderId", "select", "store", "getOrderId", "checkLoginAndMandate", "isUserLoggedIn", "response", "fetch", "method", "headers", "body", "URLSearchParams", "action", "ajax_check_mandate_action", "nonce", "result", "json", "success", "data", "mandate", "should_use_mandate", "mandate_limit_exceeded", "handleExpressPayment", "event", "requestBody", "ajax_express_payment_action", "payment_agreement_uid", "url", "order_received_url", "redirect_url", "errorMessage", "checkoutUrl", "origin", "key", "marginBottom", "margin", "maximum_amount", "type", "width", "button_style", "fontWeight", "cursor", "opacity", "height", "onClick", "disabled", "canMakePayment", "cart", "cartTotals", "cartNeedsShipping", "shippingAddress", "billing<PERSON><PERSON>ress", "selectedShippingMethods", "paymentRequirements", "isCheckoutPage", "document", "classList", "contains", "pathname", "includes", "querySelector", "isCartPage", "isPayToAvailable", "canShow", "expressPaymentConfig", "name", "label", "logo_url", "src", "alt", "marginRight", "verticalAlign", "content", "edit", "paymentMethodId", "supports", "features", "expressCheckoutArea"], "sourceRoot": ""}