!function(e){"use strict";let o=null,t=!1;if("undefined"==typeof primerRedirectData)return console.error("Primer Redirect Data not found. AJAX URL or Order ID is missing."),void u("Critical error: Payment page configuration is missing. Please contact support.");const{ajax_url:n,order_id:r,client_token_nonce:i,complete_payment_nonce:a,i18n:s,gateway_settings:c,checkout_url:l}=primerRedirectData;function u(o){const t=e("#error-box");t.length?t.addClass("active").html(`<span class="ErrorText">${o}</span>`):alert(o),e.unblockUI&&e.unblockUI(),e("#checkout-container").hide()}e((async function(){if(e("#loading-payment-message").hide(),r)if(i)try{const d=await async function(){try{const o=await e.ajax({url:n,type:"POST",data:{action:"monoova_get_client_token",order_id:r,_wpnonce:i}});if(o.success&&o.data&&o.data.clientToken)return o.data;throw new Error(o.data&&o.data.message?o.data.message:s.error_fetching_token||"Could not retrieve payment session.")}catch(e){console.error("Error fetching client token:",e);let o=s.error_fetching_token||"Could not retrieve payment session.";throw e.responseJSON&&e.responseJSON.data&&e.responseJSON.data.message?o=e.responseJSON.data.message:e.message&&(o=e.message),u(o),setTimeout((()=>{window.location.href=l||"/"}),3e3),e}}();d&&d.clientToken&&async function(i){if(t)return;if(t=!0,!i||!i.clientToken)return u(s.error_invalid_token||"Invalid payment session token provided."),void(t=!1);if(o){try{await o.destroy()}catch(e){console.warn("Error destroying existing Primer instance:",e)}o=null}const l={container:"#checkout-container",clientSessionCachingEnabled:!1,style:{inputLabel:{fontFamily:c?.checkout_ui_styles?.input_label?.font_family||"Helvetica, Arial, sans-serif",fontSize:c?.checkout_ui_styles?.input_label?.font_size||"14px",fontWeight:c?.checkout_ui_styles?.input_label?.font_weight||"normal",color:c?.checkout_ui_styles?.input_label?.color||"#000000"},input:{base:{fontFamily:c?.checkout_ui_styles?.input?.font_family||"Helvetica, Arial, sans-serif",fontSize:c?.checkout_ui_styles?.input?.font_size||"14px",fontWeight:c?.checkout_ui_styles?.input?.font_weight||"normal",background:c?.checkout_ui_styles?.input?.background_color||"#FAFAFA",borderColor:c?.checkout_ui_styles?.input?.border_color||"#E8E8E8",borderRadius:c?.checkout_ui_styles?.input?.border_radius||"8px",color:c?.checkout_ui_styles?.input?.text_color||"#000000"}},submitButton:{base:{color:c?.checkout_ui_styles?.submit_button?.text_color||"#000000",background:c?.checkout_ui_styles?.submit_button?.background||"#2ab5c4",borderRadius:c?.checkout_ui_styles?.submit_button?.border_radius||"10px",borderColor:c?.checkout_ui_styles?.submit_button?.border_color||"#2ab5c4",fontFamily:c?.checkout_ui_styles?.submit_button?.font_family||"Helvetica, Arial, sans-serif",fontSize:c?.checkout_ui_styles?.submit_button?.font_size||"17px",fontWeight:c?.checkout_ui_styles?.submit_button?.font_weight||"bold",boxShadow:"none"},disabled:{color:"#9b9b9b",background:"#e1deda"}},loadingScreen:{color:c?.checkout_ui_styles?.submit_button?.background||"#2ab5c4"}},errorMessage:{disabled:!0,onErrorMessageShow(e){u(e)},onErrorMessageHide(){const o=e("#error-box");o.length&&o.removeClass("active").empty()}},successScreen:!1,onCheckoutComplete:async o=>{const t=o?.payment?.id;try{const o=await e.ajax({url:n,type:"POST",data:{action:"monoova_complete_checkout",order_id:r,primer_payment_id:t,client_transaction_ref:i.clientTransactionUniqueReference,_wpnonce:a}});if(!(o.success&&o.data&&o.data.redirect_url))throw new Error(o.data&&o.data.message?o.data.message:s.error_finalizing_order||"Could not finalize your order.");window.location.href=o.data.redirect_url}catch(e){console.error("Error completing payment with backend:",e);let o=s.error_finalizing_order||"Could not finalize your order.";e.responseJSON&&e.responseJSON.data&&e.responseJSON.data.message?o=e.responseJSON.data.message:e.message&&(o=e.message),u(o)}},onCheckoutFail:(e,{payment:o},n)=>{console.error("Primer onCheckoutFail:",e,o);let r=s.payment_failed_try_again||"Your payment could not be processed. Please try again later.";e&&e.message?r=e.message:o&&o.processor&&o.processor.message?r=o.processor.message:"string"==typeof e&&(r=e),u(r),t=!1},onBeforeCheckoutStart:()=>console.log("Primer: onBeforeCheckoutStart"),onCheckoutStart:()=>console.log("Primer: onCheckoutStart"),onCheckoutCancel:()=>{console.log("Primer: onCheckoutCancel"),u(s.checkout_cancelled||"Checkout was cancelled."),t=!1},onPaymentMethodShow:()=>console.log("Primer: onPaymentMethodShow"),onPaymentMethodHide:()=>console.log("Primer: onPaymentMethodHide"),onAuthorizationSuccess:e=>console.log("Primer: onAuthorizationSuccess",e),onAuthorizationFailed:e=>{console.log("Primer: onAuthorizationFailed",e),u(s.auth_failed||"Payment authorization failed.")},onPaymentComplete:e=>console.log("Primer: onPaymentComplete (different from onCheckoutComplete)",e),onPaymentFailed:e=>{console.log("Primer: onPaymentFailed",e),u(s.payment_failed||"Payment failed.")}};try{e("#checkout-container").show(),o=await Primer.showUniversalCheckout(i.clientToken,l)}catch(e){console.error("Error initializing Primer Universal Checkout:",e),u((s.error_init_payment_form||"Failed to initialize payment form: ")+(e.message||""))}t=!1}(d)}catch(e){console.error("Failed to initialize payment process:",e)}else u(s.error_missing_nonce||"Security token is missing. Cannot proceed.");else u(s.error_missing_order_id||"Order ID is missing. Cannot proceed with payment.")}))}(jQuery);